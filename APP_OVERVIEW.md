# iThinc Welfare Management System

## Overview
The iThinc Welfare Management System is a comprehensive web application designed to manage welfare services at festivals and events. It provides tools for managing admissions, shift scheduling, inventory tracking, and lost property management.

## Project Structure

```
/
├── public/                      # Static assets
│   ├── _redirects              # Netlify redirect rules
│   ├── favicon.svg             # Site favicon
│   ├── index.html              # Main HTML file
│   ├── ithink-logo.svg         # Brand logo
│   ├── manifest.json           # PWA manifest
│   └── robots.txt              # Search engine rules
│
├── src/                        # Source code
│   ├── __tests__/             # Test files
│   │   ├── admissions.test.tsx # Admission component tests
│   │   └── database.test.ts    # Database service tests
│   │
│   ├── assets/                 # Project assets
│   │   └── ithink-logo.svg     # Brand logo for components
│   │
│   ├── components/             # React components
│   │   ├── reports/            # Reporting components
│   │   │   ├── admissions/     # Admission report components
│   │   │   ├── frontOfHouse/   # Front of house report components
│   │   │   ├── lostProperty/   # Lost property report components
│   │   │   ├── sensoryHub/     # Sensory hub report components
│   │   │   └── shared/         # Shared report components
│   │   │
│   │   ├── admission/          # Admission form components
│   │   │   ├── AdmissionModals.tsx    # Modal dialogs
│   │   │   ├── AdmissionNotesSection.tsx # Notes section
│   │   │   ├── LocationSection.tsx    # Location management
│   │   │   ├── PersonalInfoSection.tsx # Personal details
│   │   │   ├── PhysicalDescriptionSection.tsx # Physical description
│   │   │   ├── ReferralSection.tsx    # Referral information
│   │   │   ├── SafeguardingSection.tsx # Safeguarding concerns
│   │   │   ├── StyledComponents.tsx   # Shared styles
│   │   │   └── SubstanceUseSection.tsx # Substance use info
│   │   │
│   │   ├── sensory-hub/       # Sensory hub components
│   │   │   └── SensoryHubVisitForm.tsx # Visit logging form
│   │   │
│   │   ├── shifts/            # Shift management components
│   │   │   ├── ShiftConfigForm.tsx    # Shift pattern configuration
│   │   │   ├── ShiftScheduleTable.tsx # Shift schedule display/edit
│   │   │   └── TeamLeaderForm.tsx     # Team leader management
│   │   │
│   │   ├── festival/          # Festival management components
│   │   │   ├── AdminPanelSection.tsx  # Admin controls
│   │   │   ├── FestivalForm.tsx       # Festival creation/editing
│   │   │   ├── FestivalHeader.tsx     # Festival info display
│   │   │   ├── FestivalLinks.tsx      # Festival URL management
│   │   │   └── FestivalList.tsx       # Festival listing
│   │   │
│   │   ├── AdminPanel.tsx     # Admin control panel
│   │   ├── AdmissionForm.tsx  # Main admission form container
│   │   ├── AdmissionList.tsx  # Admission listing
│   │   ├── Dashboard.tsx      # Main dashboard
│   │   ├── ErrorBoundary.tsx  # Error handling
│   │   ├── FestivalSelector.tsx # Festival selection
│   │   ├── LostPropertyPage.tsx # Lost property management
│   │   ├── ShiftNotes.tsx     # Shift notes management
│   │   └── Sidebar.tsx        # Navigation sidebar
│   │
│   ├── config/                # Configuration files
│   │   ├── secrets.ts         # API keys and credentials
│   │   └── secrets.example.ts # Template for secrets
│   │
│   ├── contexts/              # React contexts
│   │   ├── FestivalContext.tsx # Festival state management
│   │   └── SiteLocationContext.tsx # Site location management
│   │
│   ├── hooks/                 # Custom React hooks
│   │   ├── useAdmissions.ts   # Admission data management
│   │   ├── useAdmissionForm.ts # Admission form logic
│   │   └── useReportData.ts   # Report data handling
│   │
│   ├── pages/                 # Page components
│   │   ├── AdmissionsPage.tsx     # Admissions listing
│   │   ├── DashboardPage.tsx      # Main dashboard
│   │   ├── FestivalManagementPage.tsx # Festival management
│   │   ├── FrontOfHousePage.tsx   # Front desk operations
│   │   ├── NewAdmissionPage.tsx   # New admission entry
│   │   ├── ReportsPage.tsx        # Reports and analytics
│   │   ├── SensoryHubPage.tsx     # Sensory hub visitor tracking
│   │   └── ShiftsPage.tsx         # Shift management
│   │
│   ├── services/              # Service layer
│   │   └── database/          # Database services
│   │       ├── admission-manager.ts    # Admission operations
│   │       ├── festival-manager.ts     # Festival operations
│   │       ├── item-manager.ts         # Inventory operations
│   │       ├── lost-property-manager.ts # Lost property operations
│   │       ├── sensory-hub-manager.ts  # Sensory hub operations
│   │       ├── shift-manager.ts        # Shift operations
│   │       ├── sync-manager.ts         # Database sync
│   │       └── index.ts                # Service exports
│   │
│   ├── types/                 # TypeScript type definitions
│   │   ├── admission.ts       # Admission types
│   │   ├── base.ts           # Common types
│   │   ├── festival.ts       # Festival types
│   │   ├── item.ts           # Inventory types
│   │   ├── sensory-hub.ts    # Sensory hub types
│   │   ├── shift.ts          # Shift types
│   │   └── index.ts          # Type exports
│   │
│   ├── utils/                 # Utility functions
│   │   ├── reportDataProcessing.ts # Report data handling
│   │   ├── validation.ts     # Form validation
│   │   └── version.ts        # Version management
│   │
│   ├── App.tsx               # Main application component
│   └── index.tsx             # Application entry point
│
├── rsbuild.config.ts          # Rsbuild configuration
├── nginx.conf                 # Nginx server configuration
├── package.json              # Project dependencies
├── supervisord.conf          # Process management
└── tsconfig.json             # TypeScript configuration
```

## Core Features

### Festival Management
- Create and manage multiple festivals/events
- Track festival dates, locations, and important URLs
- Maintain festival-specific notes and information
- Toggle active status for current events
- Multi-site support with Arena and Campsite locations:
  - Site-specific data separation
  - Easy site switching from festival header
  - Site location persistence across sessions
  - Site-specific inventory and admission tracking

### Welfare Admissions
- Record and track welfare admissions with detailed information
- Modular form structure with specialized sections:
  - Personal Information (with simplified ethnicity options and optional surname)
  - Location Management
  - Physical Description
  - Substance Use (with optimized substance order)
  - Safeguarding Concerns
  - Referral Information
  - Admission Notes
- Custom hooks for form logic and state management
- Separate modal components for actions like discharge and re-admission
- Track substance use, mental health, and safeguarding concerns
- Manage bay/chair assignments and occupancy
- Maintain admission history and additional notes
- Enhanced search and filtering capabilities:
  - Quick search functionality
  - Column-specific filtering
  - Advanced sorting options
  - Proper data type handling for dates and numbers
- Required discharge notes with configurable discharge time

### Shift Management
- Configure shift patterns with customizable parameters
- Manage team leaders and their assigned teams
- Generate and maintain shift schedules
- Support for multiple shifts per day
- Automatic shift rotation pattern (A -> B -> C -> D -> E)
- Track team members and shift notes
- Site-specific shift assignments and notes

### Inventory Management
- Track essential items:
  - Medical supplies (Sanitizer, ToiletRoll)
  - Weather protection (Suncream, Poncho)
  - Safety items (Earplugs, Condoms)
  - Wristbands (Children's and General)
  - Other supplies (Water, Charging facilities)
- Monitor stock levels and usage
- Site-specific inventory tracking
- Long-press functionality for entering specific quantities:
  - Optimized for iPad and touch devices
  - Maintains backward compatibility with single-click increments
  - Improves efficiency for distributing multiple items at once

### Lost Property
- Record found items with detailed descriptions
- Categorize items (Phone, Passport, Keys, etc.)
- Track item status (claimed/unclaimed)
- Record item return information
- Support for item photos/attachments
- Enhanced search and filtering:
  - Quick search functionality
  - Advanced filtering options
  - Improved sorting capabilities
- Ability to reverse returned item status with "Unmark as Returned" functionality

### Sensory Hub Visitor Tracking
- Comprehensive visitor tracking system for sensory hub services
- Record visitor interactions with detailed categorization:
  - Visit purpose tracking (looking around vs. using services)
  - User type classification (crew vs. public visitors)
  - Team name tracking for crew visits
  - Timestamp-based visit logging
- Site-specific tracking for multi-location festivals
- Real-time visit data management and display
- Integration with existing festival and location management systems

### Reporting
- Generate comprehensive reports for:
  - Admissions
  - Front of House activities
  - Lost Property
  - Sensory Hub visitor tracking
- Filter reports by date ranges
- View data in both chart and table formats
- Site-specific reporting capabilities
- Interactive elements:
  - Clickable bar charts showing patients admitted during specific time periods
  - Clickable table rows to view detailed record information
  - Advanced search and filtering options
- PDF export functionality for all report types

### Feedback System
- User feedback collection across all pages via feedback button
- Admin panel for feedback management
- Optimized database performance with indexed queries
- Feedback categorization and tracking
- Rating system for user experiences

## Technical Architecture

### Version Management
- Version information is maintained in three locations:
  1. package.json: Primary source of version number
  2. CHANGELOG.md: Documents changes for each version
  3. Sidebar display: Automatically pulls version from package.json via version.ts
- When updating versions:
  1. Update version in package.json
  2. Add new version section in CHANGELOG.md with changes
  3. Sidebar version will update automatically
- Version format: MAJOR.MINOR.PATCH (e.g., 1.0.3)
  - MAJOR: Breaking changes
  - MINOR: New features
  - PATCH: Bug fixes and minor improvements

### Database
- **SimplifiedDatabaseService**: Direct PouchDB access with immediate initialization for optimal performance
- **iPad-Optimized Configuration**: Conservative settings designed for resource-constrained devices
  - 8-second timeouts for slower network conditions
  - Batch size of 3 items for memory efficiency
  - Minimal revision limits (5 revisions) for memory savings
- **CouchDB Integration**: Remote storage with enhanced reliability and conflict resolution
- **Cloudflare Workers Proxy**: Secure database access with authentication and CORS support
  - Handles authentication and authorization
  - Provides CORS support and security layer
  - Manages database connections and rate limiting
  - Improves cold start performance
- **Soft Deletion Infrastructure**: Comprehensive tombstone record system for data integrity and iPad compatibility
  - **Tombstone Records**: Deleted records are marked with `isDeleted: true` and `deletedAt` timestamp instead of immediate removal
  - **Database Index Integration**: All database indexes include `isDeleted` field for efficient filtering of active vs deleted records
  - **Automatic Query Filtering**: All database queries automatically exclude deleted records unless explicitly requested with `includeDeleted` parameter
  - **Cascading Soft Deletion**: Festival deletion triggers soft deletion of all associated admissions, items, shifts, and notes
  - **Two-Tier Cleanup Policy**: Soft deleted records are automatically hard deleted after 6 months to manage storage
  - **iPad Cache Persistence Resolution**: Eliminates critical issue where deleted records would reappear on iPads due to cache synchronization conflicts
  - **Enhanced Data Consistency**: Provides consistent view of deleted vs active records across all devices and sync operations
- **Background Sync Strategy**: Non-blocking sync operations that never interfere with data access
  - Local-first operations for immediate responsiveness
  - Background sync triggers after data changes
  - Sync failures don't block data access
  - Progressive loading approach with immediate data availability
- **Enhanced Reliability**: Improved error handling and graceful degradation
  - Better handling of network issues and connection failures
  - Optimized for older iPad devices and slow networks
  - Memory-efficient operations with proper cleanup
  - Offline-first capability with local data persistence

### Data Models
- Festivals: Track event details and status
- Admissions: Record welfare cases and patient information
- Shifts: Manage staff scheduling and assignments
- Inventory: Track supplies and equipment
- Lost Property: Manage found items
- Feedback: User feedback and suggestions
- Sensory Hub Visits: Track visitor interactions and service usage

### Security
- IP-based access control
- Cloudflare Access email-based authentication
- Role-based access control with user roles (Admin, Partner, User, Public)
- Comprehensive Access Management system
- Database-backed access control with role hierarchy
- Basic authentication for database access
- Secure data synchronization

### User Interface
- React-based frontend
- Material UI components and styling system
- Modular component architecture
- Custom hooks for logic separation
- Responsive design for various devices
- Consistent theming and component design
- Enhanced accessibility features
- Improved loading states and error handling
- Advanced data grid functionality with comprehensive search and filtering
- Integrated User Guide with tabbed sections, including a comprehensive FAQ
- **Enhanced Sync Status Indicator** - Always-visible status indicator in sidebar showing real-time sync state:
  - Real-time connection monitoring with color-coded status (synced, syncing, initial sync, offline, error, auth error)
  - Initial sync detection for empty databases with appropriate loading indicators
  - Manual sync capability via click interaction
  - Responsive design adapting to sidebar state
  - Improved user experience with accurate sync state representation
  - Seamless integration with existing sync architecture
- Code splitting for improved performance:
  - Lazy-loaded components for faster initial load
  - Suspense with fallback loading indicators
  - Reduced bundle sizes for better user experience

## Deployment
- Docker containerization
- Nginx web server configuration
- Environment-based configuration
- Automated build process

## Data Management
- **Comprehensive Database Cleanup System** - Automated cleanup functionality with soft deletion awareness to maintain database performance
  - **Graduated Cleanup Policy**: Two-tier approach with soft deletion followed by hard deletion after 6 months
  - **Admission Cleanup**: Discharged admissions older than 3 months are soft deleted, hard deleted after 6 months
  - **Feedback Cleanup**: Resolved feedback older than 3 months is soft deleted, hard deleted after 6 months
  - **Lost Property Cleanup**: Claimed items older than 3 months are soft deleted, hard deleted after 6 months
  - **Item Aggregation Cleanup**: Old item counts are soft deleted, then hard deleted following the same policy
  - **Safe Cleanup Operations**: Comprehensive validation prevents accidental deletion of active records
  - **Performance Optimization**: Regular maintenance with soft deletion awareness maintains optimal query performance
- **Complete Database Export System** - Full database export capabilities for backup and analysis
  - JSON export format with complete metadata and relationships
  - CSV export format optimized for spreadsheet analysis
  - Comprehensive data coverage across all system data types
  - Export metadata including timestamps and statistics
  - Enhanced database managers with consistent export interfaces
- **Admin Database Operations Panel** - Integrated admin interface for database management
  - One-click database cleanup operations with soft deletion support
  - Easy access to database export functionality
  - Seamlessly integrated with Festival Management page
  - User-friendly operation feedback and status indicators
- Regular data synchronization with improved reliability
- Backup and recovery support
- Enhanced conflict resolution

## Knowledge Base
- Organized repository of external resources for staff reference
- Category and subcategory organization
- Initial sections: Substance Info, Mental Health, and Support Contacts
- Custom categories can be created as needed
- Phone number and URL support for resources
- Cross-festival resource sharing with visual indicators
- Tile-based dashboard layout with improved usability
- Quick descriptions for each resource
- Easy access to external sites with one click

## Universal Festival Selection
- "Active Festival" button always visible in the sidebar
- Modal dialog for selecting festivals without requiring access to festival management
- Browser-specific festival selection for multi-user environments
- Maintained site location selection for multi-site festivals

## Future Enhancements
- Advanced analytics with custom report generation
- Mobile-optimized interface with touch-friendly controls
- External system integration and API development
- Multi-language support and localization
