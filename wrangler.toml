name = "ithink-welfare"
compatibility_date = "2024-01-31"
main = "workers-site/index.js"

[site]
bucket = "./dist"

# Routes are now defined at the top level
routes = [
  { pattern = "welfare.brisflix.com/*", custom_domain = true },
  { pattern = "ithink-welfare.brisflix.workers.dev/*" }
]

[env.production]
vars.ITHINC_DB_REMOTE_URL = "https://database-proxy.brisflix.workers.dev"
vars.ITHINC_DB_LOCAL_NAME = "ithinc_welfare"
vars.ITHINC_DB_REMOTE_NAME = "ithinc_welfare"
vars.ITHINC_DB_USERNAME = "ithinc"

# Secrets that should be set via wrangler secret commands:
# - ITHINC_DB_PASSWORD
# - ITHINC_DB_CF_CLIENT_ID
# - ITHINC_DB_CF_CLIENT_SECRET

# wrangler.toml (wrangler v3.88.0^)
[observability.logs]
enabled = true



