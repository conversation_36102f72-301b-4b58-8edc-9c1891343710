Subject: iThinc Welfare System Testing - Your Input Needed

Hi there,

We're ready to begin testing the new iThinc Welfare System, and given your experience with welfare operations, we'd really value your feedback on this modern take on welfare management.

What's Different:
- Works offline with automatic sync when connection returns
- Simplified admission process with smart bay management
- Real-time occupancy tracking
- Integrated lost property
- Smart inventory tracking for welfare supplies

Please note: The first sync will take ages, it takes some time for the databases to sort themselves out, it should not take more than a few minutes and sunsequent syncs are much faster. 

The system is built specifically for festival welfare, so you'll find it familiar but with some quality-of-life improvements we think you'll appreciate.

Getting Started:
1. Click the access link (coming separately)
2. Use your email to authenticate through Cloudflare (login lasts a month)
3. The interface should feel intuitive, but there's a detailed guide if needed

We'd love your feedback on the admission flow, inventory tracking, and reports. The feedback button is always in the bottom-right corner - don't be shy about using it! It should also work offline, so please do some tests in airplane mode. It should hold the data internally and then sync on the next page load. 

Thanks for helping us test this. Your experience with previous systems makes your input particularly valuable.

Best regards,
Sam

P.S. Test data is automatically cleaned up, so feel free to experiment fully.