# iThink Welfare System User Guide

## Getting Started

### Logging In
To access the iThink Welfare system, you'll need to:
1. Navigate to the system URL provided by your administrator
2. You'll be presented with a Cloudflare login page
3. Enter your email address when prompted
4. Complete the authentication process
   - Your login session will remain active for one month
   - After one month, you'll need to authenticate again

**Note:** This secure login process ensures that only authorized personnel can access the system. Access to specific features is controlled by your assigned role (Admin, Partner, User, or Public).

### Selecting a Festival
[Screenshot: Active Festival selector in sidebar]

The system allows you to work with multiple festivals:
1. Click on "Active Festival" in the sidebar
2. A dialog will appear showing all available festivals
3. Select the festival you want to work with
4. The system will update to show data for the selected festival

**Note:** Your festival selection is saved in your browser and will persist between sessions.

## Dashboard
[Screenshot: Dashboard overview showing statistics and quick access buttons]

The Dashboard is your central hub for monitoring welfare activities. Here you can:
- View current festival status and key statistics
- Access quick links to common tasks
- Monitor recent admissions and activities
- See current bay/chair occupancy status

## Admissions
[Screenshot: Admissions list showing current occupants and recent admissions]

The Admissions page shows all welfare admissions. You can:
- View all current and past admissions
- Filter admissions by date, status, or other criteria
- Access detailed information about each admission
- Update admission status and add notes
- Monitor bay/chair assignments

### Creating a New Admission
[Screenshot: New admission form with personal information section visible]

To create a new admission:
1. Click the "New Admission" button
2. Fill out the personal information:
   - First name (required)
   - Surname (optional)
   - Age
   - Contact details
   - Simplified ethnicity options
3. Complete all relevant sections:
   - Location (current bay/chair assignment)
   - Physical Description
   - Substance Use (if applicable)
   - Safeguarding Concerns
   - Referral Information
4. Add any additional notes
5. Save the admission to create the record

## Front of House
[Screenshot: Front of house interface showing item distribution form]

The Front of House page helps track welfare items given to festival attendees:
- Record items provided to visitors
- Track inventory of essential supplies:
  - Medical supplies (Sanitizer, Toilet Roll)
  - Weather protection (Suncream, Poncho)
  - Safety items (Earplugs, Condoms)
  - Wristbands
  - Water and charging facilities
- Monitor usage patterns
- View low stock warnings

### Item Distribution
There are two ways to record distributed items:
1. **Single Click**: Click once on an item button to add one of that item
2. **Long Press**: Press and hold an item button to enter a specific quantity
   - Especially useful for distributing multiple items at once
   - Optimized for iPad and touch devices

## Shift Management
[Screenshot: Shift schedule showing team assignments]

Manage staff scheduling through:
- Viewing current shift patterns
- Checking team leader assignments
- Monitoring shift notes
- Viewing team member schedules
- Following the rotation pattern (A → B → C → D → E)

## Lost Property
[Screenshot: Lost property logging form and item list]

Track and manage lost property items:
- Log new found items with detailed descriptions
- Categorize items (Phone, Passport, Keys, etc.)
- Add photos of found items when possible
- Record claimed items and return information
- Search existing lost property using the search box
- Generate lost property reports

### Managing Returns
When an item is claimed:
1. Find the item in the list
2. Click "Mark as Returned"
3. Enter the return details

If an item was accidentally marked as returned:
1. Find the item in the returned items list
2. Click "Unmark as Returned"
3. The item will be moved back to the active list

## Knowledge Base
[Screenshot: Knowledge Base tile view showing categorized resources]

The Knowledge Base provides quick access to important external resources:
- Browse resources organized by category and subcategory
- Access substance information, mental health resources, and support contacts
- View phone numbers and website links for external services
- Resources may be specific to the current festival or available across all festivals
- Search for specific resources using the search function

### Adding Knowledge Base Resources
If you have admin access, you can add new resources:
1. Click the "Add Resource" button
2. Enter the resource details:
   - Title and description
   - URL and/or phone number
   - Category and subcategory
   - Specify if the resource should be available for all festivals
3. Save the resource to make it available to all users

## Reports
[Screenshot: Reports dashboard showing available report types]

Generate comprehensive analytics through:
- Admissions Reports
  - View admission trends
  - Track substance use patterns
  - Monitor safeguarding incidents
- Front of House Reports
  - Item distribution statistics
  - Stock level tracking
  - Usage patterns
- Lost Property Reports
  - Items found/claimed statistics
  - Category breakdowns
  - Time-based analysis

### Generating Reports
[Screenshot: Report generation interface with date selection]

To create a report:
1. Select the report type
2. Choose the date range
3. Select specific data points to include
4. Generate the report in your preferred format (PDF available)

### Interactive Reports
The reports include interactive elements:
- Click on any bar in the admissions chart to see patients admitted during that time period
- Click on any row in the tables to view detailed information about that record
- Use the search and filter functions to find specific records
- Sort columns by clicking on column headers

## Feedback
[Screenshot: Feedback form showing rating and comment fields]

Help improve the system:
- Click the feedback button in the bottom-right corner
- Rate your experience
- Provide detailed comments
- Report any issues encountered
- Track the status of your submitted feedback

## Access Management
[Screenshot: Access Management interface showing user roles and permissions]

For administrators, the Access Management page provides control over user permissions:
- View all users who have accessed the system
- Assign roles to users (Admin, Partner, User, Public)
- Set required access levels for different features
- Monitor user activity and access patterns

### User Roles
The system uses a role-based access control system:
- **Admin**: Full access to all features and management functions
- **Partner**: Access to most features except system administration
- **User**: Standard access to core welfare functions
- **Public**: Limited access to basic information only

## Data Management
Important notes about data handling:
- All data is automatically synchronized between devices
- The system works offline with automatic sync when online
- Live sync with WebSocket support ensures real-time updates
- Improved conflict resolution for simultaneous edits
- Records are retained for 3 months
- Regular backups ensure data safety
- Export functionality available for data preservation

### Festival-Specific Data
- Each festival maintains its own separate data
- Some resources (like Knowledge Base items) can be shared across festivals
- Your active festival selection is browser-specific, allowing different users to work with different festivals simultaneously

---

**Technical Support**
If you encounter any issues:
1. Check your internet connection
2. Try refreshing the page
3. Use the feedback button to report specific problems
4. Contact your system administrator if problems persist

**Note:** Screenshots will be updated regularly to reflect the latest interface changes. For technical support or questions, please contact your system administrator.

## Frequently Asked Questions (FAQ)

**Q: What is the iThinc Welfare Management System?**
A: It's a web application designed for managing welfare services at festivals and events, covering admissions, shifts, inventory, lost property, and reporting across multiple sites if needed.

**Q: Can I use the system offline?**
A: Yes, the system is designed with offline-first capability. It uses local storage (PouchDB) allowing you to continue working without an internet connection. Data syncs automatically with the central database (CouchDB) when you're back online.

**Q: How does the system handle multiple sites at a festival (e.g., Arena and Campsite)?**
A: The system supports multi-site management. You can easily switch between configured sites (like Arena or Campsite) using the selector in the festival header. Data for admissions, inventory, and shifts is kept separate for each site, ensuring accurate tracking.

**Q: How do I add a new festival or manage existing ones?**
A: Navigate to the Festival Management page. Here you can create new festivals, edit details of existing ones (name, dates, location), configure sites (Arena, Campsite), and set the active festival.

**Q: How is patient data recorded in Admissions?**
A: The Admission form is comprehensive and modular. It includes sections for Personal Information, Location within the festival, Physical Description, Substance Use details, Safeguarding concerns, Referral information, and general Admission Notes. Fill in the relevant sections to create a detailed record.

**Q: Can I track inventory usage per site?**
A: Yes, inventory management is site-specific. When you record item usage (e.g., handing out water, ponchos, first aid supplies), it's tracked against the currently selected site (Arena or Campsite).

**Q: How does shift scheduling work?**
A: Administrators can configure shift patterns and assign team leaders via the Shifts page. The system helps generate schedules, often following a predefined rotation pattern. Shift notes specific to each site can also be recorded.

**Q: What happens if two users edit the same record while offline?**
A: The system uses PouchDB and CouchDB, which include mechanisms for conflict resolution. When data syncs, the system attempts to merge changes intelligently to maintain data integrity.

**Q: How do I find a specific lost property item?**
A: Go to the Lost Property page. You can use the quick search bar for general searches or utilize the advanced filtering options (filter by category, status, date found, description keywords, etc.) to locate specific items efficiently.

**Q: How can I provide feedback about the system?**
A: A feedback button is available on most pages. Clicking this button opens a form where you can submit comments, suggestions, or report any issues you encounter. This feedback is reviewed by the administrators.