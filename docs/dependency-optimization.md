# Dependency Optimization Guide

This document outlines the strategies implemented to optimize dependencies and reduce bundle size in the iTHINK Welfare application.

## Implemented Optimizations

### 1. Moved Development Dependencies

The following packages were moved from `dependencies` to `devDependencies` as they are only used during development and testing:

- `@testing-library/jest-dom`
- `@testing-library/react`
- `@testing-library/user-event`
- `@types/jest`
- `@types/lodash`
- `@types/node`
- `@types/pouchdb-browser`
- `@types/react`
- `@types/react-dom`
- `@types/uuid`
- `typescript`
- `web-vitals`

### 2. Removed Unused Dependencies

The following dependencies were removed as they were not being used in the application:

- `@mui/system` - Not directly imported anywhere in the codebase
- `process` - Not necessary in the browser environment with proper webpack configuration
- `util` - Not necessary in the browser environment with proper webpack configuration

### 3. Optimized MUI Imports

Updated MUI imports to use specific component paths rather than importing from the root package. For example:

```javascript
// Before
import { Button, <PERSON>Field, Typography } from '@mui/material';

// After
import Button from '@mui/material/Button';
import <PERSON>Field from '@mui/material/TextField';
import Typography from '@mui/material/Typography';
```

This allows the bundler to include only the components that are actually used, rather than the entire package.

### 4. Optimized Workbox Usage

Consolidated Workbox imports to only include the modules that are actually used in the service worker. The unused Workbox modules were removed from dependencies.

### 5. Selective Imports for Large Libraries

- **lodash**: Already using selective imports (`import debounce from 'lodash/debounce'`)
- **date-fns**: Already using selective imports for specific functions

## Best Practices for Future Development

### 1. Import Optimization

Always use specific imports rather than importing from the root package:

```javascript
// Prefer this
import Button from '@mui/material/Button';

// Over this
import { Button } from '@mui/material';
```

### 2. Dependency Management

- Before adding a new dependency, check if the functionality can be implemented with existing dependencies
- Consider the size impact of new dependencies using tools like [Bundlephobia](https://bundlephobia.com/)
- For large libraries, always use selective imports
- Add proper side effects comments where needed:
  ```javascript
  // For libraries with side effects
  import /* webpackPrefetch: true */ './some-module';
  ```

### 3. Regular Auditing

- Run `npm run build:analyze` periodically to identify large dependencies
- Review the dependency tree with `npm ls` to identify duplicate or unnecessary packages
- Use `npm prune` to remove unused dependencies

### 4. Code Splitting

Continue to use React's lazy loading and code splitting to load components only when needed:

```javascript
const MyComponent = lazy(() => import('./MyComponent'));
```

## Monitoring Bundle Size

To monitor the bundle size and identify optimization opportunities:

1. Run `npm run build:analyze` to generate a bundle analysis report
2. Review the report to identify large dependencies
3. Look for opportunities to optimize imports or replace heavy dependencies

By following these practices, we can maintain a lean and performant application with minimal bundle size.