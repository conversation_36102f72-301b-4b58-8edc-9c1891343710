# iTHINK Welfare Management System

A Progressive Web Application (PWA) for managing welfare admissions, lost property, and resource tracking at festivals and events.

## Features

- **Festival Management**
  - Create and manage multiple festivals
  - Switch between different festivals
  - Track resources and admissions per festival

- **Welfare Admissions**
  - Record personal information and demographics
  - Track substance use and mental health details
  - Monitor admission status and bay/chair allocation
  - Add and update admission notes
  - Age calculation and under-18 warnings

- **Lost Property Management**
  - Record found items with detailed descriptions
  - Track item status (found/returned)
  - Categorize items (phones, wallets, keys, etc.)
  - Filter items by festival
  - Record item return status

- **Resource Tracking**
  - Monitor welfare supplies (water, suncream, etc.)
  - Track distribution of resources
  - Historical usage statistics

- **Technical Features**
  - Offline-first functionality with PouchDB
  - Real-time data synchronization
  - Responsive design optimized for iPad
  - Secure data storage
  - Progressive Web App capabilities

## User Guide

### Getting Started

1. **Accessing the System**
   - Open the application in a web browser
   - The system will work offline once loaded
   - First-time users should load while online to sync initial data

2. **Festival Selection**
   - Use the festival dropdown at the top of the screen
   - Select the active festival you're working with
   - All data entry will be associated with the selected festival

### Managing Welfare Admissions

1. **Creating a New Admission**
   - Navigate to the Admissions page
   - Fill in required personal information
   - Select bay/chair allocation
   - Record substance use if applicable
   - Add any relevant notes
   - Save the admission

2. **Updating Admissions**
   - Find the admission in the list
   - Click to edit and update information
   - Add progress notes as needed
   - Update status (e.g., discharged)

### Lost Property Management

1. **Recording Found Items**
   - Go to the Lost Property page
   - Select the item category
   - Enter description and location found
   - Add any identifying details
   - Save the item record

2. **Managing Items**
   - View all items for the current festival
   - Update item status when returned
   - Filter and search for specific items
   - Track return status

### Resource Tracking

1. **Recording Resource Usage**
   - Navigate to the Resources page
   - Update quantities of distributed items
   - Add notes for special circumstances
   - Track supply levels

2. **Viewing Statistics**
   - Access usage reports by festival
   - View historical distribution data
   - Monitor supply levels

### Offline Usage

The system works offline by:
- Storing all data locally using PouchDB
- Automatically syncing when online
- Showing sync status in the interface
- Caching assets for offline access

### Data Security

- All data is encrypted at rest
- Sync occurs only on secure networks
- No sensitive data is cached
- Regular data backups are maintained

## Technology Stack

- React with TypeScript
- PouchDB for offline data storage
- Service Workers for offline functionality
- Tailwind CSS for styling
- Progressive Web App (PWA) capabilities

## Installation

1. Clone the repository
2. Install dependencies:
   ```bash
   npm install --legacy-peer-deps
   ```

3. Configure the remote database URL in `src/services/database.ts`

4. Start the development server:
   ```bash
   npm start
   ```

5. Build for production:
   ```bash
   npm run build
   ```

## Contributing

1. Fork the repository
2. Create your feature branch
3. Commit your changes
4. Push to the branch
5. Create a new Pull Request

## Support

For support and bug reports, please create an issue in the GitHub repository.

## License

This project is licensed under the MIT License.
