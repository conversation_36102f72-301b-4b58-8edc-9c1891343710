(()=>{"use strict";var e={87393:function(e,t,a){a.d(t,{U:()=>p});var i=a(85893),s=a(67294),r=a(83502),n=a(5214),o=a(91679),d=a(89126),l=a(33991),c=a(54757),h=a(38953),u=a(80415),y=a(56099),g=a(39467);let p=e=>{let{onClose:t}=e,[a,p]=(0,s.useState)(""),[m,f]=(0,s.useState)(!1),[b,w]=(0,s.useState)(""),[v,S]=(0,s.useState)(!1),{activeFestival:x}=(0,n.C)(),D=(0,s.useRef)(null),A=async()=>{if(x)try{f(!0),await r.R.updateFestivalNotes(x._id,a),w("")}catch(e){w("Failed to save notes")}finally{f(!1)}},_=async()=>{await A(),t()};(0,s.useEffect)(()=>{(async()=>{if(!x)return p("");try{S(!0);let e=await r.R.getFestivalNotes(x._id);p(e||"")}catch(e){w("Failed to load notes"),p("")}finally{S(!1)}})()},[x]);let I=!x;return(0,s.useEffect)(()=>{D.current&&D.current.focus()},[]),(0,i.jsx)(o.ZP,{anchor:"right",open:!0,onClose:_,sx:{"& .MuiDrawer-paper":{width:"100%",maxWidth:"32rem"}},children:(0,i.jsxs)(d.Z,{elevation:0,sx:{display:"flex",flexDirection:"column",height:"100%",borderRadius:0},children:[(0,i.jsxs)(l.Z,{sx:{px:3,py:2,display:"flex",alignItems:"center",justifyContent:"space-between",borderBottom:1,borderColor:"divider"},children:[(0,i.jsx)(c.Z,{variant:"h6",component:"h2",children:"Notes for Next Shift"}),(0,i.jsx)(h.Z,{onClick:_,edge:"end","aria-label":"close",size:"small",children:(0,i.jsx)(u.Z,{children:"close"})})]}),(0,i.jsx)(l.Z,{sx:{flex:1,p:3,display:"flex",flexDirection:"column"},children:(0,i.jsx)(y.Z,{inputRef:D,value:a,onChange:e=>p(e.target.value),onBlur:A,multiline:!0,fullWidth:!0,variant:"outlined",placeholder:I?"Select a festival to add notes":"Enter notes for the next shift...",disabled:I,sx:{flex:1,"& .MuiInputBase-root":{height:"100%"},"& .MuiInputBase-input":{height:"100% !important",overflow:"auto !important"}}})}),(0,i.jsxs)(l.Z,{sx:{borderTop:1,borderColor:"divider"},children:[m&&(0,i.jsx)(g.Z,{severity:"info",sx:{borderRadius:0},children:"Saving..."}),b&&(0,i.jsx)(g.Z,{severity:"error",sx:{borderRadius:0},children:b}),v&&(0,i.jsx)(g.Z,{severity:"info",sx:{borderRadius:0},children:"Loading..."})]})]})})}},37231:function(e,t,a){a.d(t,{H:()=>l,a:()=>d});var i=a(85893),s=a(67294),r=a(83502),n=a(3065);let o=(0,s.createContext)({userEmail:null,isAdmin:!1,hasAccess:()=>!1,userRole:null,isLoading:!0}),d=()=>(0,s.useContext)(o),l=e=>{let{children:t}=e,[a,d]=(0,s.useState)(null),[l,c]=(0,s.useState)(null),[h,u]=(0,s.useState)(!0),[y,g]=(0,s.useState)(new Map);(0,s.useEffect)(()=>{let e=/iPad/.test(navigator.userAgent)||/Macintosh/.test(navigator.userAgent)&&"ontouchend"in document,t=document.querySelector('meta[name="x-auth-user-email"]')?.getAttribute("content"),a=(e=>{let t=`; ${document.cookie}`.split(`; ${e}=`);return 2===t.length&&t.pop()?.split(";").shift()||null})("auth_user_email"),i=document.querySelector('meta[http-equiv="X-Auth-User-Email"]')?.getAttribute("content"),s=t||a||i||null;if(t||a||i||(s="<EMAIL>"),d(s),u(!1),e)return void c(n.K.USER);(async()=>{if(s)try{let e=new Promise((e,t)=>{setTimeout(()=>t(Error("Auth timeout after 5 seconds")),5e3)}),t=await Promise.race([r.R.getUserRole(s),e]);c(t?.role||null);let a=await Promise.race([r.R.getAllPageAccess(),e]),i=new Map;a.forEach(e=>{i.set(e.pageId,e.requiredRole)}),g(i)}catch(e){c(n.K.ADMIN)}})()},[]);let p=["<EMAIL>","<EMAIL>","<EMAIL>"],m=["brisflix.com"],f=(0,s.useMemo)(()=>{if(l===n.K.ADMIN)return!0;if(!a)return!1;if(p.includes(a))return!0;let e=a.split("@")[1];return m.includes(e)},[a,l]);return(0,i.jsx)(o.Provider,{value:{userEmail:a,isAdmin:f,hasAccess:e=>{if(!a)return!1;if(f)return!0;let t=y.get(e),i=e=>{if(!l)return e===n.K.PUBLIC;switch(l){case n.K.ADMIN:return!0;case n.K.USER:return e===n.K.USER||e===n.K.PARTNER||e===n.K.PUBLIC;case n.K.PARTNER:return e===n.K.PARTNER||e===n.K.PUBLIC;default:return e===n.K.PUBLIC}};if(t)return i(t);switch(e){case"reports":case"shifts":case"admin-panel":case"knowledge-base-edit":case"festival-management":case"access-management":return i(n.K.ADMIN);case"feedback-management":case"admissions":case"new-admission":case"front-of-house":return i(n.K.USER);case"lost-property":return i(n.K.PARTNER);default:return i(n.K.PUBLIC)}},userRole:l,isLoading:h},children:t})}},5214:function(e,t,a){a.d(t,{C:()=>d,t:()=>o});var i=a(85893),s=a(67294),r=a(83502);let n=(0,s.createContext)(void 0),o=e=>{let{children:t}=e,[a,o]=(0,s.useState)([]),[d,l]=(0,s.useState)(null),[c,h]=(0,s.useState)(!0),[u,y]=(0,s.useState)(null),g=(0,s.useRef)(!1),p=(0,s.useRef)(0),m=(0,s.useCallback)(async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];if(e||!g.current)try{g.current=!0,h(!0);let e=await r.R.getFestivals();o(e);let t=localStorage.getItem("activeFestivalId");if(t){let a=e.find(e=>e._id===t);a?d?._id!==a._id&&l(a):(localStorage.removeItem("activeFestivalId"),null!==d&&l(null))}else if(e.length>0){let t=e[0];d?._id!==t._id&&(l(t),localStorage.setItem("activeFestivalId",t._id))}else null!==d&&l(null);y(null)}catch(e){y(e instanceof Error?e:Error("Failed to load festivals"))}finally{h(!1),g.current=!1}},[]);(0,s.useEffect)(()=>{let e=async()=>{try{await m(!0),r.R.manualSync().then(()=>{m(!0)}).catch(e=>{})}catch(t){y(t instanceof Error?t:Error("Failed to load festivals")),p.current<3?(p.current++,setTimeout(e,Math.min(1e3*Math.pow(2,p.current),3e3))):h(!1)}};e()},[m]);let f=(0,s.useCallback)(async e=>{try{h(!0),d?._id!==e?._id&&r.R.manualSync(),e?(l(e),localStorage.setItem("activeFestivalId",e._id)):(l(null),localStorage.removeItem("activeFestivalId")),h(!1)}catch(e){throw y(e instanceof Error?e:Error("Failed to update festival")),h(!1),e}},[d,h,l,y]),b=(0,s.useCallback)(async()=>{try{h(!0),await r.R.sync(),await m(!0)}catch(e){y(e instanceof Error?e:Error("Failed to sync"))}finally{h(!1)}},[h,m,y]),w=s.useMemo(()=>({festivals:a,activeFestival:d,setActiveFestival:f,loading:c,error:u,manualSync:b}),[a,d,f,c,u,b]);return(0,i.jsx)(n.Provider,{value:w,children:t})},d=()=>{let e=(0,s.useContext)(n);if(void 0===e)throw Error("useFestival must be used within a FestivalProvider");return e}},59326:function(e,t,a){a.d(t,{C:()=>o,U:()=>d});var i=a(85893),s=a(67294),r=a(5214);let n=(0,s.createContext)(void 0),o=()=>{let e=(0,s.useContext)(n);if(!e)throw Error("useSiteLocation must be used within a SiteLocationProvider");return e},d=e=>{let{children:t}=e,{activeFestival:a,loading:o}=(0,r.C)(),[d,l]=(0,s.useState)(null);(0,s.useEffect)(()=>{if(o)return;if(!a||!a.locations)return void l(null);let e=localStorage.getItem("activeSiteLocationId");if(e&&Array.isArray(a.locations)){let t=a.locations.find(t=>t.id===e);t?l(t):(localStorage.removeItem("activeSiteLocationId"),l(null))}},[a,o]);let c=(0,s.useCallback)(e=>{l(e),e?localStorage.setItem("activeSiteLocationId",e.id):localStorage.removeItem("activeSiteLocationId")},[]),h=s.useMemo(()=>({activeSiteLocation:d,setActiveSiteLocation:c}),[d,c]);return(0,i.jsx)(n.Provider,{value:h,children:t})}},59113:function(e,t,a){var i=a(85893),s=a(67294),r=a(20745),n=a(96872),o=a(5214),d=a(12546),l=a(87393);let c=()=>"1.11.0";var h=a(75585),u=a(33991),y=a(38953),g=a(65e3),p=a(73876),m=a(54757);let f=e=>{let{festival:t}=e,[a,r]=(0,s.useState)(null),n=[{name:"Main Website",url:t.mainUrl},{name:"Map",url:t.mapUrl},{name:"Travel Info",url:t.travelInfoUrl},{name:"FAQs",url:t.faqsUrl}].filter(e=>e.url);if(0===n.length)return null;let o=()=>{r(null)};return(0,i.jsxs)(u.Z,{children:[(0,i.jsx)(y.Z,{onClick:e=>{e.stopPropagation(),r(e.currentTarget)},title:"Festival Links",size:"small",sx:{color:"black","&:hover":{bgcolor:"rgba(0, 0, 0, 0.1)"}},children:(0,i.jsx)(u.Z,{component:h.Z,sx:{width:20,height:20}})}),(0,i.jsx)(g.Z,{anchorEl:a,open:!!a,onClose:o,anchorOrigin:{vertical:"top",horizontal:"right"},transformOrigin:{vertical:"top",horizontal:"left"},sx:{"& .MuiPaper-root":{width:200,bgcolor:"background.paper",boxShadow:3,borderRadius:1,mt:1}},children:n.map(e=>{let{name:t,url:a}=e;return(0,i.jsx)(p.Z,{onClick:o,component:"a",href:a,target:"_blank",rel:"noopener noreferrer",sx:{py:1,"&:hover":{bgcolor:"action.hover"}},children:(0,i.jsx)(m.Z,{variant:"body2",color:"text.primary",children:t})},t)})})]})};var b=a(17047);let w=e=>{let{festival:t,isCollapsed:a=!1}=e,[r,n]=(0,s.useState)({type:"before",days:0,isLoading:!0}),o=()=>{try{let e=new Date,a=new Date(t.startDate),i=new Date(t.endDate);if(isNaN(a.getTime())||isNaN(i.getTime()))return{type:"before",days:0,isLoading:!1,error:"Invalid festival dates"};let s=e=>{let t=new Date(e);return t.setHours(0,0,0,0),t},r=s(e),n=s(a),o=s(i),d=o.getTime()-n.getTime(),l=Math.max(1,Math.ceil(d/864e5)+1);if(r<n){let e=n.getTime()-r.getTime(),t=Math.ceil(e/864e5);return{type:"before",days:t,isLoading:!1}}if(r<=o){let e=r.getTime()-n.getTime(),t=Math.floor(e/864e5)+1;return{type:"during",days:0,currentDay:Math.max(1,t),totalDays:l,isLoading:!1}}{let e=r.getTime()-o.getTime(),t=Math.floor(e/864e5);return{type:"after",days:t,isLoading:!1}}}catch(e){return{type:"before",days:0,isLoading:!1,error:"Error calculating festival timing"}}};return((0,s.useEffect)(()=>{n(o());let e=setInterval(()=>{n(o())},6e4);return()=>clearInterval(e)},[t.startDate,t.endDate]),a)?(0,i.jsx)(u.Z,{sx:{display:"flex",justifyContent:"center",p:.5},children:(0,i.jsx)(u.Z,{sx:{width:8,height:8,borderRadius:"50%",bgcolor:"during"===r.type?"secondary.main":"before"===r.type?"primary.main":"grey.400"}})}):(0,i.jsxs)(u.Z,{sx:{p:1.5,borderTop:1,borderColor:"rgba(0, 0, 0, 0.1)"},children:[(0,i.jsx)(b.Z,{label:(()=>{if(r.isLoading)return"Loading...";if(r.error)return"Date error";switch(r.type){case"before":if(1===r.days)return"1 day until festival";return`${r.days} days until festival`;case"during":return`Day ${r.currentDay} of ${r.totalDays}`;case"after":if(0===r.days)return"Festival ended today";if(1===r.days)return"1 day since festival ended";return`${r.days} days since festival ended`;default:return""}})(),color:(()=>{switch(r.type){case"before":return"primary";case"during":return"secondary";default:return"default"}})(),variant:"during"===r.type?"filled":"outlined",size:"small",sx:{width:"100%",fontSize:"0.75rem",fontWeight:500,"& .MuiChip-label":{px:1}}}),r.error&&(0,i.jsx)(m.Z,{variant:"caption",sx:{display:"block",textAlign:"center",mt:.5,color:"error.main"},children:r.error})]})};var v=a(59326),S=a(37231),x=a(60488),D=a(64889),A=a(30925),_=a(81839),I=a(62983),M=a(11161),k=a(9332),C=a(19410),T=a(89425);let j=e=>{let{isCollapsed:t=!1}=e,{festivals:a,activeFestival:r,setActiveFestival:n,loading:d}=(0,o.C)(),[l,c]=(0,s.useState)(!1),h=()=>{c(!1)},g=async e=>{await n(e),h()};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(x.Z,{title:t?"Select Festival":"",placement:"right",children:(0,i.jsx)(u.Z,{onClick:()=>{c(!0)},sx:{display:"flex",alignItems:"center",justifyContent:t?"center":"flex-start",p:t?1:1.5,cursor:"pointer",borderRadius:1,"&:hover":{bgcolor:"rgba(0, 0, 0, 0.05)"}},children:t?(0,i.jsx)(m.Z,{variant:"subtitle2",sx:{fontWeight:600,color:"black",transform:"rotate(-90deg)",whiteSpace:"nowrap"},children:"Active Festival"}):(0,i.jsxs)(u.Z,{children:[(0,i.jsx)(m.Z,{variant:"subtitle2",sx:{fontWeight:600,color:"black"},children:"Active Festival:"}),(0,i.jsx)(m.Z,{variant:"body2",color:"black",children:r?r.name:"None selected"})]})})}),(0,i.jsxs)(D.Z,{open:l,onClose:h,maxWidth:"sm",fullWidth:!0,children:[(0,i.jsxs)(A.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:["Select Festival",(0,i.jsx)(y.Z,{edge:"end",color:"inherit",onClick:h,"aria-label":"close",children:(0,i.jsx)(T.Z,{})})]}),(0,i.jsx)(_.Z,{children:d?(0,i.jsx)(m.Z,{children:"Loading festivals..."}):0===a.length?(0,i.jsx)(m.Z,{children:"No festivals available."}):(0,i.jsx)(I.Z,{children:a.map(e=>(0,i.jsx)(M.ZP,{disablePadding:!0,children:(0,i.jsx)(k.Z,{onClick:()=>g(e),selected:r?._id===e._id,children:(0,i.jsx)(C.Z,{primary:e.name,secondary:e.startDate&&e.endDate?`${new Date(e.startDate).toLocaleDateString()} - ${new Date(e.endDate).toLocaleDateString()}`:void 0})})},e._id))})})]})]})};var E=a(83502);let z=()=>{let[e,t]=(0,s.useState)({status:"disconnected",pendingChanges:0,authError:!1});return(0,s.useEffect)(()=>{if(!E.D){let e=()=>{if(E.D)return t(E.D.getSyncStatus()),E.D.addSyncListener(()=>{E.D&&t(E.D.getSyncStatus())})},a=e();if(a)return a;let i=setInterval(()=>{let t=e();if(t)return clearInterval(i),t},100);return()=>clearInterval(i)}return t(E.D.getSyncStatus()),E.D.addSyncListener(()=>{E.D&&t(E.D.getSyncStatus())})},[]),e};var B=a(20249),P=a(74414),F=a(93554),Z=a(28410),L=a(65171),$=a(35337),O=a(62238),R=a(78328),N=a(12900),H=a(60716),U=a(68757),W=a(30513),V=a(24083),K=a(7230),Q=a(92291),Y=a(70779),q=a(64286),G=a(49696),J=a(61215);let X=e=>{let{onSignOut:t,children:a}=e,{activeFestival:r,festivals:h,setActiveFestival:b,loading:X}=(0,o.C)(),{activeSiteLocation:ee,setActiveSiteLocation:et}=(0,v.C)(),{userEmail:ea,hasAccess:ei}=(0,S.a)();(0,d.ac)({query:"(min-width: 1024px)"});let[es,er]=(0,s.useState)(!1),[en,eo]=(0,s.useState)(!1),[ed,el]=(0,s.useState)(null),[ec,eh]=(0,s.useState)(!1),[eu,ey]=(0,s.useState)(!1),eg=z(),ep=()=>{ey(!1)},em=async e=>{await b(e),ep()},ef=()=>{el(null)},eb=e=>{if(r){if(e){let t=r.locations.find(t=>t.id===e);t&&et(t)}else et(null);ef()}},ew=e=>({display:"flex",alignItems:"center",p:.75,borderRadius:1,textDecoration:"none !important",color:"black","&:hover":{bgcolor:"rgba(0, 0, 0, 0.05)"},...e&&{background:"rgba(0, 0, 0, 0.1)"}}),ev={width:18,height:18,mr:1.5*!en,color:"black"},eS=(e,t,a,s)=>(0,i.jsx)(M.ZP,{disablePadding:!0,children:(0,i.jsx)(n.OL,{to:e,style:{width:"100%",textDecoration:"none"},end:s,children:e=>{let{isActive:s}=e;return(0,i.jsx)(x.Z,{title:en?a:"",placement:"right",children:(0,i.jsxs)(u.Z,{sx:ew(s),children:[(0,i.jsx)(u.Z,{component:t,sx:ev}),!en&&(0,i.jsx)(m.Z,{color:"black",children:a})]})})}})}),ex=async()=>{if(!ec)try{eh(!0),await E.R.manualSync()}catch(e){}finally{eh(!1)}},eD=()=>{if(ec)return"Syncing...";switch(eg.status){case"synced":return eg.pendingChanges>0?`Synced (${eg.pendingChanges} pending)`:"Synced";case"syncing":return"Syncing...";case"initial_sync":return"Initial Sync...";case"error":return"Sync Failed";case"auth_error":return"Auth Error";case"paused":return"Sync Paused";case"disconnected":return"Disconnected";default:return"Sync"}},eA=()=>{if(ec||"syncing"===eg.status||"initial_sync"===eg.status)return"#1976d2";switch(eg.status){case"synced":return"#2e7d32";case"error":return"#d32f2f";case"auth_error":return"#ed6c02";default:return"#1976d2"}};return(0,i.jsxs)(u.Z,{sx:{display:"flex",flexDirection:"column",height:"100%",background:"linear-gradient(to right, #662D91, #f5f5f5)",color:"black",width:en?56:240,transition:"width 0.2s ease-in-out",position:"relative"},children:[(0,i.jsx)(u.Z,{sx:{display:"flex",alignItems:"center",p:1.5},children:!en&&(0,i.jsx)(u.Z,{component:"img",src:"/ithink-logo.svg",alt:"iThink Logo",sx:{height:42,mr:1}})}),(0,i.jsx)(y.Z,{onClick:()=>eo(!en),sx:{position:"absolute",right:-20,top:20,bgcolor:"#662D91",color:"white",width:40,height:40,"&:hover":{bgcolor:"rgba(102, 45, 145, 0.9)"},border:"1px solid rgba(255, 255, 255, 0.2)",borderRadius:"50%",zIndex:1},children:en?(0,i.jsx)(B.Z,{}):(0,i.jsx)(P.Z,{})}),(0,i.jsxs)(u.Z,{sx:{borderTop:1,borderBottom:1,borderColor:"rgba(0, 0, 0, 0.1)"},children:[en?(0,i.jsx)(j,{isCollapsed:en}):(0,i.jsxs)(u.Z,{sx:{display:"flex",alignItems:"center",justifyContent:"space-between",p:1.5,cursor:"pointer",borderRadius:1,"&:hover":{bgcolor:"rgba(0, 0, 0, 0.05)"}},onClick:e=>{r?r.hasMultipleLocations&&el(e.currentTarget):ey(!0)},children:[(0,i.jsxs)(u.Z,{sx:{flex:1,cursor:"pointer","&:hover":{opacity:.8}},onClick:e=>{e.stopPropagation(),ey(!0)},children:[(0,i.jsx)(m.Z,{variant:"subtitle2",sx:{fontWeight:600,color:"black"},children:"Active Festival:"}),(0,i.jsx)(m.Z,{variant:"body2",color:"black",children:r?r.name:"None selected"})]}),r&&(0,i.jsx)(u.Z,{sx:{ml:2},children:(0,i.jsx)(f,{festival:r})})]}),r&&(0,i.jsx)(w,{festival:r,isCollapsed:en}),r&&!en&&r.hasMultipleLocations&&(0,i.jsx)(u.Z,{sx:{px:1.5,pb:1.5},children:(0,i.jsxs)(m.Z,{variant:"body2",color:"black",children:["Site: ",ee?"arena"===ee.type?"Arena":"Campsite":"All Sites"]})})]}),r?.hasMultipleLocations&&(0,i.jsxs)(g.Z,{anchorEl:ed,open:!!ed,onClose:ef,anchorOrigin:{vertical:"bottom",horizontal:"left"},transformOrigin:{vertical:"top",horizontal:"left"},children:[(0,i.jsx)(p.Z,{onClick:()=>eb(null),selected:!ee,children:(0,i.jsx)(m.Z,{variant:"body2",children:"All Sites"})}),r.locations.map(e=>(0,i.jsx)(p.Z,{onClick:()=>eb(e.id),selected:ee?.id===e.id,children:(0,i.jsx)(m.Z,{variant:"body2",children:"arena"===e.type?"Arena":"Campsite"})},e.id))]}),(0,i.jsxs)(I.Z,{sx:{flexGrow:1,px:1.5},children:[eS("/",F.Z,"Dashboard",!0),r?.showAdmissions!==!1&&ei("admissions")&&(0,i.jsxs)(i.Fragment,{children:[eS("/admissions",Z.Z,"Admissions"),ei("new-admission")&&eS("/new-admission",L.Z,"New Admission")]}),r?.showFrontOfHouse!==!1&&ei("front-of-house")&&eS("/front-of-house",$.Z,"Front of House"),r?.showLostProperty!==!1&&ei("lost-property")&&eS("/lost-property",O.Z,"Lost Property"),ei("sensory-hub")&&eS("/sensory-hub",R.Z,"Sensory Hub"),ei("knowledge-base-view")&&eS("/knowledge-base",N.Z,"Knowledge Base"),ei("reports")&&eS("/reports",H.Z,"Reports"),r&&!1!==r.showShifts&&ei("shifts")&&eS(`/shifts/${r._id}`,U.Z,"Shifts"),ei("festival-management")&&eS("/admin/festivals",W.Z,"Festival Management"),ei("feedback-management")&&eS("/admin/feedback",V.Z,"Feedback Management"),ei("access-management")&&eS("/admin/access",W.Z,"Access Management"),!en&&ea&&(0,i.jsxs)(u.Z,{sx:{mt:2,p:1,bgcolor:"rgba(0, 0, 0, 0.05)",borderRadius:1},children:[(0,i.jsx)(m.Z,{variant:"caption",sx:{display:"block",color:"black"},children:"Logged in as:"}),(0,i.jsx)(m.Z,{variant:"caption",sx:{display:"block",color:"black",wordBreak:"break-all"},children:ea})]})]}),a,(0,i.jsxs)(u.Z,{sx:{p:1.5,borderTop:1,borderColor:"rgba(0, 0, 0, 0.1)"},children:[(0,i.jsx)(x.Z,{title:en?eD():"",placement:"right",children:(0,i.jsxs)(K.Z,{fullWidth:!0,onClick:ex,disabled:ec,sx:{...ew(!1),color:eA(),minWidth:"unset",mb:1},children:[(0,i.jsx)(u.Z,{sx:{width:18,height:18,mr:1.5*!en},children:(()=>{if(ec||"syncing"===eg.status||"initial_sync"===eg.status)return(0,i.jsx)(J.Z,{size:18,sx:{color:"#1976d2"}});switch(eg.status){case"synced":return(0,i.jsx)(Y.Z,{sx:{width:18,height:18,color:"#2e7d32"}});case"error":return(0,i.jsx)(q.Z,{sx:{width:18,height:18,color:"#d32f2f"}});case"auth_error":return(0,i.jsx)(G.Z,{sx:{width:18,height:18,color:"#ed6c02"}});default:return(0,i.jsx)(Q.Z,{sx:{width:18,height:18,color:"#1976d2"}})}})()}),!en&&(0,i.jsx)(m.Z,{sx:{color:eA()},children:eD()})]})}),(0,i.jsx)(x.Z,{title:en?"Notes":"",placement:"right",children:(0,i.jsxs)(K.Z,{fullWidth:!0,onClick:()=>er(!0),sx:{color:"black",display:"flex",alignItems:"center",p:.75,borderRadius:1,minWidth:"unset","&:hover":{bgcolor:"rgba(0, 0, 0, 0.05)"}},children:[(0,i.jsx)(u.Z,{component:"svg",sx:{width:18,height:18,mr:1.5*!en},fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,i.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})}),!en&&(0,i.jsx)(m.Z,{color:"black",children:"Notes"})]})}),!en&&(0,i.jsxs)(u.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center"},children:[(0,i.jsx)(n.OL,{to:"/user-guide",style:{textDecoration:"none"},children:(0,i.jsx)(m.Z,{variant:"caption",sx:{display:"block",textAlign:"center",mt:1.5,color:"black","&:hover":{textDecoration:"underline"}},children:"User Guide"})}),(0,i.jsx)(n.OL,{to:"/changelog",style:{textDecoration:"none"},children:(0,i.jsxs)(m.Z,{variant:"caption",sx:{display:"block",textAlign:"center",mt:.5,color:"black","&:hover":{textDecoration:"underline"}},children:["Version ",c()]})})]})]}),es&&(0,i.jsx)(l.U,{onClose:()=>er(!1)}),(0,i.jsxs)(D.Z,{open:eu,onClose:ep,maxWidth:"sm",fullWidth:!0,children:[(0,i.jsxs)(A.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:["Select Festival",(0,i.jsx)(y.Z,{edge:"end",color:"inherit",onClick:ep,"aria-label":"close",children:(0,i.jsx)(T.Z,{})})]}),(0,i.jsx)(_.Z,{children:X?(0,i.jsx)(m.Z,{children:"Loading festivals..."}):0===h.length?(0,i.jsx)(m.Z,{children:"No festivals available."}):(0,i.jsx)(I.Z,{children:h.map(e=>(0,i.jsx)(M.ZP,{disablePadding:!0,children:(0,i.jsx)(k.Z,{onClick:()=>em(e),selected:r?._id===e._id,children:(0,i.jsx)(C.Z,{primary:e.name,secondary:e.startDate&&e.endDate?`${new Date(e.startDate).toLocaleDateString()} - ${new Date(e.endDate).toLocaleDateString()}`:void 0})})},e._id))})})]})]})},ee=e=>{let{feature:t,children:a,fallbackPath:s="/"}=e,{hasAccess:r}=(0,S.a)();return r(t)?(0,i.jsx)(i.Fragment,{children:a}):(0,i.jsx)(n.Fg,{to:s,replace:!0})};var et=a(1824),ea=a(72434),ei=a(39467),es=a(56099),er=a(13319),en=a(17616);let eo=e=>{let{open:t,onClose:a}=e,r=(0,n.TH)(),[o,d]=(0,s.useState)(""),[l,c]=(0,s.useState)(""),[h,u]=(0,s.useState)(!1),[y,g]=(0,s.useState)(!1),[p,m]=(0,s.useState)(null),f=async()=>{if(!o.trim()||!l.trim())return void m("Please fill in all fields");u(!0),m(null);try{await E.R.addFeedback({name:o.trim(),page:r.pathname,feedback:l.trim()}),g(!0),d(""),c(""),a()}catch(e){m("Failed to submit feedback. Please try again.")}finally{u(!1)}},b=()=>{d(""),c(""),m(null),a()};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(D.Z,{open:t,onClose:b,maxWidth:"sm",fullWidth:!0,children:[(0,i.jsx)(A.Z,{children:"Provide Feedback"}),(0,i.jsxs)(_.Z,{children:[p&&(0,i.jsx)(ei.Z,{severity:"error",sx:{mb:2},children:p}),(0,i.jsx)(es.Z,{autoFocus:!0,margin:"dense",label:"Your Name",fullWidth:!0,value:o,onChange:e=>d(e.target.value),disabled:h,sx:{mb:2}}),(0,i.jsx)(es.Z,{margin:"dense",label:"Your Feedback",fullWidth:!0,multiline:!0,rows:4,value:l,onChange:e=>c(e.target.value),disabled:h})]}),(0,i.jsxs)(er.Z,{children:[(0,i.jsx)(K.Z,{onClick:b,disabled:h,children:"Cancel"}),(0,i.jsx)(K.Z,{onClick:f,variant:"contained",disabled:h,children:"Submit"})]})]}),(0,i.jsx)(en.Z,{open:y,autoHideDuration:6e3,onClose:()=>g(!1),anchorOrigin:{vertical:"bottom",horizontal:"center"},children:(0,i.jsx)(ei.Z,{severity:"success",sx:{width:"100%"},children:"Thank you for your feedback!"})})]})},ed=()=>{let[e,t]=(0,s.useState)(!1);return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(et.Z,{color:"primary","aria-label":"feedback",onClick:()=>t(!0),sx:{position:"fixed",bottom:16,right:16,zIndex:1e3},children:(0,i.jsx)(ea.Z,{})}),(0,i.jsx)(eo,{open:e,onClose:()=>t(!1)})]})},el=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("743")]).then(a.bind(a,67347)).then(e=>({default:e.DashboardPage}))),ec=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("192"),a.e("632")]).then(a.bind(a,46381)).then(e=>({default:e.AdmissionsPage}))),eh=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("616")]).then(a.bind(a,66713)).then(e=>({default:e.NewAdmissionPage}))),eu=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("192"),a.e("934")]).then(a.bind(a,95809)).then(e=>({default:e.FrontOfHousePage}))),ey=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("192"),a.e("581"),a.e("435")]).then(a.bind(a,86755)).then(e=>({default:e.LostPropertyPage}))),eg=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("192"),a.e("581"),a.e("555")]).then(a.bind(a,51780)).then(e=>({default:e.ReportsPage}))),ep=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("442")]).then(a.bind(a,98911))),em=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("192"),a.e("99")]).then(a.bind(a,48066)).then(e=>({default:e.FestivalManagementPage}))),ef=(0,s.lazy)(()=>a.e("702").then(a.bind(a,6921)).then(e=>({default:e.ChangelogPage}))),eb=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("871")]).then(a.bind(a,4733)).then(e=>({default:e.UserGuidePageSimple}))),ew=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("192"),a.e("175")]).then(a.bind(a,2688)).then(e=>({default:e.FeedbackManagementPage}))),ev=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("880")]).then(a.bind(a,24856)).then(e=>({default:e.KnowledgeBasePage}))),eS=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("923")]).then(a.bind(a,33327))),ex=(0,s.lazy)(()=>Promise.all([a.e("263"),a.e("374")]).then(a.bind(a,81433)).then(e=>({default:e.SensoryHubPage}))),eD=()=>(0,i.jsx)(u.Z,{sx:{display:"flex",justifyContent:"center",alignItems:"center",height:"100%",p:4,minHeight:"200px",backgroundColor:"#f5f5f5"},children:(0,i.jsxs)(u.Z,{sx:{textAlign:"center"},children:[(0,i.jsx)(J.Z,{size:40}),(0,i.jsx)(u.Z,{sx:{mt:2,color:"text.secondary",fontSize:"0.875rem"},children:"Loading..."})]})}),eA=()=>{let e=async()=>!0;return(0,i.jsxs)(u.Z,{sx:{display:"flex",height:"100vh"},children:[(0,i.jsx)(X,{onSignOut:e}),(0,i.jsxs)(u.Z,{component:"main",sx:{flexGrow:1,p:3,overflow:"auto",bgcolor:"#f5f5f5"},children:[(0,i.jsx)(s.Suspense,{fallback:(0,i.jsx)(eD,{}),children:(0,i.jsxs)(n.Z5,{children:[(0,i.jsx)(n.AW,{path:"/",element:(0,i.jsx)(el,{})}),(0,i.jsx)(n.AW,{path:"/changelog",element:(0,i.jsx)(ef,{})}),(0,i.jsx)(n.AW,{path:"/user-guide",element:(0,i.jsx)(eb,{})}),(0,i.jsx)(n.AW,{path:"/admissions",element:(0,i.jsx)(ee,{feature:"admissions",children:(0,i.jsx)(ec,{})})}),(0,i.jsx)(n.AW,{path:"/new-admission",element:(0,i.jsx)(ee,{feature:"new-admission",children:(0,i.jsx)(eh,{})})}),(0,i.jsx)(n.AW,{path:"/edit-admission/:id",element:(0,i.jsx)(ee,{feature:"new-admission",children:(0,i.jsx)(eh,{})})}),(0,i.jsx)(n.AW,{path:"/front-of-house",element:(0,i.jsx)(ee,{feature:"front-of-house",children:(0,i.jsx)(eu,{})})}),(0,i.jsx)(n.AW,{path:"/lost-property",element:(0,i.jsx)(ee,{feature:"lost-property",children:(0,i.jsx)(ey,{})})}),(0,i.jsx)(n.AW,{path:"/sensory-hub",element:(0,i.jsx)(ee,{feature:"sensory-hub",children:(0,i.jsx)(ex,{})})}),(0,i.jsx)(n.AW,{path:"/reports",element:(0,i.jsx)(ee,{feature:"reports",children:(0,i.jsx)(eg,{})})}),(0,i.jsx)(n.AW,{path:"/shifts/:festivalId",element:(0,i.jsx)(ee,{feature:"shifts",children:(0,i.jsx)(ep,{})})}),(0,i.jsx)(n.AW,{path:"/admin/festivals",element:(0,i.jsx)(ee,{feature:"festival-management",children:(0,i.jsx)(em,{})})}),(0,i.jsx)(n.AW,{path:"/admin/feedback",element:(0,i.jsx)(ee,{feature:"feedback-management",children:(0,i.jsx)(ew,{})})}),(0,i.jsx)(n.AW,{path:"/admin/access",element:(0,i.jsx)(ee,{feature:"access-management",children:(0,i.jsx)(eS,{})})}),(0,i.jsx)(n.AW,{path:"/knowledge-base",element:(0,i.jsx)(ee,{feature:"knowledge-base-view",children:(0,i.jsx)(ev,{})})})]})}),(0,i.jsx)(ed,{})]})]})};var e_=a(99814),eI=a(14115),eM=a(68530),ek=a(62403);let eC={purple:"#662D91",pink:"#E0338C",purpleLight:"#8E44AD",pinkLight:"#E74C3C",gradientStart:"#662D91",gradientEnd:"#E0338C"},eT=(0,eM.Z)({palette:{primary:{main:eC.purple,light:eC.purpleLight,contrastText:"#fff"},secondary:{main:eC.pink,light:eC.pinkLight,contrastText:"#fff"},ithink:eC,background:{default:"#f5f5f5",paper:"#ffffff"}},typography:{fontFamily:'-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",Arial,sans-serif',h1:{fontSize:"2rem",fontWeight:600,color:eC.purple},h2:{fontSize:"1.75rem",fontWeight:600,color:eC.purple},h3:{fontSize:"1.5rem",fontWeight:500,color:eC.purple},h4:{fontSize:"1.25rem",fontWeight:500,color:eC.purple},h5:{fontSize:"1.125rem",fontWeight:500,color:eC.purple},h6:{fontSize:"1rem",fontWeight:500,color:eC.purple}},components:{MuiButton:{styleOverrides:{root:{textTransform:"none",borderRadius:"8px",fontWeight:500},contained:{boxShadow:"none","&:hover":{boxShadow:"none"}}},defaultProps:{disableElevation:!0}},MuiPaper:{styleOverrides:{root:{borderRadius:"12px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)"}}},MuiCard:{styleOverrides:{root:{borderRadius:"12px",boxShadow:"0 2px 4px rgba(0,0,0,0.05)"}}},MuiTableCell:{styleOverrides:{root:{borderBottom:"1px solid rgba(224, 224, 224, 0.4)"},head:{fontWeight:600,backgroundColor:(0,ek.Fq)(eC.purple,.05)}}},MuiAppBar:{styleOverrides:{root:{background:`linear-gradient(45deg, ${eC.gradientStart} 30%, ${eC.gradientEnd} 90%)`,boxShadow:"none"}}},MuiTabs:{styleOverrides:{indicator:{backgroundColor:eC.pink}}},MuiTab:{styleOverrides:{root:{textTransform:"none","&.Mui-selected":{color:eC.pink}}}},MuiTextField:{styleOverrides:{root:{"& .MuiOutlinedInput-root":{"&.Mui-focused fieldset":{borderColor:eC.purple}}}}},MuiCheckbox:{styleOverrides:{root:{color:eC.purple,"&.Mui-checked":{color:eC.purple}}}},MuiRadio:{styleOverrides:{root:{color:eC.purple,"&.Mui-checked":{color:eC.purple}}}}}});eC.gradientStart,eC.gradientEnd,r.createRoot(document.getElementById("root")).render((0,i.jsxs)(e_.Z,{theme:eT,children:[(0,i.jsx)(eI.ZP,{}),(0,i.jsx)(n.VK,{children:(0,i.jsx)(o.t,{children:(0,i.jsx)(v.U,{children:(0,i.jsx)(()=>(0,i.jsx)(S.H,{children:(0,i.jsx)(v.U,{children:(0,i.jsx)(eA,{})})}),{})})})})]}))},3065:function(e,t,a){a.d(t,{D:()=>o,K:()=>n});var i,s=a(83087),r=a(38575),n=((i={}).ADMIN="admin",i.USER="user",i.PARTNER="partner",i.PUBLIC="public",i);let o=new class{db;constructor(){this.db=new s.Z(r.H4)}async getAllPageAccess(){try{return(await this.db.find({selector:{type:"page-access"}})).docs.map(e=>e)}catch(e){return[]}}async getPageAccess(e){try{let t=await this.db.find({selector:{type:"page-access",pageId:e}});if(t.docs.length>0)return t.docs[0];return null}catch(e){return null}}async setPageAccess(e,t,a){try{let i=await this.getPageAccess(e);return i?await this.db.put({...i,requiredRole:a,updatedAt:new Date().toISOString()}):await this.db.put({_id:`page-access-${e}`,type:"page-access",documentType:"page-access",syncStatus:"sync_pending",pageId:e,pageName:t,requiredRole:a,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}),!0}catch(e){return!1}}async getAllUserRoles(){try{return(await this.db.find({selector:{type:"user-role"}})).docs.map(e=>e)}catch(e){return[]}}async getUserRole(e){try{let t=await this.db.find({selector:{type:"user-role",email:e}});if(t.docs.length>0)return t.docs[0];return null}catch(e){return null}}async setUserRole(e,t){try{let a=await this.getUserRole(e);return a?await this.db.put({...a,role:t,updatedAt:new Date().toISOString()}):await this.db.put({_id:`user-role-${e}`,type:"user-role",documentType:"user-role",syncStatus:"sync_pending",email:e,role:t,createdAt:new Date().toISOString(),updatedAt:new Date().toISOString()}),!0}catch(e){return!1}}async deleteUserRole(e){try{let t=await this.getUserRole(e);if(t&&t._id&&t._rev)return await this.db.remove(t._id,t._rev),!0;return!1}catch(e){return!1}}async exportAllAccessControl(){try{let e=(await this.db.find({selector:{documentType:"page-access"}})).docs,t=(await this.db.find({selector:{documentType:"user-role"}})).docs;return{pageAccess:e,userRoles:t}}catch(e){throw e}}}},38575:function(e,t,a){a.d(t,{CE:()=>h,H4:()=>c,hM:()=>l,iE:()=>d,tC:()=>o});var i=a(83087),s=a(49807);i.Z.plugin(s.Z);let r={localName:"ithinc_welfare",remoteUrl:"https://couchdb.brisflix.com/ithinc_welfare",remoteName:"ithinc_welfare",cloudflare:{clientId:"",clientSecret:""},username:"ithinc",password:"all the welfare cases"},n={...r},o=async()=>{try{let{secrets:e}=await a.e("751").then(a.bind(a,6348));if(e?.database?.cloudflare?.clientId&&e?.database?.cloudflare?.clientSecret)return n={localName:e.database.localName||r.localName,remoteUrl:e.database.remoteUrl||r.remoteUrl,remoteName:e.database.remoteName||r.remoteName,cloudflare:{clientId:e.database.cloudflare.clientId,clientSecret:e.database.cloudflare.clientSecret},username:e.database.username||r.username,password:e.database.password||r.password}}catch(e){}return n},d=()=>n,l={live:!1,retry:!1,ajax:{timeout:8e3,withCredentials:!0,cache:!1,headers:{Accept:"application/json","Content-Type":"application/json","Cache-Control":"no-cache, no-store, must-revalidate",Pragma:"no-cache"},beforeSend:function(e,t){}},batch_size:3,batches_limit:1,websocket:!1,heartbeat:!1,auto_compaction:!1,revs_limit:5,attachments:!1},c="ithinc_welfare",h={DEBOUNCE_DELAY:2e3,MAX_RETRY_ATTEMPTS:5,RETRY_DELAY_BASE:1e3,CONNECTION_TEST_INTERVAL:12e4,CHANGE_BATCH_TIMEOUT:5e3,AUTH_RETRY_DELAY:5e3,AUTH_MAX_BACKOFF:12e4,AUTH_SUPPRESSION_TIME:3e5,DATABASE_RESET_TIMEOUT:3e4,DATABASE_RESET_RETRY_ATTEMPTS:3}},83502:function(e,t,a){a.d(t,{D:()=>S,R:()=>v});var i=a(83087),s=a(49807),r=a(60434);class n{db;syncManager;constructor(e,t){this.db=e,this.syncManager=t,this.createIndexes()}async createIndexes(){try{"function"==typeof this.db.createIndex&&(await this.db.createIndex({index:{fields:["documentType","festivalId","createdAt","isDeleted"],name:"admission-festival-index"}}),await this.db.createIndex({index:{fields:["documentType","siteLocationId","createdAt","isDeleted"],name:"admission-location-index"}}),await this.db.createIndex({index:{fields:["documentType","status","InBayNow","isDeleted"],name:"admission-status-index"}}))}catch(e){}}isValidDate(e){if(!e)return!1;let t=new Date(e);return t instanceof Date&&!isNaN(t.getTime())}async addAdmission(e){try{let t=`admission_${(0,r.Z)()}`,a=new Date().toISOString(),i={...e,_id:t,documentType:"admission",type:"admission",createdAt:a,timestamp:a,status:"active",AdditionalNotes:e.AdditionalNotes||[],History:e.History||[],FirstName:e.FirstName||"",Surname:e.Surname||"",Gender:e.Gender,Attended:e.Attended||a,BaysOrChairs:e.BaysOrChairs,InBayNow:e.InBayNow||!1,ReferredBy:e.ReferredBy||"",ReasonCategory:e.ReasonCategory||"",SubstanceUsed:e.SubstanceUsed||[],DOB:e.DOB||"",Pronoun:e.Pronoun||"",Ethnicity:e.Ethnicity,ContactName:e.ContactName||"",ContactNumber:e.ContactNumber||"",DischargeTime:e.DischargeTime||"",festivalId:e.festivalId,syncStatus:"sync_pending"},s=await this.db.put(i);return await this.syncManager.syncAfterChange(t),{...i,_rev:s.rev}}catch(e){throw e}}async updateAdmission(e){if(!e._id)throw Error("Cannot update admission without _id");try{let t=await this.db.get(e._id),a=new Date().toISOString(),i={...e,_id:e._id,_rev:t._rev,updatedAt:a,AdditionalNotes:e.AdditionalNotes||[],History:e.History||[],createdAt:t.createdAt,timestamp:t.timestamp,syncStatus:"sync_pending"},s=await this.db.put(i);return await this.syncManager.syncAfterChange(e._id),{...i,_rev:s.rev}}catch(e){throw e}}async getAdmissionsByFestival(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let a="all"===e?{documentType:"admission",...t?{}:{$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}}:{documentType:"admission",festivalId:e,...t?{}:{$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}};return(await this.db.find({selector:a})).docs.map(e=>"admission"===e.documentType&&"admission"===e.type?(this.isValidDate(e.createdAt)||(e.createdAt=this.isValidDate(e.timestamp)?e.timestamp:new Date().toISOString()),e.DischargeTime&&"active"===e.status&&(e.status="discharged",e.InBayNow=!1),e):null).filter(e=>null!==e)}catch(e){throw e}}async getAdmissionsByLocation(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return(await this.db.find({selector:{documentType:"admission",siteLocationId:e,...t?{}:{$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}}})).docs.map(e=>"admission"===e.documentType&&"admission"===e.type?e:null).filter(e=>null!==e)}catch(e){throw e}}async getAdmissionById(e){try{let t=await this.db.get(e);if(t&&"admission"===t.documentType&&"admission"===t.type)return this.isValidDate(t.createdAt)||(t.createdAt=this.isValidDate(t.timestamp)?t.timestamp:new Date().toISOString()),t.DischargeTime&&"active"===t.status&&(t.status="discharged",t.InBayNow=!1),t;return null}catch(e){return null}}async deleteAdmission(e){try{let t=await this.db.get(e),a=new Date().toISOString(),i={...t,isDeleted:!0,deletedAt:a,updatedAt:a,syncStatus:"sync_pending"};await this.db.put(i),await this.syncManager.syncAfterChange(e)}catch(e){throw e}}async cleanupOldAdmissions(e){try{let t=0,a=new Date;a.setMonth(a.getMonth()-6);let i=a.toISOString();for(let e of(await this.db.find({selector:{documentType:"admission",isDeleted:!0,deletedAt:{$lt:i}}})).docs)e._id&&e._rev&&(await this.db.remove(e._id,e._rev),t++);let s=await this.db.find({selector:{documentType:"admission",status:"discharged",createdAt:{$lt:e},$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}}),r=new Date().toISOString();for(let e of s.docs)if(e._id&&e._rev){let a={...e,isDeleted:!0,deletedAt:r,updatedAt:r,syncStatus:"sync_pending"};await this.db.put(a),t++}return t>0&&await this.syncManager.syncAfterChange(),t}catch(e){throw e}}async fixInconsistentAdmissions(){try{for(let e of(await this.db.find({selector:{documentType:"admission"}})).docs){let t=!1,a={...e,type:"admission",documentType:"admission",syncStatus:"sync_pending"};e.DischargeTime&&"active"===e.status&&(a.status="discharged",a.InBayNow=!1,t=!0),e.DischargeTime||"discharged"!==e.status||(a.status="active",t=!0),!e.siteLocationId&&e.Location&&(a.siteLocationId="",a.siteLocationName="",a.siteLocationType="",t=!0),t&&await this.updateAdmission(a)}}catch(e){throw e}}async exportAllAdmissions(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{return(await this.db.find({selector:{documentType:"admission",...e?{}:{$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}}})).docs.map(e=>"admission"===e.documentType&&"admission"===e.type?(this.isValidDate(e.createdAt)||(e.createdAt=this.isValidDate(e.timestamp)?e.timestamp:new Date().toISOString()),e.DischargeTime&&"active"===e.status&&(e.status="discharged",e.InBayNow=!1),e):null).filter(e=>null!==e)}catch(e){throw e}}}class o{db;syncManager;constructor(e,t){this.db=e,this.syncManager=t}async getFestivals(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{if("function"!=typeof this.db.createIndex)return(await this.db.allDocs({include_docs:!0,startkey:"festival_",endkey:"festival_￰"})).rows.map(t=>{let a=t.doc;return a&&"festival"===a.documentType&&("festival"===a.type||"regular_event"===a.type)?e||!0!==a.isDeleted?{_id:a._id,_rev:a._rev,name:a.name,startDate:a.startDate,endDate:a.endDate,isActive:a.isActive,documentType:a.documentType,type:a.type,locations:a.locations,hasMultipleLocations:a.hasMultipleLocations,showAdmissions:a.showAdmissions,showFrontOfHouse:a.showFrontOfHouse,showLostProperty:a.showLostProperty,showShifts:a.showShifts}:null:null}).filter(e=>null!==e);{await this.db.createIndex({index:{fields:["documentType","isDeleted"],name:"festival_documentType_index"}});let t={documentType:"festival",$or:[{type:"festival"},{type:"regular_event"}]};return e||(t.$and=[{$or:[{type:"festival"},{type:"regular_event"}]},{$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}],delete t.$or),(await this.db.find({selector:t,fields:["_id","name","startDate","endDate","isActive","documentType","type","locations","hasMultipleLocations","showAdmissions","showFrontOfHouse","showLostProperty","showShifts"],sort:[{_id:"asc"}]})).docs}}catch(e){throw e}}async addFestival(e){let t=`festival_${(0,r.Z)()}`,a={...e,_id:t,documentType:"festival",type:e.type||"regular_event",syncStatus:"local_only"},i=await this.db.put(a);return await this.syncManager.syncAfterChange(),{...a,_rev:i.rev}}async updateFestival(e){let t=await this.db.get(e._id),a={...t,...e,_rev:t._rev},i=await this.db.put(a);return await this.syncManager.syncAfterChange(),{...a,_rev:i.rev}}async deleteFestival(e){try{let t=new Date().toISOString(),a={...await this.db.get(e),isDeleted:!0,deletedAt:t,updatedAt:t,syncStatus:"sync_pending"};for(let i of(await this.db.put(a),(await this.db.find({selector:{documentType:"admission",festivalId:e,$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}})).docs)){let e={...i,isDeleted:!0,deletedAt:t,updatedAt:t,syncStatus:"sync_pending"};await this.db.put(e)}for(let a of(await this.db.find({selector:{documentType:"item",festivalId:e,$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}})).docs){let e={...a,isDeleted:!0,deletedAt:t,updatedAt:t,syncStatus:"sync_pending"};await this.db.put(e)}for(let a of(await this.db.find({selector:{documentType:"shift_assignment",festivalId:e,$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}})).docs){let e={...a,isDeleted:!0,deletedAt:t,updatedAt:t,syncStatus:"sync_pending"};await this.db.put(e)}try{let a=`notes_${e}`,i={...await this.db.get(a),isDeleted:!0,deletedAt:t,updatedAt:t,syncStatus:"sync_pending"};await this.db.put(i)}catch(e){if(404!==e.status)throw e}for(let a of(await this.db.find({selector:{documentType:"knowledgeBase",festivalId:e,showForAllFestivals:{$ne:!0},$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}})).docs){let e={...a,isDeleted:!0,deletedAt:t,updatedAt:t,syncStatus:"sync_pending"};await this.db.put(e)}await this.syncManager.syncAfterChange()}catch(e){throw e}}async getFestivalNotes(e){let t=`notes_${e}`;try{return(await this.db.get(t)).notes}catch(e){if(404===e.status)return"";throw e}}async updateFestivalNotes(e,t){let a=`notes_${e}`,i={_id:a,documentType:"festival_notes",type:"festival_notes",festivalId:e,notes:t,lastUpdated:new Date().toISOString(),syncStatus:"local_only"};try{let e=await this.db.get(a),t={...e,...i,_rev:e._rev};await this.db.put(t)}catch(e){if(404===e.status)await this.db.put(i);else throw e}await this.syncManager.syncAfterChange()}async exportAllFestivals(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{if("function"!=typeof this.db.createIndex)return(await this.db.allDocs({include_docs:!0,startkey:"festival_",endkey:"festival_￰"})).rows.map(e=>e.doc).filter(t=>!!t&&"festival"===t.documentType&&(!!e||!0!==t.isDeleted));{await this.db.createIndex({index:{fields:["documentType","isDeleted"],name:"festival_documentType_index"}});let t={documentType:"festival",$or:[{type:"festival"},{type:"regular_event"}]};return e||(t.$and=[{$or:[{type:"festival"},{type:"regular_event"}]},{$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}],delete t.$or),(await this.db.find({selector:t,sort:[{_id:"asc"}]})).docs}}catch(e){throw e}}}class d{db;syncManager;constructor(e,t){this.db=e,this.syncManager=t,this.createIndexes()}async createIndexes(){try{"function"==typeof this.db.createIndex&&await this.db.createIndex({index:{fields:["documentType","type","festivalId","siteLocationId","timestamp","isDeleted"],name:"item_index"}})}catch(e){}}async getTodayDocument(e,t){let a=new Date;a.setHours(0,0,0,0);let i=new Date;i.setHours(23,59,59,999);try{let s={documentType:"item",type:"item",festivalId:e,timestamp:{$gte:a.toISOString(),$lte:i.toISOString()},$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]};return t&&(s.siteLocationId=t),(await this.db.find({selector:s})).docs[0]||null}catch(e){throw e}}async addOrUpdateItemCount(e,t,a){let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:1,s=new Date().toISOString(),n=await this.getTodayDocument(t,a);if(n){let t="number"==typeof n[e]?Number(n[e]):0,a={...n,[e]:Number(t)+i,timestamp:s,syncStatus:"sync_pending"};try{let e=await this.db.put(a);return await this.syncManager.syncAfterChange(),{...a,_rev:e.rev}}catch(e){throw e}}{let n={_id:`item_${(0,r.Z)()}`,documentType:"item",type:"item",festivalId:t,siteLocationId:a,timestamp:s,syncStatus:"sync_pending",Suncream:0,Poncho:0,Water:0,SanitaryProducts:0,Earplugs:0,Condoms:0,ChildrensWristbands:0,GeneralWristbands:0,Charging:0,Sanitizer:0,ToiletRoll:0,GeneralEnqs:0,HotWater:0,RestAndRecuperation:0,Other:""};"Other"===e?n[e]="":n[e]=i;try{let e=await this.db.put(n);return await this.syncManager.syncAfterChange(),{...n,_rev:e.rev}}catch(e){throw e}}}async getItemCountsByFestival(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let a={selector:{documentType:"item",type:"item",festivalId:e,...t?{}:{$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}}};return(await this.db.find(a)).docs.filter(e=>"item"===e.documentType&&"item"===e.type)}catch(e){throw e}}async updateItemCount(e){try{let t=await this.db.get(e._id),a={...t,...e,_rev:t._rev,syncStatus:"sync_pending"},i=await this.db.put(a);return await this.syncManager.syncAfterChange(),{...a,_rev:i.rev}}catch(e){throw e}}async deleteItemCount(e){try{let t=await this.db.get(e),a=new Date().toISOString(),i={...t,isDeleted:!0,deletedAt:a,updatedAt:a,syncStatus:"sync_pending"};await this.db.put(i),await this.syncManager.syncAfterChange()}catch(e){throw e}}async cleanupOldItems(e){try{let t=0,a=new Date;a.setMonth(a.getMonth()-6);let i=a.toISOString();for(let e of(await this.db.find({selector:{documentType:"item",type:"item",isDeleted:!0,deletedAt:{$lt:i}},use_index:["item_index"]})).docs)e._id&&e._rev&&(await this.db.remove(e._id,e._rev),t++);let s={selector:{documentType:"item",type:"item",timestamp:{$lt:e},$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]},use_index:["item_index"]},r=await this.db.find(s),n=new Date().toISOString();for(let e of r.docs)if(e._id&&e._rev){let a={...e,isDeleted:!0,deletedAt:n,updatedAt:n,syncStatus:"sync_pending"};await this.db.put(a),t++}return t>0&&await this.syncManager.syncAfterChange(),t}catch(e){throw e}}async exportAllItems(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{let t={documentType:"item",type:"item"};return e||(t.$or=[{isDeleted:{$exists:!1}},{isDeleted:!1}]),(await this.db.find({selector:t})).docs.filter(e=>"item"===e.documentType&&"item"===e.type)}catch(e){throw e}}}class l{db;syncManager;constructor(e,t){this.db=e,this.syncManager=t}async addLostPropertyItem(e){let t=`lost_property_${(0,r.Z)()}`,a=new Date().toISOString(),i={...e,_id:t,timestamp:a,documentType:"lost_property",type:"lost_property",status:e.status||"unclaimed"},s=await this.db.put(i);return await this.syncManager.syncAfterChange(),{...i,_rev:s.rev}}async getLostPropertyItems(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e)try{let a={documentType:"lost_property",type:"lost_property",festivalId:e};return t||(a.$or=[{isDeleted:{$exists:!1}},{isDeleted:!1}]),(await this.db.find({selector:a})).docs.filter(e=>"lost_property"===e.documentType&&"lost_property"===e.type)}catch(e){throw e}{let e=(await this.db.allDocs({include_docs:!0,startkey:"lost_property_",endkey:"lost_property_￰"})).rows.filter(e=>void 0!==e.doc).map(e=>{let t=e.doc;return"lost_property"===t.documentType&&"lost_property"===t.type?t:null}).filter(e=>null!==e);return t?e:e.filter(e=>!e.isDeleted)}}async updateLostPropertyItem(e){let t=await this.db.get(e._id),a={...t,...e,_rev:t._rev},i=await this.db.put(a);return await this.syncManager.syncAfterChange(),{...a,_rev:i.rev}}async deleteLostPropertyItem(e){let t=await this.db.get(e),a=new Date().toISOString(),i={...t,isDeleted:!0,deletedAt:a,updatedAt:a,syncStatus:"sync_pending"};await this.db.put(i),await this.syncManager.syncAfterChange()}async cleanupOldLostProperty(e){try{let t=0,a=new Date;a.setMonth(a.getMonth()-6);let i=a.toISOString();for(let e of(await this.db.find({selector:{documentType:"lost_property",type:"lost_property",isDeleted:!0,deletedAt:{$lt:i}}})).docs)e._id&&e._rev&&(await this.db.remove(e._id,e._rev),t++);let s={selector:{documentType:"lost_property",type:"lost_property",timestamp:{$lt:e},$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}},r={selector:{documentType:"lost_property",type:"lost_property",status:"claimed",timestamp:{$lt:e},$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}},[n,o]=await Promise.all([this.db.find(s),this.db.find(r)]),d=new Map;[...n.docs,...o.docs].filter(e=>"lost_property"===e.documentType&&"lost_property"===e.type).forEach(e=>{d.set(e._id,e)});let l=new Date().toISOString();for(let e of Array.from(d.values()))if(e._id&&e._rev){let a={...e,isDeleted:!0,deletedAt:l,updatedAt:l,syncStatus:"sync_pending"};await this.db.put(a),t++}return t>0&&await this.syncManager.syncAfterChange(),t}catch(e){throw e}}async exportAllLostProperty(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{let t={documentType:"lost_property",type:"lost_property"};return e||(t.$or=[{isDeleted:{$exists:!1}},{isDeleted:!1}]),(await this.db.find({selector:t})).docs.filter(e=>"lost_property"===e.documentType&&"lost_property"===e.type)}catch(e){throw e}}}class c{db;syncManager;constructor(e,t){this.db=e,this.syncManager=t}async getShiftConfig(e){let t=`shift_config_${e}`;try{return await this.db.get(t)}catch(e){if(404===e.status)return null;throw e}}async saveShiftConfig(e){let t=`shift_config_${e.festivalId}`,a={...e,_id:t,documentType:"shift_config",type:"shift_config",syncStatus:"local_only"};try{let e=await this.db.get(t),i={...e,...a,_rev:e._rev},s=await this.db.put(i);return{...i,_rev:s.rev}}catch(e){if(404===e.status){let e=await this.db.put(a);return{...a,_rev:e.rev}}throw e}}async getTeamLeaders(e){try{return(await this.db.allDocs({include_docs:!0,startkey:`team_leader_${e}_`,endkey:`team_leader_${e}_\ufff0`})).rows.filter(e=>void 0!==e.doc).map(e=>e.doc)}catch(e){throw e}}async addTeamLeader(e){let t=`team_leader_${e.festivalId}_${(0,r.Z)()}`,a={...e,_id:t,documentType:"team_leader",type:"team_leader",teams:e.teams||[],syncStatus:"local_only"},i=await this.db.put(a);return await this.syncManager.syncAfterChange(),{...a,_rev:i.rev}}async updateTeamLeader(e){let t=await this.db.get(e._id),a={...t,...e,_rev:t._rev},i=await this.db.put(a);return await this.syncManager.syncAfterChange(),{...a,_rev:i.rev}}async deleteTeamLeader(e){let t=await this.db.get(e);await this.db.remove(t),await this.syncManager.syncAfterChange()}async getShiftAssignments(e){try{return(await this.db.allDocs({include_docs:!0,startkey:`shift_assignment_${e}_`,endkey:`shift_assignment_${e}_\ufff0`})).rows.filter(e=>void 0!==e.doc).map(e=>e.doc).sort((e,t)=>{let a=e.date.localeCompare(t.date);return 0!==a?a:e.shiftNumber-t.shiftNumber})}catch(e){throw e}}async addShiftAssignment(e){let t=`shift_assignment_${e.festivalId}_${(0,r.Z)()}`,a={...e,_id:t,documentType:"shift_assignment",type:"shift_assignment",syncStatus:"local_only"},i=await this.db.put(a);return await this.syncManager.syncAfterChange(),{...a,_rev:i.rev}}async updateShiftAssignment(e){let t=await this.db.get(e._id),a={...t,...e,_rev:t._rev},i=await this.db.put(a);return await this.syncManager.syncAfterChange(),{...a,_rev:i.rev}}async deleteShiftAssignment(e){let t=await this.db.get(e);await this.db.remove(t),await this.syncManager.syncAfterChange()}async generateShiftSchedule(e,t,a,i){let s=[],r=new Date(t),n=new Date(a);if(!this.validateShiftTimes(i.firstShiftStart,"00:00"))return s;for(let t=new Date(r);t<=n;t.setDate(t.getDate()+1))for(let a=1;a<=i.shiftsPerDay;a++){let r=this.calculateShiftTimes(i.firstShiftStart,i.shiftDuration,a);if(!this.validateShiftTimes(r.start,r.end))continue;let n=String.fromCharCode(65+(a-1)),o={type:"shift_assignment",documentType:"shift_assignment",festivalId:e,date:t.toISOString().split("T")[0],shiftNumber:a,startTime:r.start,endTime:r.end,teamLeaderId:"",teamLetter:n,teamMembers:[],syncStatus:"local_only"};try{let e=await this.addShiftAssignment(o);s.push(e)}catch(e){}}return s}calculateShiftTimes(e,t,a){try{let[i,s]=e.split(":").map(Number);if(isNaN(i)||isNaN(s))return{start:"00:00",end:"00:00"};let r=new Date;r.setHours(i+(a-1)*t,s,0);let n=new Date(r);return n.setHours(r.getHours()+t),{start:r.toTimeString().slice(0,5),end:n.toTimeString().slice(0,5)}}catch(e){return{start:"00:00",end:"00:00"}}}validateShiftTimes(e,t){let a=/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;return a.test(e)&&a.test(t)}async cleanupOldShifts(e){try{let t=0,a=new Date(e);for(let e of(await this.db.allDocs({include_docs:!0,startkey:"shift_assignment_",endkey:"shift_assignment_￰"})).rows.filter(e=>void 0!==e.doc).map(e=>e.doc).filter(e=>new Date(e.date)<a))e._id&&e._rev&&(await this.db.remove(e._id,e._rev),t++);let i=await this.db.allDocs({include_docs:!0,startkey:"team_leader_",endkey:"team_leader_￰"}),s=await this.db.allDocs({include_docs:!0,startkey:"shift_assignment_",endkey:"shift_assignment_￰"}),r=new Set(s.rows.filter(e=>void 0!==e.doc).map(e=>e.doc.teamLeaderId).filter(e=>e));for(let e of i.rows){let a=e.doc;a&&a._id&&a._rev&&!r.has(a._id)&&(await this.db.remove(a._id,a._rev),t++)}return t>0&&await this.syncManager.syncAfterChange(),t}catch(e){throw e}}async exportAllShifts(){try{let e=(await this.db.find({selector:{documentType:"shift_config"}})).docs,t=(await this.db.find({selector:{documentType:"team_leader"}})).docs,a=(await this.db.find({selector:{documentType:"shift_assignment"}})).docs;return{configs:e,teamLeaders:t,assignments:a}}catch(e){throw e}}}class h{db;syncManager;constructor(e,t){this.db=e,this.syncManager=t,this.createIndexes()}async createIndexes(){try{"function"==typeof this.db.createIndex&&await this.db.createIndex({index:{fields:["type","documentType","timestamp","resolved","isDeleted"],name:"feedback_index"}})}catch(e){}}async addFeedback(e){let t=new Date().toISOString(),a={_id:`feedback_${(0,r.Z)()}`,type:"feedback",documentType:"feedback",syncStatus:"sync_pending",name:e.name,page:e.page,feedback:e.feedback,timestamp:t,createdAt:t,updatedAt:t,resolved:!1};try{let e=await this.db.put(a);return await this.syncManager.syncAfterChange(),{...a,_rev:e.rev}}catch(e){throw e}}async getFeedback(e){try{return await this.db.get(e)}catch(e){throw e}}isFeedbackDocument(e){return e&&"string"==typeof e.documentType&&"feedback"===e.documentType&&"string"==typeof e.type&&"feedback"===e.type}async getAllFeedback(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{let t={type:"feedback",documentType:"feedback"};return e||(t.$or=[{isDeleted:{$exists:!1}},{isDeleted:!1}]),(await this.db.find({selector:t,sort:[{timestamp:"desc"}],use_index:["feedback_index"]})).docs.filter(this.isFeedbackDocument).sort((e,t)=>{let a=new Date(e.timestamp||0);return new Date(t.timestamp||0).getTime()-a.getTime()})}catch(t){try{let t=await this.db.allDocs({include_docs:!0,startkey:"feedback_",endkey:"feedback_￰"}),a=[];for(let i of t.rows)i.doc&&this.isFeedbackDocument(i.doc)&&(e||!i.doc.isDeleted)&&a.push(i.doc);return a.sort((e,t)=>{let a=new Date(e.timestamp||0);return new Date(t.timestamp||0).getTime()-a.getTime()})}catch(e){throw t}}}async getUnresolvedFeedback(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{let t={type:"feedback",documentType:"feedback",resolved:!1};return e||(t.$and=[{$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}]),(await this.db.find({selector:t,sort:[{timestamp:"desc"}],use_index:["feedback_index"]})).docs.filter(this.isFeedbackDocument).sort((e,t)=>{let a=new Date(e.timestamp||0);return new Date(t.timestamp||0).getTime()-a.getTime()})}catch(e){throw e}}async updateFeedbackStatus(e,t){try{let a={...await this.getFeedback(e),resolved:t,syncStatus:"sync_pending",updatedAt:new Date().toISOString()},i=await this.db.put(a);return await this.syncManager.syncAfterChange(),{...a,_rev:i.rev}}catch(e){throw e}}async deleteFeedback(e){try{let t=await this.db.get(e),a=new Date().toISOString(),i={...t,isDeleted:!0,deletedAt:a,updatedAt:a,syncStatus:"sync_pending"};await this.db.put(i),await this.syncManager.syncAfterChange()}catch(e){throw e}}async cleanupOldFeedback(e){try{let t=0,a=new Date;a.setMonth(a.getMonth()-6);let i=a.toISOString();for(let e of(await this.db.find({selector:{type:"feedback",documentType:"feedback",isDeleted:!0,deletedAt:{$lt:i}},use_index:["feedback_index"]})).docs)e._id&&e._rev&&(await this.db.remove(e._id,e._rev),t++);let s={selector:{type:"feedback",documentType:"feedback",timestamp:{$lt:e},$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]},use_index:["feedback_index"]},r={selector:{type:"feedback",documentType:"feedback",resolved:!0,timestamp:{$lt:e},$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]},use_index:["feedback_index"]},[n,o]=await Promise.all([this.db.find(s),this.db.find(r)]),d=new Map;[...n.docs,...o.docs].filter(this.isFeedbackDocument).forEach(e=>{d.set(e._id,e)});let l=new Date().toISOString();for(let e of Array.from(d.values()))if(e._id&&e._rev){let a={...e,isDeleted:!0,deletedAt:l,updatedAt:l,syncStatus:"sync_pending"};await this.db.put(a),t++}return t>0&&await this.syncManager.syncAfterChange(),t}catch(e){throw e}}async exportAllFeedback(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{let t={type:"feedback",documentType:"feedback"};return e||(t.$or=[{isDeleted:{$exists:!1}},{isDeleted:!1}]),(await this.db.find({selector:t,sort:[{timestamp:"desc"}],use_index:["feedback_index"]})).docs.filter(this.isFeedbackDocument).sort((e,t)=>{let a=new Date(e.timestamp||0);return new Date(t.timestamp||0).getTime()-a.getTime()})}catch(e){throw e}}}var u=a(23279),y=a.n(u);class g{db;syncManager;debouncedSync;DEBOUNCE_DELAY;constructor(e,t){this.db=e,this.DEBOUNCE_DELAY=2e3,this.syncManager=t,this.debouncedSync=y()(this.syncChanges.bind(this),this.DEBOUNCE_DELAY)}async syncChanges(e){await this.syncManager.syncAfterChange(e)}async listAdmissionsForBay(e){try{return(await v.getAdmissionsByFestival("all")).filter(t=>t.Location===e)}catch(e){throw e}}async checkBayAvailability(e){try{let t=(await this.listAdmissionsForBay(e)).find(e=>"Bay"===e.BaysOrChairs&&e.InBayNow&&"active"===e.status);if(t)return{isOccupied:!0,occupiedBy:{name:`${t.FirstName} ${t.Surname}`,admissionId:t._id},message:`Bay ${e} is currently occupied by ${t.FirstName} ${t.Surname}`};return{isOccupied:!1,message:`Bay ${e} is available`}}catch(e){throw e}}async validateBayAssignment(e,t){try{let a=await this.checkBayAvailability(e);return!a.isOccupied||a.occupiedBy?.admissionId===t}catch(e){throw e}}async assignToBay(e,t){try{let a={...await this.db.get(e),Location:t,InBayNow:!0,syncStatus:"sync_pending",updatedAt:new Date().toISOString()};await this.db.put(a),this.debouncedSync(e)}catch(e){throw e}}async removeFromBay(e){try{let t={...await this.db.get(e),InBayNow:!1,syncStatus:"sync_pending",updatedAt:new Date().toISOString()};await this.db.put(t),this.debouncedSync(e)}catch(e){throw e}}cleanup(){this.debouncedSync&&this.debouncedSync.cancel()}}class p{db;syncManager;constructor(e,t){this.db=e,this.syncManager=t}async getKnowledgeBaseItems(e){if(!e)return[];try{return(await this.db.allDocs({include_docs:!0})).rows.map(e=>e.doc).filter(t=>t&&"knowledgeBase"===t.documentType&&(t.festivalId===e||!0===t.showForAllFestivals)).sort((e,t)=>e.category<t.category?-1:e.category>t.category?1:e.title<t.title?-1:+(e.title>t.title))}catch(e){return[]}}async getKnowledgeBaseCategories(e){try{let t=await this.getKnowledgeBaseItems(e),a=new Set;if(0===t.length)return["Substance Info","Mental Health","Support Contacts"];return t.forEach(e=>{e.category&&a.add(e.category)}),Array.from(a).sort()}catch(e){return["Substance Info","Mental Health","Support Contacts"]}}async getSubcategoriesForCategory(e,t){try{let a=await this.getKnowledgeBaseItems(e),i=new Set;return a.forEach(e=>{e.category===t&&e.subcategory&&i.add(e.subcategory)}),Array.from(i).sort()}catch(e){return[]}}async addKnowledgeBaseItem(e){try{let t=`knowledgebase_${(0,r.Z)()}`,a=new Date().toISOString(),i={...e,_id:t,documentType:"knowledgeBase",createdAt:a,syncStatus:"sync_pending"},s=await this.db.put(i);return await this.syncManager.syncAfterChange(t),{...i,_rev:s.rev}}catch(e){throw e}}async updateKnowledgeBaseItem(e){try{if(!e._id)throw Error("Cannot update knowledge base item without _id");let t=await this.db.get(e._id),a={...e,_rev:t._rev,updatedAt:new Date().toISOString(),syncStatus:"sync_pending"},i=await this.db.put(a);return await this.syncManager.syncAfterChange(e._id),{...a,_rev:i.rev}}catch(e){throw e}}async deleteKnowledgeBaseItem(e){try{let t=await this.db.get(e);await this.db.remove(t._id,t._rev),await this.syncManager.syncAfterChange(e)}catch(e){throw e}}async exportAllKnowledgeBase(){try{return(await this.db.find({selector:{documentType:"knowledge_base"}})).docs}catch(e){throw e}}}class m{db;syncManager;constructor(e,t){this.db=e,this.syncManager=t,this.createIndexes()}async createIndexes(){try{"function"==typeof this.db.createIndex&&(await this.db.createIndex({index:{fields:["documentType","festivalId","visitTimestamp","isDeleted"],name:"sensory-hub-festival-index"}}),await this.db.createIndex({index:{fields:["documentType","siteLocationId","visitTimestamp","isDeleted"],name:"sensory-hub-location-index"}}),await this.db.createIndex({index:{fields:["documentType","userType","purpose","visitTimestamp","isDeleted"],name:"sensory-hub-type-index"}}))}catch(e){}}validateVisitData(e){if("crew"===e.userType&&!e.teamName)throw Error("Team name is required for crew visits")}async addVisit(e){try{this.validateVisitData(e);let t=`sensory-hub-visit_${(0,r.Z)()}`,a=new Date().toISOString(),i={...e,_id:t,documentType:"sensory-hub-visit",type:"sensory-hub-visit",createdAt:a,timestamp:a,syncStatus:"sync_pending"},s=await this.db.put(i);return await this.syncManager.syncAfterChange(t),{...i,_rev:s.rev}}catch(e){throw e}}async getVisitsByFestival(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{let a="all"===e?{documentType:"sensory-hub-visit",...t?{}:{$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}}:{documentType:"sensory-hub-visit",festivalId:e,...t?{}:{$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}};return(await this.db.find({selector:a})).docs.map(e=>"sensory-hub-visit"===e.documentType&&"sensory-hub-visit"===e.type?e:null).filter(e=>null!==e).sort((e,t)=>new Date(t.visitTimestamp).getTime()-new Date(e.visitTimestamp).getTime())}catch(e){throw e}}async getVisitsByLocation(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];try{return(await this.db.find({selector:{documentType:"sensory-hub-visit",siteLocationId:e,...t?{}:{$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}}})).docs.map(e=>"sensory-hub-visit"===e.documentType&&"sensory-hub-visit"===e.type?e:null).filter(e=>null!==e).sort((e,t)=>new Date(t.visitTimestamp).getTime()-new Date(e.visitTimestamp).getTime())}catch(e){throw e}}async updateVisit(e){if(!e._id)throw Error("Cannot update sensory hub visit without _id");try{this.validateVisitData(e);let t=await this.db.get(e._id),a=new Date().toISOString(),i={...e,_id:e._id,_rev:t._rev,updatedAt:a,createdAt:t.createdAt,timestamp:t.timestamp,syncStatus:"sync_pending"},s=await this.db.put(i);return await this.syncManager.syncAfterChange(e._id),{...i,_rev:s.rev}}catch(e){throw e}}async deleteVisit(e){try{let t=await this.db.get(e),a=new Date().toISOString(),i={...t,isDeleted:!0,deletedAt:a,updatedAt:a,syncStatus:"sync_pending"};await this.db.put(i),await this.syncManager.syncAfterChange(e)}catch(e){throw e}}async cleanupOldVisits(e){try{let t=0,a=new Date;a.setMonth(a.getMonth()-6);let i=a.toISOString();for(let e of(await this.db.find({selector:{documentType:"sensory-hub-visit",isDeleted:!0,deletedAt:{$lt:i}}})).docs)e._id&&e._rev&&(await this.db.remove(e._id,e._rev),t++);let s=await this.db.find({selector:{documentType:"sensory-hub-visit",visitTimestamp:{$lt:e},$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}}),r=new Date().toISOString();for(let e of s.docs)if(e._id&&e._rev){let a={...e,isDeleted:!0,deletedAt:r,updatedAt:r,syncStatus:"sync_pending"};await this.db.put(a),t++}return t>0&&await this.syncManager.syncAfterChange(),t}catch(e){throw e}}async exportAllVisits(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{return(await this.db.find({selector:{documentType:"sensory-hub-visit",...e?{}:{$or:[{isDeleted:{$exists:!1}},{isDeleted:!1}]}}})).docs.map(e=>"sensory-hub-visit"===e.documentType&&"sensory-hub-visit"===e.type?e:null).filter(e=>null!==e).sort((e,t)=>new Date(t.visitTimestamp).getTime()-new Date(e.visitTimestamp).getTime())}catch(e){throw e}}}var f=a(3065),b=a(38575);class w{db;remoteDb;syncHandler;syncListeners=new Set;syncStatus={status:"disconnected",pendingChanges:0,authError:!1};retryAttempts=0;lastConnectionTest=0;syncInProgress=!1;isInitialized=!1;changeQueue=new Map;processingQueue=!1;debouncedSync;authRetryState={attempts:0,lastAttempt:0,backoffDelay:5e3,isAuthError:!1};suppressAuthErrors=!1;constructor(e){this.db=e,this.db.setMaxListeners&&this.db.setMaxListeners(20);let{remoteUrl:t,remoteName:a,cloudflare:s,username:r,password:n}=(0,b.iE)(),o=t.replace(/\/$/,""),d=`${o}/${a}`;s.clientId&&s.clientSecret&&(s.clientId.includes(".access"),s.clientSecret.length);let l=r&&n?"Basic "+btoa(`${r}:${n}`):void 0,c={...b.hM,auth:{username:r,password:n},ajax:{...b.hM.ajax,headers:{...b.hM.ajax.headers,"CF-Access-Client-Id":s.clientId,"CF-Access-Client-Secret":s.clientSecret,...l?{Authorization:l}:{}}}};try{this.remoteDb=new i.Z(d,c),this.remoteDb.setMaxListeners&&this.remoteDb.setMaxListeners(20),this.initializeSync()}catch(t){let e=String(t);e.includes("<!DOCTYPE")||e.includes("<html")?this.updateSyncStatus("error","Authentication error: Received HTML instead of JSON. Please check Cloudflare Access credentials."):this.updateSyncStatus("error",e)}this.debouncedSync=y()(this.processChangeQueue.bind(this),b.CE.DEBOUNCE_DELAY)}updateSyncStatus(e,t){let a=arguments.length>2&&void 0!==arguments[2]&&arguments[2];this.syncStatus={status:e,lastSync:"synced"===e?new Date:this.syncStatus.lastSync,pendingChanges:this.changeQueue.size,error:t,authError:a},this.notifyListeners()}isAuthenticationError(e){let t=String(e);return t.includes("<!DOCTYPE")||t.includes("<html")||t.includes("530")||t.includes("Authentication failed")||t.includes("blocked by CORS policy")||t.includes("Unexpected token")||e?.status===530}async handleAuthError(e){let t=Date.now();this.authRetryState.isAuthError=!0,this.authRetryState.attempts<3&&this.suppressAuthErrors,this.authRetryState.attempts>=3&&(this.suppressAuthErrors=!0,setTimeout(()=>{this.suppressAuthErrors=!1,this.authRetryState.attempts=0},3e5)),this.authRetryState.attempts++,this.authRetryState.lastAttempt=t,this.authRetryState.backoffDelay=Math.min(2*this.authRetryState.backoffDelay,12e4),this.updateSyncStatus("auth_error","Authentication failed. App running in offline mode.",!0)}shouldRetryAuth(){return Date.now()-this.authRetryState.lastAttempt>=this.authRetryState.backoffDelay}resetAuthRetryState(){this.authRetryState={attempts:0,lastAttempt:0,backoffDelay:5e3,isAuthError:!1},this.suppressAuthErrors=!1}async initializeSync(){!this.isInitialized&&this.remoteDb&&(this.isInitialized=!0,this.updateSyncStatus("synced"),setTimeout(async()=>{try{await this.testConnection(),this.resetAuthRetryState(),await this.checkForLocalData()?this.updateSyncStatus("synced"):this.performInitialSync().catch(e=>{}),this.notifyListeners()}catch(e){if(this.isAuthenticationError(e))return void await this.handleAuthError(e);{let t=String(e);this.updateSyncStatus("error",t)}}},100))}async checkForLocalData(){try{return(await this.db.allDocs({limit:1,include_docs:!1,startkey:"\0",endkey:"￰"})).rows.length>0}catch(e){return!1}}async performInitialSync(){try{this.updateSyncStatus("syncing"),await this.sync();let e=await this.checkForLocalData();this.updateSyncStatus("synced"),this.notifyListeners(),"undefined"!=typeof window&&window.dispatchEvent(new CustomEvent("initialSyncComplete",{detail:{hasData:e}}))}catch(e){throw this.updateSyncStatus("error",String(e)),e}}shouldTestConnection(){return Date.now()-this.lastConnectionTest>=b.CE.CONNECTION_TEST_INTERVAL}async testConnection(){if(!this.remoteDb)throw Error("No remote database configured");let e=(0,b.iE)(),{remoteUrl:t,remoteName:a,cloudflare:i}=e,s=`${t}/${a}`;try{let t=e.username&&e.password?"Basic "+btoa(`${e.username}:${e.password}`):void 0,a={Accept:"application/json","Content-Type":"application/json","CF-Access-Client-Id":i.clientId,"CF-Access-Client-Secret":i.clientSecret};t&&(a.Authorization=t);let r=await fetch(s,{method:"GET",headers:a});r.ok||await r.text()}catch(e){}try{await this.remoteDb.info(),this.lastConnectionTest=Date.now(),this.retryAttempts=0,this.resetAuthRetryState()}catch(e){if(this.isAuthenticationError(e))throw Error("Authentication error: Cloudflare Access authentication failed.");throw e}}async retryConnection(){if(this.authRetryState.isAuthError&&!this.shouldRetryAuth())return;if(this.retryAttempts>=b.CE.MAX_RETRY_ATTEMPTS){this.suppressAuthErrors,this.retryAttempts=0;return}let e=Math.min(b.CE.RETRY_DELAY_BASE*Math.pow(1.5,this.retryAttempts),3e5);this.retryAttempts++,await new Promise(t=>setTimeout(t,e));try{await this.initializeSync()}catch(e){this.isAuthenticationError(e)||await this.retryConnection()}}async processChangeQueue(){if(!this.processingQueue&&0!==this.changeQueue.size&&(!this.authRetryState.isAuthError||this.shouldRetryAuth())){this.processingQueue=!0,this.updateSyncStatus("syncing");try{this.shouldTestConnection()&&await this.testConnection(),Array.from(this.changeQueue.values()),this.changeQueue.clear(),await this.sync(),this.authRetryState.isAuthError&&this.resetAuthRetryState(),this.updateSyncStatus("synced")}catch(e){if(this.isAuthenticationError(e))await this.handleAuthError(e);else{this.suppressAuthErrors;let t=Date.now();Array.from(this.changeQueue.values()).forEach(e=>{e.retryCount<b.CE.MAX_RETRY_ATTEMPTS&&this.changeQueue.set(e.id,{...e,timestamp:t,retryCount:e.retryCount+1})}),this.updateSyncStatus("error",String(e))}}finally{this.processingQueue=!1}}}addSyncListener(e){return this.syncListeners.add(e),()=>{this.syncListeners.delete(e)}}getSyncStatus(){return this.syncStatus}hasPendingChanges(){return this.changeQueue.size>0}async sync(){if(!this.syncInProgress&&this.remoteDb&&(!this.authRetryState.isAuthError||this.shouldRetryAuth())){this.syncInProgress=!0,this.updateSyncStatus("syncing");try{this.syncHandler&&(this.syncHandler.cancel(),this.syncHandler=void 0);let e=await this.db.sync(this.remoteDb,{...b.hM,live:!1});"complete"!==e.status||e.errors?.length?(this.suppressAuthErrors,this.updateSyncStatus("synced")):(this.updateSyncStatus("synced"),this.retryAttempts=0,this.resetAuthRetryState())}catch(t){let e=t instanceof Error?t.message:String(t);if(this.isAuthenticationError(t))return void await this.handleAuthError(t);e.includes("502")||this.suppressAuthErrors,this.updateSyncStatus("error",e),(e.includes("Failed to fetch")||e.includes("NetworkError")||e.includes("network error")||e.includes("502"))&&await this.retryConnection()}finally{this.syncInProgress=!1}}}notifyListeners(){Array.from(this.syncListeners).forEach(e=>{try{e()}catch(e){}})}async syncAfterChange(e){let t=Date.now();e&&this.changeQueue.set(e,{id:e,timestamp:t,retryCount:0}),this.updateSyncStatus(this.syncStatus.status),this.debouncedSync()}async handlePostResetSync(){try{this.syncInProgress=!1,this.changeQueue.clear(),this.processingQueue=!1,this.retryAttempts=0,this.resetAuthRetryState(),this.updateSyncStatus("initial_sync"),await this.sync()}catch(e){this.isAuthenticationError(e)?await this.handleAuthError(e):this.updateSyncStatus("error",`Post-reset sync failed: ${e}`)}}cleanup(){this.debouncedSync.cancel(),this.syncHandler&&this.syncHandler.cancel(),this.syncListeners.clear(),this.remoteDb&&this.remoteDb.close(),this.isInitialized=!1,this.syncInProgress=!1,this.changeQueue.clear(),this.processingQueue=!1}}i.Z.plugin(s.Z);let v=new class{db;syncManager;admissionManager;festivalManager;itemManager;lostPropertyManager;shiftManager;feedbackManager;bayManager;knowledgeBaseManager;sensoryHubManager;isInitialized=!1;initializationPromise=null;constructor(){this.initializationPromise=this.initializeSync()}async initializeSync(){try{await (0,b.tC)();let e=(0,b.iE)();this.db=new i.Z(e.localName),this.syncManager=new w(this.db),this.admissionManager=new n(this.db,this.syncManager),this.festivalManager=new o(this.db,this.syncManager),this.itemManager=new d(this.db,this.syncManager),this.lostPropertyManager=new l(this.db,this.syncManager),this.shiftManager=new c(this.db,this.syncManager),this.feedbackManager=new h(this.db,this.syncManager),this.bayManager=new g(this.db,this.syncManager),this.knowledgeBaseManager=new p(this.db,this.syncManager),this.sensoryHubManager=new m(this.db,this.syncManager),this.isInitialized=!0,this.startBackgroundSync()}catch(e){}}startBackgroundSync(){setTimeout(()=>{try{this.syncManager.sync().catch(e=>{})}catch(e){}},100)}async waitForInitialization(){this.initializationPromise&&await this.initializationPromise}async ensureInitialized(){this.isInitialized||await this.waitForInitialization()}async addAdmission(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.admissionManager.addAdmission(e);return this.triggerBackgroundSync(),t}async getAdmissionsByFestival(e){if(!this.isInitialized)throw Error("Database not initialized");return this.admissionManager.getAdmissionsByFestival(e,!1)}async getAdmissionsByLocation(e){if(!this.isInitialized)throw Error("Database not initialized");return this.admissionManager.getAdmissionsByLocation(e,!1)}async getAdmissionById(e){if(!this.isInitialized)throw Error("Database not initialized");return this.admissionManager.getAdmissionById(e)}async updateAdmission(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.admissionManager.updateAdmission(e);return this.triggerBackgroundSync(),t}async deleteAdmission(e){if(!this.isInitialized)throw Error("Database not initialized");await this.admissionManager.deleteAdmission(e),this.triggerBackgroundSync()}async fixInconsistentAdmissions(){if(!this.isInitialized)throw Error("Database not initialized");return this.admissionManager.fixInconsistentAdmissions()}async addFestival(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.festivalManager.addFestival(e);return this.triggerBackgroundSync(),t}async getFestivals(){return this.isInitialized||await this.waitForInitialization(),this.festivalManager.getFestivals(!1)}async updateFestival(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.festivalManager.updateFestival(e);return this.triggerBackgroundSync(),t}async deleteFestival(e){if(!this.isInitialized)throw Error("Database not initialized");await this.festivalManager.deleteFestival(e),this.triggerBackgroundSync()}async getFestivalNotes(e){if(!this.isInitialized)throw Error("Database not initialized");return this.festivalManager.getFestivalNotes(e)}async updateFestivalNotes(e,t){if(!this.isInitialized)throw Error("Database not initialized");await this.festivalManager.updateFestivalNotes(e,t),this.triggerBackgroundSync()}async addOrUpdateItemCount(e,t,a,i){if(!this.isInitialized)throw Error("Database not initialized");let s=await this.itemManager.addOrUpdateItemCount(e,t,a,i);return this.triggerBackgroundSync(),s}async getItemCountsByFestival(e){if(!this.isInitialized)throw Error("Database not initialized");return this.itemManager.getItemCountsByFestival(e,!1)}async updateItemCount(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.itemManager.updateItemCount(e);return this.triggerBackgroundSync(),t}async deleteItemCount(e){if(!this.isInitialized)throw Error("Database not initialized");await this.itemManager.deleteItemCount(e),this.triggerBackgroundSync()}async addLostPropertyItem(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.lostPropertyManager.addLostPropertyItem(e);return this.triggerBackgroundSync(),t}async getLostPropertyItems(e){if(!this.isInitialized)throw Error("Database not initialized");return this.lostPropertyManager.getLostPropertyItems(e,!1)}async updateLostPropertyItem(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.lostPropertyManager.updateLostPropertyItem(e);return this.triggerBackgroundSync(),t}async deleteLostPropertyItem(e){if(!this.isInitialized)throw Error("Database not initialized");await this.lostPropertyManager.deleteLostPropertyItem(e),this.triggerBackgroundSync()}async getShiftConfig(e){if(!this.isInitialized)throw Error("Database not initialized");return this.shiftManager.getShiftConfig(e)}async saveShiftConfig(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.shiftManager.saveShiftConfig(e);return this.triggerBackgroundSync(),t}async getTeamLeaders(e){if(!this.isInitialized)throw Error("Database not initialized");return this.shiftManager.getTeamLeaders(e)}async addTeamLeader(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.shiftManager.addTeamLeader(e);return this.triggerBackgroundSync(),t}async updateTeamLeader(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.shiftManager.updateTeamLeader(e);return this.triggerBackgroundSync(),t}async deleteTeamLeader(e){if(!this.isInitialized)throw Error("Database not initialized");await this.shiftManager.deleteTeamLeader(e),this.triggerBackgroundSync()}async getShiftAssignments(e){if(!this.isInitialized)throw Error("Database not initialized");return this.shiftManager.getShiftAssignments(e)}async addShiftAssignment(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.shiftManager.addShiftAssignment(e);return this.triggerBackgroundSync(),t}async updateShiftAssignment(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.shiftManager.updateShiftAssignment(e);return this.triggerBackgroundSync(),t}async deleteShiftAssignment(e){if(!this.isInitialized)throw Error("Database not initialized");await this.shiftManager.deleteShiftAssignment(e),this.triggerBackgroundSync()}async generateShiftSchedule(e,t,a,i){if(!this.isInitialized)throw Error("Database not initialized");let s=await this.shiftManager.generateShiftSchedule(e,t,a,i);return this.triggerBackgroundSync(),s}async addFeedback(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.feedbackManager.addFeedback(e);return this.triggerBackgroundSync(),t}async getAllFeedback(){if(!this.isInitialized)throw Error("Database not initialized");return this.feedbackManager.getAllFeedback(!1)}async getUnresolvedFeedback(){if(!this.isInitialized)throw Error("Database not initialized");return this.feedbackManager.getUnresolvedFeedback(!1)}async updateFeedbackStatus(e,t){if(!this.isInitialized)throw Error("Database not initialized");let a=await this.feedbackManager.updateFeedbackStatus(e,t);return this.triggerBackgroundSync(),a}async deleteFeedback(e){if(!this.isInitialized)throw Error("Database not initialized");await this.feedbackManager.deleteFeedback(e),this.triggerBackgroundSync()}async getKnowledgeBaseItems(e){if(!this.isInitialized)throw Error("Database not initialized");return this.knowledgeBaseManager.getKnowledgeBaseItems(e)}async getKnowledgeBaseCategories(e){if(!this.isInitialized)throw Error("Database not initialized");return this.knowledgeBaseManager.getKnowledgeBaseCategories(e)}async addKnowledgeBaseItem(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.knowledgeBaseManager.addKnowledgeBaseItem(e);return this.triggerBackgroundSync(),t}async updateKnowledgeBaseItem(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.knowledgeBaseManager.updateKnowledgeBaseItem(e);return this.triggerBackgroundSync(),t}async deleteKnowledgeBaseItem(e){if(!this.isInitialized)throw Error("Database not initialized");await this.knowledgeBaseManager.deleteKnowledgeBaseItem(e),this.triggerBackgroundSync()}async listAdmissionsForBay(e){if(!this.isInitialized)throw Error("Database not initialized");return this.bayManager.listAdmissionsForBay(e)}async checkBayAvailability(e){if(!this.isInitialized)throw Error("Database not initialized");return this.bayManager.checkBayAvailability(e)}async validateBayAssignment(e,t){if(!this.isInitialized)throw Error("Database not initialized");return this.bayManager.validateBayAssignment(e,t)}async assignToBay(e,t){if(!this.isInitialized)throw Error("Database not initialized");await this.bayManager.assignToBay(e,t),this.triggerBackgroundSync()}async removeFromBay(e){if(!this.isInitialized)throw Error("Database not initialized");await this.bayManager.removeFromBay(e),this.triggerBackgroundSync()}async addSensoryHubVisit(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.sensoryHubManager.addVisit(e);return this.triggerBackgroundSync(),t}async getSensoryHubVisitsByFestival(e){if(!this.isInitialized)throw Error("Database not initialized");return this.sensoryHubManager.getVisitsByFestival(e,!1)}async getSensoryHubVisitsByLocation(e){if(!this.isInitialized)throw Error("Database not initialized");return this.sensoryHubManager.getVisitsByLocation(e,!1)}async updateSensoryHubVisit(e){if(!this.isInitialized)throw Error("Database not initialized");let t=await this.sensoryHubManager.updateVisit(e);return this.triggerBackgroundSync(),t}async deleteSensoryHubVisit(e){if(!this.isInitialized)throw Error("Database not initialized");await this.sensoryHubManager.deleteVisit(e),this.triggerBackgroundSync()}async getAllPageAccess(){return f.D.getAllPageAccess()}async getPageAccess(e){return f.D.getPageAccess(e)}async setPageAccess(e,t,a){let i=await f.D.setPageAccess(e,t,a);return this.triggerBackgroundSync(),i}async getAllUserRoles(){return f.D.getAllUserRoles()}async getUserRole(e){return f.D.getUserRole(e)}async setUserRole(e,t){let a=await f.D.setUserRole(e,t);return this.triggerBackgroundSync(),a}async deleteUserRole(e){let t=await f.D.deleteUserRole(e);return this.triggerBackgroundSync(),t}sync(){this.syncManager&&this.triggerBackgroundSync()}addSyncListener(e){return this.syncManager?this.syncManager.addSyncListener(e):()=>{}}async manualSync(){if(this.syncManager)try{await this.syncManager.sync()}catch(e){}}hasPendingChanges(){return!!this.syncManager&&this.syncManager.hasPendingChanges()}getSyncStatus(){return this.syncManager?this.syncManager.getSyncStatus():{status:"disconnected"}}getSyncManager(){return this.syncManager}triggerBackgroundSync(){this.syncManager&&setTimeout(()=>{this.syncManager.syncAfterChange().catch(e=>{})},100)}async performDatabaseCleanup(){if(!this.isInitialized)throw Error("Database not initialized");let e=new Date;e.setMonth(e.getMonth()-3);let t=e.toISOString(),a={admissions:0,feedback:0,items:0,lostProperty:0,shifts:0,sensoryHubVisits:0},i=[],s=0;try{a.admissions=await this.admissionManager.cleanupOldAdmissions(t),s+=a.admissions}catch(t){let e=`Failed to cleanup admissions: ${t}`;i.push(e)}try{a.feedback=await this.feedbackManager.cleanupOldFeedback(t),s+=a.feedback}catch(t){let e=`Failed to cleanup feedback: ${t}`;i.push(e)}try{a.items=await this.itemManager.cleanupOldItems(t),s+=a.items}catch(t){let e=`Failed to cleanup items: ${t}`;i.push(e)}try{a.lostProperty=await this.lostPropertyManager.cleanupOldLostProperty(t),s+=a.lostProperty}catch(t){let e=`Failed to cleanup lost property: ${t}`;i.push(e)}try{a.shifts=await this.shiftManager.cleanupOldShifts(t),s+=a.shifts}catch(t){let e=`Failed to cleanup shifts: ${t}`;i.push(e)}try{a.sensoryHubVisits=await this.sensoryHubManager.cleanupOldVisits(t),s+=a.sensoryHubVisits}catch(t){let e=`Failed to cleanup sensory hub visits: ${t}`;i.push(e)}try{s>0&&await this.syncManager.sync()}catch(t){let e=`Failed final sync after cleanup: ${t}`;i.push(e)}return{totalCleaned:s,cleanupSummary:a,errors:i}}async cleanupOldRecords(){try{await this.performDatabaseCleanup()}catch(e){}}async exportCompleteDatabase(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"json";try{await this.ensureInitialized();let t={metadata:{exportTimestamp:new Date().toISOString(),exportFormat:e,databaseVersion:"1.8.1",exportedBy:"iThinc Welfare Management System"},data:{},recordCounts:{}},a=[];try{let e=await this.admissionManager.exportAllAdmissions();t.data.admissions=e,t.recordCounts.admissions=e.length}catch(e){a.push("admissions"),t.data.admissions=[],t.recordCounts.admissions=0}try{let e=await this.festivalManager.exportAllFestivals();t.data.festivals=e,t.recordCounts.festivals=e.length}catch(e){a.push("festivals"),t.data.festivals=[],t.recordCounts.festivals=0}try{let e=await this.itemManager.exportAllItems();t.data.items=e,t.recordCounts.items=e.length}catch(e){a.push("items"),t.data.items=[],t.recordCounts.items=0}try{let e=await this.lostPropertyManager.exportAllLostProperty();t.data.lostProperty=e,t.recordCounts.lostProperty=e.length}catch(e){a.push("lostProperty"),t.data.lostProperty=[],t.recordCounts.lostProperty=0}try{let e=await this.feedbackManager.exportAllFeedback();t.data.feedback=e,t.recordCounts.feedback=e.length}catch(e){a.push("feedback"),t.data.feedback=[],t.recordCounts.feedback=0}try{let e=await this.shiftManager.exportAllShifts();t.data.shifts=e,t.recordCounts.shifts={configs:e.configs.length,teamLeaders:e.teamLeaders.length,assignments:e.assignments.length}}catch(e){a.push("shifts"),t.data.shifts={configs:[],teamLeaders:[],assignments:[]},t.recordCounts.shifts={configs:0,teamLeaders:0,assignments:0}}try{let e=await this.knowledgeBaseManager.exportAllKnowledgeBase();t.data.knowledgeBase=e,t.recordCounts.knowledgeBase=e.length}catch(e){a.push("knowledgeBase"),t.data.knowledgeBase=[],t.recordCounts.knowledgeBase=0}try{let e=await this.sensoryHubManager.exportAllVisits();t.data.sensoryHubVisits=e,t.recordCounts.sensoryHubVisits=e.length}catch(e){a.push("sensoryHubVisits"),t.data.sensoryHubVisits=[],t.recordCounts.sensoryHubVisits=0}try{let e=await f.D.exportAllAccessControl();t.data.accessControl=e,t.recordCounts.accessControl={pageAccess:e.pageAccess.length,userRoles:e.userRoles.length}}catch(e){a.push("accessControl"),t.data.accessControl={pageAccess:[],userRoles:[]},t.recordCounts.accessControl={pageAccess:0,userRoles:0}}if(a.length>0?(t.metadata.partialExport=!0,t.metadata.failedDataTypes=a):t.metadata.partialExport=!1,"json"===e){let e=JSON.stringify(t,null,2);return new Blob([e],{type:"application/json"})}if("csv"===e){t.metadata.note="CSV export requires JSZip library. Returning JSON format instead.";let e=JSON.stringify(t,null,2);return new Blob([e],{type:"application/json"})}throw Error(`Unsupported export format: ${e}`)}catch(e){throw e}}async clearLocalDatabase(){try{this.syncManager&&this.syncManager.cleanup(),this.db&&this.db.removeAllListeners(),this.db&&await this.db.destroy(),this.isInitialized=!1,this.initializationPromise=null}catch(e){throw Error(`Failed to clear local database: ${e}`)}}async reinitializeDatabase(){try{if(this.isInitialized)throw Error("Database is already initialized. Call clearLocalDatabase() first.");await (0,b.tC)();let e=(0,b.iE)();this.db=new i.Z(e.localName),this.syncManager=new w(this.db),this.admissionManager=new n(this.db,this.syncManager),this.festivalManager=new o(this.db,this.syncManager),this.itemManager=new d(this.db,this.syncManager),this.lostPropertyManager=new l(this.db,this.syncManager),this.shiftManager=new c(this.db,this.syncManager),this.feedbackManager=new h(this.db,this.syncManager),this.bayManager=new g(this.db,this.syncManager),this.knowledgeBaseManager=new p(this.db,this.syncManager),this.isInitialized=!0}catch(e){throw this.isInitialized=!1,this.initializationPromise=null,Error(`Failed to reinitialize database: ${e}`)}}async resetDatabaseWithFreshSync(){try{if(await this.clearLocalDatabase(),await this.reinitializeDatabase(),this.syncManager)try{await this.syncManager.handlePostResetSync()}catch(e){}}catch(e){throw Error(`Failed to reset database with fresh sync: ${e}`)}}},S=null;setTimeout(()=>{S=v.getSyncManager()},1e3)}},t={};function a(i){var s=t[i];if(void 0!==s)return s.exports;var r=t[i]={id:i,loaded:!1,exports:{}};return e[i].call(r.exports,r,r.exports,a),r.loaded=!0,r.exports}a.m=e,a.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return a.d(t,{a:t}),t},(()=>{var e,t=Object.getPrototypeOf?e=>Object.getPrototypeOf(e):e=>e.__proto__;a.t=function(i,s){if(1&s&&(i=this(i)),8&s||"object"==typeof i&&i&&(4&s&&i.__esModule||16&s&&"function"==typeof i.then))return i;var r=Object.create(null);a.r(r);var n={};e=e||[null,t({}),t([]),t(t)];for(var o=2&s&&i;"object"==typeof o&&!~e.indexOf(o);o=t(o))Object.getOwnPropertyNames(o).forEach(e=>{n[e]=()=>i[e]});return n.default=()=>i,a.d(r,n),r}})(),a.d=(e,t)=>{for(var i in t)a.o(t,i)&&!a.o(e,i)&&Object.defineProperty(e,i,{enumerable:!0,get:t[i]})},a.f={},a.e=e=>Promise.all(Object.keys(a.f).reduce((t,i)=>(a.f[i](e,t),t),[])),a.u=e=>"static/js/async/"+e+"."+({175:"7e84f2f1",374:"44572717",435:"a8c2b4a4",442:"b5a3c3d6",555:"81bef1c1",581:"9c5a9ba1",616:"a21835bf",632:"e976c8f3",702:"fef60077",743:"b480cb52",751:"494bb29d",871:"c74e1b2b",880:"83c9e2cb",923:"32f025fb",934:"3b114cfc",99:"70a845d9"})[e]+".js",a.miniCssF=e=>""+e+".css",a.h=()=>"b989586cd219b347",a.g=(()=>{if("object"==typeof globalThis)return globalThis;try{return this||Function("return this")()}catch(e){if("object"==typeof window)return window}})(),a.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),(()=>{var e={},t="ithinc-welfare:";a.l=function(i,s,r,n){if(e[i])return void e[i].push(s);if(void 0!==r)for(var o,d,l=document.getElementsByTagName("script"),c=0;c<l.length;c++){var h=l[c];if(h.getAttribute("src")==i||h.getAttribute("data-webpack")==t+r){o=h;break}}o||(d=!0,(o=document.createElement("script")).charset="utf-8",o.timeout=120,a.nc&&o.setAttribute("nonce",a.nc),o.setAttribute("data-webpack",t+r),o.src=i),e[i]=[s];var u=function(t,a){o.onerror=o.onload=null,clearTimeout(y);var s=e[i];if(delete e[i],o.parentNode&&o.parentNode.removeChild(o),s&&s.forEach(function(e){return e(a)}),t)return t(a)},y=setTimeout(u.bind(null,void 0,{type:"timeout",target:o}),12e4);o.onerror=u.bind(null,o.onerror),o.onload=u.bind(null,o.onload),d&&document.head.appendChild(o)}})(),a.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},a.nmd=e=>(e.paths=[],e.children||(e.children=[]),e),(()=>{var e=[];a.O=(t,i,s,r)=>{if(i){r=r||0;for(var n=e.length;n>0&&e[n-1][2]>r;n--)e[n]=e[n-1];e[n]=[i,s,r];return}for(var o=1/0,n=0;n<e.length;n++){for(var i=e[n][0],s=e[n][1],r=e[n][2],d=!0,l=0;l<i.length;l++)(!1&r||o>=r)&&Object.keys(a.O).every(e=>a.O[e](i[l]))?i.splice(l--,1):(d=!1,r<o&&(o=r));if(d){e.splice(n--,1);var c=s();void 0!==c&&(t=c)}}return t}})(),a.p="/",a.rv=()=>"1.3.5",(()=>{var e={980:0};a.f.j=function(t,i){var s=a.o(e,t)?e[t]:void 0;if(0!==s)if(s)i.push(s[2]);else{var r=new Promise((a,i)=>s=e[t]=[a,i]);i.push(s[2]=r);var n=a.p+a.u(t),o=Error();a.l(n,function(i){if(a.o(e,t)&&(0!==(s=e[t])&&(e[t]=void 0),s)){var r=i&&("load"===i.type?"missing":i.type),n=i&&i.target&&i.target.src;o.message="Loading chunk "+t+" failed.\n("+r+": "+n+")",o.name="ChunkLoadError",o.type=r,o.request=n,s[1](o)}},"chunk-"+t,t)}},a.O.j=t=>0===e[t];var t=(t,i)=>{var s,r,n=i[0],o=i[1],d=i[2],l=0;if(n.some(t=>0!==e[t])){for(s in o)a.o(o,s)&&(a.m[s]=o[s]);if(d)var c=d(a)}for(t&&t(i);l<n.length;l++)r=n[l],a.o(e,r)&&e[r]&&e[r][0](),e[r]=0;return a.O(c)},i=self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[];i.forEach(t.bind(null,0)),i.push=t.bind(null,i.push.bind(i))})(),a.ruid="bundler=rspack@1.3.5";var i=a.O(void 0,["964","263","64","192"],function(){return a(59113)});i=a.O(i)})();