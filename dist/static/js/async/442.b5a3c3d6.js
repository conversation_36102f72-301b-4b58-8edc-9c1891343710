"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["442"],{98911:function(e,t,a){a.r(t),a.d(t,{default:()=>R});var n=a(85893),s=a(67294),i=a(96872),r=a(33991),l=a(54757),o=a(13400),d=a(83502),c=a(5214),h=a(56099),m=a(73892),u=a(1156),x=a(48346),f=a(73876),j=a(7230),g=a(75169);let p=e=>{let{festivalId:t,existingConfig:a,onConfigSaved:i}=e,[l,c]=(0,s.useState)(a?.shiftsPerDay||4),[p,Z]=(0,s.useState)(a?.firstShiftStart||"06:00"),[y,v]=(0,s.useState)(a?.shiftDuration||6),[S,b]=(0,s.useState)(a?.maxTeamSize||6),[C,T]=(0,s.useState)(!1),L=async e=>{e.preventDefault(),T(!0);try{let e=await d.R.saveShiftConfig({type:"shift_config",documentType:"shift_config",festivalId:t,shiftsPerDay:l,firstShiftStart:p,shiftDuration:y,maxTeamSize:S,syncStatus:"local_only"});i(e)}catch(e){}finally{T(!1)}};return(0,n.jsx)(r.Z,{component:"form",onSubmit:L,sx:{maxWidth:"sm"},children:(0,n.jsxs)(o.Z,{spacing:3,children:[(0,n.jsx)(h.Z,{label:"Number of Shifts per Day",type:"number",value:l,onChange:e=>c(parseInt(e.target.value)),inputProps:{min:1,max:8},fullWidth:!0,required:!0}),(0,n.jsxs)(m.Z,{fullWidth:!0,required:!0,children:[(0,n.jsx)(u.Z,{children:"First Shift Start Time"}),(0,n.jsx)(x.Z,{value:p,onChange:e=>Z(e.target.value),label:"First Shift Start Time",children:(()=>{let e=[];for(let t=0;t<24;t++){let a=t.toString().padStart(2,"0");e.push(`${a}:00`)}return e})().map(e=>(0,n.jsx)(f.Z,{value:e,children:e},e))})]}),(0,n.jsx)(h.Z,{label:"Shift Duration (hours)",type:"number",value:y,onChange:e=>v(parseInt(e.target.value)),inputProps:{min:1,max:12},fullWidth:!0,required:!0}),(0,n.jsx)(h.Z,{label:"Maximum Team Size",type:"number",value:S,onChange:e=>b(parseInt(e.target.value)),inputProps:{min:1,max:20},fullWidth:!0,required:!0}),(0,n.jsx)(r.Z,{sx:{display:"flex",justifyContent:"flex-end"},children:(0,n.jsx)(j.Z,{type:"submit",variant:"contained",disabled:C,startIcon:(0,n.jsx)(g.Z,{}),sx:{bgcolor:"primary.main","&:hover":{bgcolor:"primary.dark"}},children:C?"Saving...":"Save Configuration"})})]})})};var Z=a(3814),y=a(89126),v=a(38953),S=a(14540),b=a(97454),C=a(96493);let T=e=>{let{festivalId:t,teamLeaders:a,onTeamLeaderChange:i}=e,[c,p]=(0,s.useState)(""),[T,L]=(0,s.useState)(""),[w,D]=(0,s.useState)([]),[_,I]=(0,s.useState)(!1),[A,W]=(0,s.useState)(null),k=Array.from({length:26},(e,t)=>String.fromCharCode(65+t)),R=async e=>{e.preventDefault(),I(!0);try{await d.R.addTeamLeader({type:"team_leader",documentType:"team_leader",festivalId:t,name:c,contact:T||void 0,teams:w,syncStatus:"local_only"}),i(),p(""),L(""),D([])}catch(e){}finally{I(!1)}},$=async e=>{I(!0);try{await d.R.updateTeamLeader(e),i(),W(null)}catch(e){}finally{I(!1)}},z=async e=>{if(window.confirm("Are you sure you want to remove this team leader?"))try{await d.R.deleteTeamLeader(e),i()}catch(e){}};return(0,n.jsxs)(o.Z,{spacing:4,children:[(0,n.jsx)(r.Z,{component:"form",onSubmit:R,sx:{maxWidth:"sm"},children:(0,n.jsxs)(o.Z,{spacing:3,children:[(0,n.jsx)(h.Z,{label:"Name",value:c,onChange:e=>p(e.target.value),required:!0,fullWidth:!0}),(0,n.jsx)(h.Z,{label:"Contact (optional)",value:T,onChange:e=>L(e.target.value),fullWidth:!0}),(0,n.jsxs)(m.Z,{fullWidth:!0,children:[(0,n.jsx)(u.Z,{children:"Assigned Teams"}),(0,n.jsx)(x.Z,{multiple:!0,value:w,onChange:e=>{D("string"==typeof e.target.value?e.target.value.split(","):e.target.value)},input:(0,n.jsx)(Z.Z,{label:"Assigned Teams"}),renderValue:e=>e.map(e=>`Team ${e}`).join(", "),children:k.map(e=>(0,n.jsxs)(f.Z,{value:e,children:["Team ",e]},e))})]}),(0,n.jsx)(r.Z,{sx:{display:"flex",justifyContent:"flex-end"},children:(0,n.jsx)(j.Z,{type:"submit",variant:"contained",disabled:_,startIcon:(0,n.jsx)(g.Z,{}),children:_?"Saving...":"Add Team Leader"})})]})}),(0,n.jsxs)(r.Z,{children:[(0,n.jsx)(l.Z,{variant:"h6",sx:{mb:2},children:"Team Leaders"}),(0,n.jsx)(o.Z,{spacing:2,children:a.map(e=>(0,n.jsxs)(y.Z,{sx:{p:2,display:"flex",alignItems:"center",justifyContent:"space-between"},children:[A===e._id?(0,n.jsxs)(o.Z,{spacing:2,sx:{flex:1,mr:2},children:[(0,n.jsx)(h.Z,{value:e.name,onChange:t=>$({...e,name:t.target.value}),size:"small",fullWidth:!0}),(0,n.jsx)(h.Z,{value:e.contact||"",onChange:t=>$({...e,contact:t.target.value}),placeholder:"Contact (optional)",size:"small",fullWidth:!0}),(0,n.jsxs)(m.Z,{fullWidth:!0,size:"small",children:[(0,n.jsx)(u.Z,{children:"Teams"}),(0,n.jsx)(x.Z,{multiple:!0,value:e.teams,onChange:t=>{let a="string"==typeof t.target.value?t.target.value.split(","):t.target.value;$({...e,teams:a})},input:(0,n.jsx)(Z.Z,{label:"Teams"}),renderValue:e=>e.map(e=>`Team ${e}`).join(", "),children:k.map(e=>(0,n.jsxs)(f.Z,{value:e,children:["Team ",e]},e))})]})]}):(0,n.jsxs)(r.Z,{sx:{flex:1},children:[(0,n.jsx)(l.Z,{variant:"subtitle1",sx:{fontWeight:500},children:e.name}),e.contact&&(0,n.jsx)(l.Z,{variant:"body2",color:"text.secondary",children:e.contact}),(0,n.jsxs)(l.Z,{variant:"body2",color:"text.secondary",sx:{mt:.5},children:["Teams: ",e.teams.map(e=>`Team ${e}`).join(", ")||"None assigned"]})]}),(0,n.jsxs)(o.Z,{direction:"row",spacing:1,children:[(0,n.jsx)(v.Z,{onClick:()=>A===e._id?W(null):W(e._id),color:"primary",size:"small",children:A===e._id?(0,n.jsx)(C.Z,{}):(0,n.jsx)(S.Z,{})}),(0,n.jsx)(v.Z,{onClick:()=>z(e._id),color:"error",size:"small",children:(0,n.jsx)(b.Z,{})})]})]},e._id))})]})]})};var L=a(95438),w=a(21183),D=a(52104),_=a(98913),I=a(60583),A=a(60187),W=a(51001);let k=e=>{let{festivalId:t,startDate:a,endDate:i,shiftConfig:c,teamLeaders:m,assignments:u,onAssignmentChange:p}=e,[Z,S]=(0,s.useState)([]),[T,k]=(0,s.useState)(null),[R,$]=(0,s.useState)("");(0,s.useEffect)(()=>{let e=[],t=new Date(a),n=new Date(i);for(;t<=n;t.setDate(t.getDate()+1))e.push(t.toISOString().split("T")[0]);S(e)},[a,i]);let z=(e,a)=>{let n=u.find(t=>t.date===e&&t.shiftNumber===a);if(n)return n;let s={_id:`shift_assignment_${t}_${e}_${a}`,type:"shift_assignment",documentType:"shift_assignment",festivalId:t,date:e,shiftNumber:a,startTime:N(a).start,endTime:N(a).end,teamLeaderId:"",teamLetter:"",teamMembers:[],syncStatus:"local_only"};return d.R.addShiftAssignment(s).then(()=>{p()}).catch(e=>{}),s},M=async(e,t)=>{try{let a={...e,teamMembers:t};await d.R.updateShiftAssignment(a),p(),k(null)}catch(e){}},P=e=>m.find(t=>t._id===e)?.name||"Unknown",N=e=>{let[t,a]=c.firstShiftStart.split(":").map(Number),n=(e-1)*c.shiftDuration,s=new Date;s.setHours(t+n,a,0);let i=new Date(s);return i.setHours(s.getHours()+c.shiftDuration),{start:s.toTimeString().slice(0,5),end:i.toTimeString().slice(0,5)}},q=async()=>{for(let e=1;e<=c.shiftsPerDay;e++)for(let t of Z){let a={...z(t,e),teamLeaderId:"",teamLetter:"",teamMembers:[]};try{await d.R.updateShiftAssignment(a)}catch(e){}}p()},E=e=>{let t=["A","B","C","D","E"],a=t.indexOf(e);return t[(a+1)%t.length]},O=async()=>{let e=null,t="",a=0;for(let n of Z){for(let s=1;s<=c.shiftsPerDay;s++){let i=z(n,s);if("A"===i.teamLetter){e=i,t=n,a=s;break}}if(e)break}if(!e)return void alert("Please assign Team A to a shift first");if(!m.find(t=>t._id===e.teamLeaderId))return void alert("Could not find Team A leader");let n="A",s=a,i=Z.indexOf(t);for(;i<Z.length;){let e=z(Z[i],s),t=m.find(e=>e.teams.includes(n));if(t)try{let a={...e,teamLeaderId:t._id,teamLetter:n,teamMembers:[]};await d.R.updateShiftAssignment(a)}catch(e){}++s>c.shiftsPerDay&&(s=1,i++),n=E(n)}p()};return(0,n.jsxs)(r.Z,{sx:{overflowX:"auto"},children:[(0,n.jsxs)(o.Z,{direction:"row",spacing:2,sx:{mb:3},children:[(0,n.jsx)(j.Z,{variant:"contained",color:"error",startIcon:(0,n.jsx)(b.Z,{}),onClick:q,children:"Clear All Shifts"}),(0,n.jsx)(j.Z,{variant:"contained",startIcon:(0,n.jsx)(W.Z,{}),onClick:O,children:"Fill Rotating Pattern"})]}),(0,n.jsx)(L.Z,{component:y.Z,sx:{mb:3},children:(0,n.jsxs)(w.Z,{size:"small",children:[(0,n.jsx)(D.Z,{children:(0,n.jsxs)(_.Z,{children:[(0,n.jsx)(I.Z,{sx:{fontWeight:"medium",color:"text.secondary",fontSize:"0.75rem",textTransform:"uppercase"},children:"Shift"}),Z.map(e=>{let t=new Date(e),s=t.toLocaleDateString("en-US",{weekday:"short"}),r=t.toLocaleDateString("en-US",{day:"numeric",month:"short"}),d=new Date(a),c=Math.floor((t.getTime()-d.getTime())/864e5),h=Math.floor((new Date(i).getTime()-d.getTime())/864e5)+1;return(0,n.jsx)(I.Z,{sx:{fontWeight:"medium",color:"text.secondary",fontSize:"0.75rem",textTransform:"uppercase"},children:(0,n.jsxs)(o.Z,{children:[(0,n.jsx)(l.Z,{variant:"caption",children:s}),(0,n.jsx)(l.Z,{variant:"body2",children:r}),(0,n.jsxs)(l.Z,{variant:"caption",color:"text.secondary",children:["Day ",c+1," of ",h]})]})},e)})]})}),(0,n.jsx)(A.Z,{children:Array.from({length:c.shiftsPerDay}).map((e,t)=>(0,n.jsxs)(_.Z,{children:[(0,n.jsx)(I.Z,{children:(0,n.jsxs)(o.Z,{children:[(0,n.jsxs)(l.Z,{variant:"subtitle2",children:["Shift ",t+1]}),(0,n.jsx)(l.Z,{variant:"body2",color:"text.secondary",children:(()=>{let e=N(t+1);return`${e.start} - ${e.end}`})()}),(0,n.jsxs)(l.Z,{variant:"caption",color:"text.secondary",children:[c.shiftDuration," hour shift"]})]})}),Z.map(e=>{let a=z(e,t+1);return(0,n.jsx)(I.Z,{children:(0,n.jsxs)(o.Z,{spacing:1,children:[(0,n.jsxs)(x.Z,{size:"small",value:a.teamLetter||"no_shift",onChange:e=>{let t=e.target.value;if("no_shift"===t){let e={...a,teamLeaderId:"",teamLetter:"",teamMembers:[]};d.R.updateShiftAssignment(e).then(()=>p()).catch(e=>void 0)}else{let e=m.find(e=>e.teams.includes(t));if(e){let n={...a,teamLeaderId:e._id,teamLetter:t,teamMembers:[]};d.R.updateShiftAssignment(n).then(()=>p()).catch(e=>void 0)}}},fullWidth:!0,children:[(0,n.jsx)(f.Z,{value:"no_shift",children:"No Shift"}),m.flatMap(e=>e.teams.map(t=>({team:t,leaderName:e.name}))).sort((e,t)=>e.team.localeCompare(t.team)).map(e=>{let{team:t,leaderName:a}=e;return(0,n.jsxs)(f.Z,{value:t,children:["Team ",t," (",a,")"]},`${t}-${a}`)})]}),T===`${e}-${t+1}`?(0,n.jsxs)(r.Z,{children:[(0,n.jsx)(h.Z,{multiline:!0,rows:3,value:R,onChange:e=>$(e.target.value),placeholder:"Enter team members (one per line)",size:"small",fullWidth:!0}),(0,n.jsxs)(o.Z,{direction:"row",spacing:1,justifyContent:"flex-end",sx:{mt:1},children:[(0,n.jsx)(v.Z,{size:"small",color:"primary",onClick:()=>{M(a,R.split("\n").filter(Boolean))},children:(0,n.jsx)(g.Z,{})}),(0,n.jsx)(v.Z,{size:"small",onClick:()=>k(null),children:(0,n.jsx)(C.Z,{})})]})]}):(0,n.jsx)(r.Z,{onClick:()=>{a.teamLeaderId&&a.teamLetter&&(k(`${e}-${t+1}`),$(a.teamMembers.join("\n")||""))},sx:{cursor:a.teamLeaderId?"pointer":"default","&:hover":{bgcolor:a.teamLeaderId?"action.hover":"transparent"},p:1,borderRadius:1},children:a.teamLetter?(0,n.jsxs)(o.Z,{spacing:.5,children:[(0,n.jsxs)(l.Z,{variant:"subtitle2",children:["Team ",a.teamLetter," (",P(a.teamLeaderId),")"]}),a.teamMembers.length>0?(0,n.jsx)(r.Z,{component:"ul",sx:{pl:2,m:0},children:a.teamMembers.map((e,t)=>(0,n.jsx)(l.Z,{component:"li",variant:"body2",color:"text.secondary",children:e},t))}):(0,n.jsx)(l.Z,{variant:"body2",color:"text.secondary",sx:{fontStyle:"italic"},children:"Click to add team members"})]}):(0,n.jsx)(l.Z,{variant:"body2",color:"text.secondary",sx:{fontStyle:"italic"},children:"No Shift"})})]})},e)})]},t+1))})]})})]})},R=()=>{let{festivalId:e}=(0,i.UO)(),{activeFestival:t}=(0,c.C)(),[a,h]=(0,s.useState)(null),[m,u]=(0,s.useState)([]),[x,f]=(0,s.useState)([]),[j,g]=(0,s.useState)(!0);return((0,s.useEffect)(()=>{(async()=>{if(!e)return g(!1);try{let[t,a,n]=await Promise.all([d.R.getShiftConfig(e),d.R.getTeamLeaders(e),d.R.getShiftAssignments(e)]);h(t),u(a),f(n)}catch(e){}finally{g(!1)}})()},[e]),j)?(0,n.jsx)(r.Z,{sx:{p:2},children:"Loading..."}):t&&e?(0,n.jsxs)(r.Z,{sx:{p:2},children:[(0,n.jsx)(l.Z,{variant:"h4",sx:{fontWeight:"bold",mb:2},children:"Shift Management"}),(0,n.jsx)(l.Z,{variant:"h5",sx:{mb:2},children:t.name}),(0,n.jsxs)(o.Z,{spacing:4,children:[(0,n.jsxs)(r.Z,{children:[(0,n.jsx)(l.Z,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Shift Configuration"}),(0,n.jsx)(p,{festivalId:e,existingConfig:a,onConfigSaved:e=>h(e)})]}),(0,n.jsxs)(r.Z,{children:[(0,n.jsx)(l.Z,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Team Leaders"}),(0,n.jsx)(T,{festivalId:e,teamLeaders:m,onTeamLeaderChange:()=>{d.R.getTeamLeaders(e).then(u)}})]}),(0,n.jsxs)(r.Z,{children:[(0,n.jsx)(l.Z,{variant:"h6",sx:{fontWeight:600,mb:2},children:"Shift Schedule"}),a?(0,n.jsx)(k,{festivalId:e,startDate:t.startDate,endDate:t.endDate,shiftConfig:a,teamLeaders:m,assignments:x,onAssignmentChange:()=>{d.R.getShiftAssignments(e).then(f)}}):(0,n.jsx)(l.Z,{color:"text.secondary",children:"Please configure shift settings first"})]})]})]}):(0,n.jsx)(r.Z,{sx:{p:2},children:"No festival selected"})}}}]);