"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["374"],{81433:function(e,s,i){i.r(s),i.d(s,{SensoryHubPage:()=>w});var t=i(85893),r=i(67294),l=i(98106),n=i(39467),a=i(33991),o=i(54757),c=i(12550),d=i(89126),x=i(61215),u=i(5214),h=i(59326),j=i(73892),y=i(11449),b=i(36762),Z=i(21143),p=i(54791),v=i(56099),f=i(7230),m=i(83502);let g=e=>{let{onSuccess:s}=e,{activeFestival:i}=(0,u.C)(),{activeSiteLocation:l}=(0,h.C)(),[g,w]=(0,r.useState)("look_around"),[C,S]=(0,r.useState)("public"),[k,W]=(0,r.useState)(""),[T,z]=(0,r.useState)(!1),[R,V]=(0,r.useState)(null),[_,P]=(0,r.useState)(null),A=(0,r.useCallback)(()=>!!i&&("crew"!==C||!!k.trim()),[i,C,k]),D=(0,r.useCallback)(async e=>{if(e.preventDefault(),!A())return void V("Please fill in all required fields");if(!i)return void V("No active festival selected");try{z(!0),V(null),P(null),await m.R.waitForInitialization();let e={documentType:"sensory-hub-visit",type:"sensory-hub-visit",festivalId:i._id,siteLocationId:l?.id,visitTimestamp:new Date().toISOString(),purpose:g,userType:C,..."crew"===C&&{teamName:k.trim()}};await m.R.addSensoryHubVisit(e),w("look_around"),S("public"),W(""),P("Visit recorded successfully!"),s&&s()}catch(e){V("Failed to record visit. Please try again.")}finally{z(!1)}},[i,l,g,C,k,A,s]),F=(0,r.useCallback)(e=>{w(e.target.value),V(null)},[]),I=(0,r.useCallback)(e=>{let s=e.target.value;S(s),"public"===s&&W(""),V(null)},[]),H=(0,r.useCallback)(e=>{W(e.target.value),V(null)},[]);return i?(0,t.jsxs)(d.Z,{sx:{p:3},children:[(0,t.jsx)(o.Z,{variant:"h6",sx:{mb:3,fontWeight:"bold",color:"text.primary"},children:"Record Sensory Hub Visit"}),R&&(0,t.jsx)(n.Z,{severity:"error",sx:{mb:3},children:R}),_&&(0,t.jsx)(n.Z,{severity:"success",sx:{mb:3},children:_}),(0,t.jsx)("form",{onSubmit:D,children:(0,t.jsxs)(c.Z,{container:!0,spacing:3,children:[(0,t.jsx)(c.Z,{size:{xs:12,md:6},children:(0,t.jsxs)(j.Z,{component:"fieldset",fullWidth:!0,children:[(0,t.jsx)(y.Z,{component:"legend",sx:{mb:1,fontWeight:"bold"},children:"Visit Purpose *"}),(0,t.jsxs)(b.Z,{value:g,onChange:F,row:!0,children:[(0,t.jsx)(Z.Z,{value:"look_around",control:(0,t.jsx)(p.Z,{}),label:"Look Around"}),(0,t.jsx)(Z.Z,{value:"use_service",control:(0,t.jsx)(p.Z,{}),label:"Use Service"})]})]})}),(0,t.jsx)(c.Z,{size:{xs:12,md:6},children:(0,t.jsxs)(j.Z,{component:"fieldset",fullWidth:!0,children:[(0,t.jsx)(y.Z,{component:"legend",sx:{mb:1,fontWeight:"bold"},children:"User Type *"}),(0,t.jsxs)(b.Z,{value:C,onChange:I,row:!0,children:[(0,t.jsx)(Z.Z,{value:"public",control:(0,t.jsx)(p.Z,{}),label:"Public"}),(0,t.jsx)(Z.Z,{value:"crew",control:(0,t.jsx)(p.Z,{}),label:"Crew"})]})]})}),"crew"===C&&(0,t.jsx)(c.Z,{size:{xs:12},children:(0,t.jsx)(v.Z,{fullWidth:!0,label:"Team Name",value:k,onChange:H,required:!0,placeholder:"Enter team name",helperText:"Required for crew visits",error:"crew"===C&&!k.trim()})}),(0,t.jsx)(c.Z,{size:{xs:12},children:(0,t.jsxs)(a.Z,{sx:{p:2,bgcolor:"grey.50",borderRadius:1},children:[(0,t.jsxs)(o.Z,{variant:"body2",color:"text.secondary",children:[(0,t.jsx)("strong",{children:"Festival:"})," ",i.name]}),l&&(0,t.jsxs)(o.Z,{variant:"body2",color:"text.secondary",children:[(0,t.jsx)("strong",{children:"Location:"})," ","arena"===l.type?"Arena":"Campsite"]})]})}),(0,t.jsx)(c.Z,{size:{xs:12},children:(0,t.jsx)(a.Z,{sx:{display:"flex",justifyContent:"flex-end",mt:2},children:(0,t.jsx)(f.Z,{type:"submit",variant:"contained",color:"primary",disabled:T||!A(),startIcon:T?(0,t.jsx)(x.Z,{size:20}):null,sx:{minWidth:120},children:T?"Recording...":"Record Visit"})})})]})})]}):(0,t.jsx)(d.Z,{sx:{p:3},children:(0,t.jsx)(n.Z,{severity:"warning",children:"Please select an active festival to record visits."})})},w=()=>{let{activeFestival:e}=(0,u.C)(),{activeSiteLocation:s}=(0,h.C)(),[i,j]=(0,r.useState)([]),[y,b]=(0,r.useState)(!0),[Z,p]=(0,r.useState)(null),v=(0,r.useCallback)(async()=>{if(!e){j([]),b(!1);return}try{let i;b(!0),p(null),await m.R.waitForInitialization(),i=s?await m.R.getSensoryHubVisitsByLocation(s.id):await m.R.getSensoryHubVisitsByFestival(e._id),j(i)}catch(e){p("Failed to load visits data")}finally{b(!1)}},[e,s]);(0,r.useEffect)(()=>{v()},[v]);let f=(0,r.useCallback)(()=>{v()},[v]),w=(0,r.useCallback)(()=>{let e=new Date().toDateString(),s=i.filter(s=>new Date(s.visitTimestamp).toDateString()===e),t=s.filter(e=>"look_around"===e.purpose).length,r=s.filter(e=>"use_service"===e.purpose).length,l=s.filter(e=>"crew"===e.userType).length,n=s.filter(e=>"public"===e.userType).length;return{total:s.length,lookAround:t,useService:r,crew:l,public:n}},[i])();return e?(0,t.jsxs)(l.Z,{maxWidth:"lg",sx:{py:4},children:[(0,t.jsxs)(a.Z,{sx:{mb:4},children:[(0,t.jsx)(o.Z,{variant:"h4",sx:{fontWeight:"bold",color:"text.primary",mb:1},children:(()=>{let e="Sensory Hub Visitor Tracking";if(s){let i="arena"===s.type?"Arena":"Campsite";return`${e} - ${i}`}return e})()}),(0,t.jsx)(o.Z,{variant:"body1",color:"text.secondary",children:"Record and track visitor interactions with the sensory hub"})]}),Z&&(0,t.jsx)(n.Z,{severity:"error",sx:{mb:3},children:Z}),(0,t.jsxs)(c.Z,{container:!0,spacing:3,children:[(0,t.jsx)(c.Z,{size:{xs:12,lg:8},children:(0,t.jsx)(g,{onSuccess:f})}),(0,t.jsx)(c.Z,{size:{xs:12,lg:4},children:(0,t.jsxs)(d.Z,{sx:{p:3},children:[(0,t.jsx)(o.Z,{variant:"h6",sx:{mb:2,fontWeight:"bold",color:"text.primary"},children:"Today's Visits"}),y?(0,t.jsx)(a.Z,{sx:{display:"flex",justifyContent:"center",py:2},children:(0,t.jsx)(x.Z,{size:24})}):(0,t.jsxs)(a.Z,{sx:{display:"flex",flexDirection:"column",gap:2},children:[(0,t.jsxs)(a.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,t.jsx)(o.Z,{variant:"body1",fontWeight:"medium",children:"Total Visits:"}),(0,t.jsx)(o.Z,{variant:"h6",color:"primary",fontWeight:"bold",children:w.total})]}),(0,t.jsxs)(a.Z,{children:[(0,t.jsx)(o.Z,{variant:"body2",color:"text.secondary",sx:{mb:1},children:"By Purpose:"}),(0,t.jsxs)(a.Z,{sx:{pl:2},children:[(0,t.jsxs)(a.Z,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,t.jsx)(o.Z,{variant:"body2",children:"Look Around:"}),(0,t.jsx)(o.Z,{variant:"body2",fontWeight:"medium",children:w.lookAround})]}),(0,t.jsxs)(a.Z,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,t.jsx)(o.Z,{variant:"body2",children:"Use Service:"}),(0,t.jsx)(o.Z,{variant:"body2",fontWeight:"medium",children:w.useService})]})]})]}),(0,t.jsxs)(a.Z,{children:[(0,t.jsx)(o.Z,{variant:"body2",color:"text.secondary",sx:{mb:1},children:"By User Type:"}),(0,t.jsxs)(a.Z,{sx:{pl:2},children:[(0,t.jsxs)(a.Z,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,t.jsx)(o.Z,{variant:"body2",children:"Public:"}),(0,t.jsx)(o.Z,{variant:"body2",fontWeight:"medium",children:w.public})]}),(0,t.jsxs)(a.Z,{sx:{display:"flex",justifyContent:"space-between"},children:[(0,t.jsx)(o.Z,{variant:"body2",children:"Crew:"}),(0,t.jsx)(o.Z,{variant:"body2",fontWeight:"medium",children:w.crew})]})]})]}),(0,t.jsxs)(a.Z,{sx:{pt:2,borderTop:1,borderColor:"divider"},children:[(0,t.jsxs)(o.Z,{variant:"body2",color:"text.secondary",children:[(0,t.jsx)("strong",{children:"Festival:"})," ",e.name]}),s&&(0,t.jsxs)(o.Z,{variant:"body2",color:"text.secondary",children:[(0,t.jsx)("strong",{children:"Location:"})," ","arena"===s.type?"Arena":"Campsite"]})]})]})]})})]})]}):(0,t.jsx)(l.Z,{maxWidth:"lg",sx:{py:4},children:(0,t.jsx)(n.Z,{severity:"warning",children:"Please select an active festival to access sensory hub visitor tracking."})})}}}]);