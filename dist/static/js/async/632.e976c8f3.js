"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["632"],{43119:function(e,i,t){t.d(i,{n:()=>u});var r=t(85893);t(67294);var a=t(64889),n=t(30925),s=t(81839),o=t(33991),l=t(56099),d=t(39467),c=t(13319),h=t(7230),m=t(54757);let u=e=>{let{showReAdmitModal:i,showConfirmReAdmit:t,showConfirmDischarge:u,reAdmitLocation:w,reAdmitNotes:f,dischargeNotes:g,dischargeTime:x,bayStatus:C,isSubmitting:b,onReAdmitLocationChange:D,onReAdmitNotesChange:y,onDischargeNotesChange:p,onDischargeTimeChange:A,onCloseReAdmit:j,onCloseConfirmReAdmit:S,onCloseConfirmDischarge:N,onConfirmReAdmit:Z,onConfirmDischarge:v,onShowConfirmReAdmit:W}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(a.Z,{open:i,onClose:j,children:[(0,r.jsx)(n.Z,{children:"Re-Admit Patient"}),(0,r.jsx)(s.Z,{children:(0,r.jsxs)(o.Z,{sx:{pt:2,width:"100%",minWidth:300},children:[(0,r.jsx)(l.Z,{fullWidth:!0,type:"number",label:"Location (Bay Number)",value:w,onChange:e=>D(Number(e.target.value)),sx:{mb:2}}),C&&(0,r.jsx)(d.Z,{severity:C.isOccupied?"error":"success",sx:{mb:2},children:C.message}),(0,r.jsx)(l.Z,{fullWidth:!0,multiline:!0,rows:3,label:"Additional Notes",value:f,onChange:e=>y(e.target.value)})]})}),(0,r.jsxs)(c.Z,{children:[(0,r.jsx)(h.Z,{onClick:j,children:"Cancel"}),(0,r.jsx)(h.Z,{onClick:W,variant:"contained",disabled:!w||(C?.isOccupied??!1),children:"Re-Admit"})]})]}),(0,r.jsxs)(a.Z,{open:t,onClose:S,children:[(0,r.jsx)(n.Z,{children:"Confirm Re-Admission"}),(0,r.jsx)(s.Z,{children:(0,r.jsx)(m.Z,{children:"Are you sure you want to re-admit this patient?"})}),(0,r.jsxs)(c.Z,{children:[(0,r.jsx)(h.Z,{onClick:S,children:"Cancel"}),(0,r.jsx)(h.Z,{onClick:Z,variant:"contained",color:"primary",children:"Confirm"})]})]}),(0,r.jsxs)(a.Z,{open:u,onClose:N,children:[(0,r.jsx)(n.Z,{children:"Discharge Patient"}),(0,r.jsx)(s.Z,{children:(0,r.jsxs)(o.Z,{sx:{pt:2,width:"100%",minWidth:300},children:[(0,r.jsx)(l.Z,{fullWidth:!0,type:"datetime-local",label:"Discharge Time",value:x,onChange:e=>A(e.target.value),sx:{mb:2},InputLabelProps:{shrink:!0}}),(0,r.jsx)(l.Z,{fullWidth:!0,multiline:!0,rows:3,label:"Discharge Notes",value:g,onChange:e=>p(e.target.value),required:!0,error:!g,helperText:g?"":"Discharge notes are required"})]})}),(0,r.jsxs)(c.Z,{children:[(0,r.jsx)(h.Z,{onClick:N,children:"Cancel"}),(0,r.jsx)(h.Z,{onClick:v,variant:"contained",color:"primary",disabled:b||!g,children:b?"Discharging...":"Confirm"})]})]})]})}},66156:function(e,i,t){t.d(i,{s:()=>o});var r=t(67294),a=t(83502),n=t(5214),s=t(59326);let o=()=>{let[e,i]=(0,r.useState)([]),[t,o]=(0,r.useState)(!0),[l,d]=(0,r.useState)(null),{activeFestival:c}=(0,n.C)(),{activeSiteLocation:h}=(0,s.C)(),m=(0,r.useCallback)(async()=>{o(!0);try{let e=c?await a.R.getAdmissionsByFestival(c._id):await a.R.getAdmissionsByFestival("all"),t=h?e.filter(e=>e.siteLocationId===h.id):e;i(t),d(null)}catch(e){d(e)}finally{o(!1)}},[c,h]);(0,r.useEffect)(()=>{m()},[m]);let u=(0,r.useCallback)(async e=>{try{await a.R.addAdmission(e),await m()}catch(e){throw e}},[m]);return{admissions:e,loading:t,error:l,addAdmission:u,updateAdmission:(0,r.useCallback)(async e=>{try{await a.R.updateAdmission(e),await m()}catch(e){throw e}},[m]),deleteAdmission:(0,r.useCallback)(async e=>{try{await a.R.deleteAdmission(e),await m()}catch(e){throw e}},[m]),refreshAdmissions:m}}},46381:function(e,i,t){t.r(i),t.d(i,{AdmissionsPage:()=>D,default:()=>y});var r=t(85893),a=t(67294),n=t(96872),s=t(33991),o=t(7230),l=t(61215),d=t(54757),c=t(89126),h=t(58308),m=t(82400),u=t(48501),w=t(44218),f=t(60630),g=t(43119);let x=e=>{let{admissions:i,loading:t,error:n,onDischarge:x,onEdit:C}=e,[b,D]=(0,a.useState)(!1),[y,p]=(0,a.useState)(null),[A,j]=(0,a.useState)(""),[S,N]=(0,a.useState)(new Date().toISOString().slice(0,16)),[Z,v]=(0,a.useState)(!1),W=e=>{if(!e._id)throw Error("Admission has no valid ID");return e._id},R=e=>{if(!e)return!1;let i=new Date(e);return i instanceof Date&&!isNaN(i.getTime())},T=e=>{if(!R(e.Attended))return"Unknown";try{return(0,u.B)(new Date(e.Attended),new Date,{addSuffix:!0})}catch(e){return"Unknown"}},k=e=>{if(!R(e.DischargeTime))return"";try{return(0,u.B)(new Date(e.DischargeTime),new Date,{addSuffix:!0})}catch(e){return""}},B=e=>{p(e),N(new Date().toISOString().slice(0,16)),j(""),D(!0)},G=async()=>{if(y&&A.trim()){v(!0);try{await x(W(y),A,S),I()}catch(e){}finally{v(!1)}}},I=()=>{D(!1),p(null),j(""),N(new Date().toISOString().slice(0,16))},M=[{field:"name",headerName:"Name",flex:1,minWidth:100,sortable:!0,filterable:!0,valueGetter:e=>{if(!e||!e.row)return"n/a";let i=e.row;return`${i.FirstName||""} ${i.Surname||""}`.trim().toLowerCase()||"n/a"},renderCell:e=>{let i=e.row;return`${i.FirstName||""} ${i.Surname||""}`.trim()||"N/A"}},{field:"Age",headerName:"Age",width:70,maxWidth:70,sortable:!0,filterable:!0,valueGetter:e=>{if(!e||!e.row)return null;let i=e.row;return i.Age||(i.DOB?(0,w.o)(new Date,new Date(i.DOB)):null)}},{field:"BaysOrChairs",headerName:"Bay/Chair",width:90,maxWidth:90,sortable:!0,filterable:!0},{field:"Location",headerName:"Location",width:90,maxWidth:90,sortable:!0,filterable:!0},{field:"timeSinceAdmission",headerName:"Time Since",width:110,sortable:!0,valueGetter:e=>{if(!e||!e.row)return -1;let i=e.row;return i.Attended?new Date(i.Attended).getTime():-1},renderCell:e=>T(e.row)},{field:"ReasonCategory",headerName:"Reason",flex:.5,minWidth:90,sortable:!0,filterable:!0},{field:"actions",headerName:"Actions",width:140,sortable:!1,renderCell:e=>(0,r.jsxs)(s.Z,{display:"flex",gap:1,children:[(0,r.jsx)(o.Z,{variant:"contained",size:"small",onClick:()=>C(e.row),sx:{bgcolor:"primary.main",fontSize:"0.7rem",py:.5,px:1,minWidth:"auto"},children:"Edit"}),(0,r.jsx)(o.Z,{variant:"contained",size:"small",onClick:()=>B(e.row),sx:{bgcolor:"error.main",fontSize:"0.7rem",py:.5,px:1,minWidth:"auto"},children:"Discharge"})]})}],$=[{field:"name",headerName:"Name",flex:1,minWidth:100,sortable:!0,filterable:!0,valueGetter:e=>{if(!e||!e.row)return"n/a";let i=e.row;return`${i.FirstName||""} ${i.Surname||""}`.trim().toLowerCase()||"n/a"},renderCell:e=>{let i=e.row;return`${i.FirstName||""} ${i.Surname||""}`.trim()||"N/A"}},{field:"Age",headerName:"Age",width:70,maxWidth:70,sortable:!0,filterable:!0,valueGetter:e=>{if(!e||!e.row)return null;let i=e.row;return i.Age||(i.DOB?(0,w.o)(new Date,new Date(i.DOB)):null)}},{field:"BaysOrChairs",headerName:"Bay/Chair",width:90,maxWidth:90,sortable:!0,filterable:!0},{field:"Location",headerName:"Location",width:90,maxWidth:90,sortable:!0,filterable:!0},{field:"DischargeTime",headerName:"Discharged",width:100,sortable:!0,type:"dateTime",valueGetter:e=>{if(!e||!e.row)return null;let i=e.row;return i.DischargeTime?new Date(i.DischargeTime):null},renderCell:e=>{let i=e.row;return R(i.DischargeTime)?(0,f.WU)(new Date(i.DischargeTime),"Pp"):""}},{field:"timeSinceDischarge",headerName:"Time Since",width:110,sortable:!0,valueGetter:e=>{if(!e||!e.row)return -1;let i=e.row;return i.DischargeTime?new Date(i.DischargeTime).getTime():-1},renderCell:e=>k(e.row)},{field:"ReasonCategory",headerName:"Reason",flex:.5,minWidth:90,sortable:!0,filterable:!0},{field:"actions",headerName:"Actions",width:80,sortable:!1,renderCell:e=>(0,r.jsx)(o.Z,{variant:"contained",size:"small",onClick:()=>C(e.row),sx:{bgcolor:"primary.main",fontSize:"0.7rem",py:.5,px:1,minWidth:"auto"},children:"Edit"})}];if(t)return(0,r.jsx)(s.Z,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"200px",children:(0,r.jsx)(l.Z,{})});if(n)return(0,r.jsx)(s.Z,{p:2,children:(0,r.jsxs)(d.Z,{color:"error",children:["Error loading admissions: ",n.message]})});let z=i.filter(e=>e.InBayNow),H=i.filter(e=>!e.InBayNow);return(0,r.jsxs)(s.Z,{children:[(0,r.jsx)(d.Z,{variant:"h6",gutterBottom:!0,children:"In Bay Now"}),(0,r.jsx)(c.Z,{sx:{height:"auto",minHeight:"40vh",marginBottom:2},children:(0,r.jsx)(h._,{rows:z,columns:M,getRowId:W,density:"compact",slots:{toolbar:m.n},slotProps:{toolbar:{showQuickFilter:!0,quickFilterProps:{debounceMs:300}}},initialState:{sorting:{sortModel:[{field:"timeSinceAdmission",sort:"desc"}]},columns:{columnVisibilityModel:{ReasonCategory:window.innerWidth>600,timeSinceAdmission:window.innerWidth>500}}},sx:{"& .MuiDataGrid-cell":{fontSize:"0.8rem",padding:"0 8px"},"& .MuiDataGrid-columnHeaders":{fontSize:"0.8rem"},"& .MuiDataGrid-toolbarContainer":{padding:"4px 8px"},"& .MuiDataGrid-columnHeaderTitle":{fontWeight:"bold",fontSize:"0.8rem"},border:"none",width:"100%",overflow:"hidden"},autoHeight:!0})}),(0,r.jsx)(d.Z,{variant:"h6",gutterBottom:!0,children:"Discharged Clients"}),(0,r.jsx)(c.Z,{sx:{height:"auto",minHeight:"40vh"},children:(0,r.jsx)(h._,{rows:H,columns:$,getRowId:W,density:"compact",slots:{toolbar:m.n},slotProps:{toolbar:{showQuickFilter:!0,quickFilterProps:{debounceMs:300}}},initialState:{sorting:{sortModel:[{field:"DischargeTime",sort:"desc"}]},columns:{columnVisibilityModel:{ReasonCategory:window.innerWidth>600,timeSinceDischarge:window.innerWidth>500,DischargeTime:window.innerWidth>450}}},sx:{"& .MuiDataGrid-cell":{fontSize:"0.8rem",padding:"0 8px"},"& .MuiDataGrid-columnHeaders":{fontSize:"0.8rem"},"& .MuiDataGrid-toolbarContainer":{padding:"4px 8px"},"& .MuiDataGrid-columnHeaderTitle":{fontWeight:"bold",fontSize:"0.8rem"},border:"none",width:"100%",overflow:"hidden"},autoHeight:!0})}),(0,r.jsx)(g.n,{showReAdmitModal:!1,showConfirmReAdmit:!1,showConfirmDischarge:b,reAdmitLocation:0,reAdmitNotes:"",dischargeNotes:A,dischargeTime:S,bayStatus:null,isSubmitting:Z,onReAdmitLocationChange:()=>{},onReAdmitNotesChange:()=>{},onDischargeNotesChange:j,onDischargeTimeChange:N,onCloseReAdmit:()=>{},onCloseConfirmReAdmit:()=>{},onCloseConfirmDischarge:I,onConfirmReAdmit:()=>{},onConfirmDischarge:G,onShowConfirmReAdmit:()=>{}})]})};var C=t(66156),b=t(83502);let D=()=>{let{admissions:e,loading:i,error:t,updateAdmission:s,refreshAdmissions:o}=(0,C.s)(),l=(0,n.s0)(),d=async(i,t,r)=>{let a=e.find(e=>e._id===i||e._id===i);if(a){let e=new Date(r).toLocaleTimeString("en-GB",{hour:"2-digit",minute:"2-digit"}),i=`[${e}] DISCHARGE - ${t}`,n={...a,InBayNow:!1,DischargeTime:r,status:"discharged",AdmissionNotes:a.AdmissionNotes?`${a.AdmissionNotes}
${i}`:i,AdditionalNotes:[...a.AdditionalNotes||[],{timestamp:r,note:`Discharge Notes: ${t}`,author:"Staff"}],History:[...a.History||[],{timestamp:r,action:"Discharged",details:t,author:"Staff"}]};await s(n),await o()}};return(0,a.useEffect)(()=>{(async()=>{try{await b.R.fixInconsistentAdmissions(),await o()}catch(e){}})()},[o]),(0,r.jsx)(x,{admissions:e,loading:i,error:t,onDischarge:d,onEdit:e=>{l(`/edit-admission/${e._id}`)}})},y=D}}]);