"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["934"],{95809:function(e,t,n){n.r(t),n.d(t,{FrontOfHousePage:()=>Q});var r=n(85893),l=n(67294),s=n(83502),i=n(5214),a=n(59326),o=n(60630),c=n(69326),d=n(64889),u=n(30925),m=n(81839),x=n(33991),p=n(54757),h=n(56099),b=n(13319),g=n(7230);let f=e=>{let{open:t,onClose:n,onConfirm:s,itemName:i,label:a}=e,[o,c]=(0,l.useState)("1"),[f,y]=(0,l.useState)(1),[Z,j]=(0,l.useState)(null);(0,l.useEffect)(()=>{t&&(c("1"),y(1),j(null))},[t]);let C=()=>{if(""===o)return void j("Please enter a valid number");let e=parseInt(o,10);!Z&&e>0&&(s(e),n())};return(0,r.jsxs)(d.Z,{open:t,onClose:n,PaperProps:{sx:{width:"90%",maxWidth:"400px",borderRadius:2}},children:[(0,r.jsxs)(u.Z,{sx:{pb:1,color:"primary.main"},children:["Add ",a," Items"]}),(0,r.jsxs)(m.Z,{children:[(0,r.jsx)(x.Z,{sx:{mb:2},children:(0,r.jsxs)(p.Z,{variant:"body2",color:"text.secondary",children:["Enter the number of ",a," items to add:"]})}),(0,r.jsx)(h.Z,{autoFocus:!0,margin:"dense",label:"Quantity",type:"number",fullWidth:!0,variant:"outlined",value:o,onChange:e=>{let t=e.target.value;if(c(t),""===t)return void j(null);let n=parseInt(t,10);isNaN(n)?j("Please enter a valid number"):n<1?j("Please enter a number greater than 0"):n>100?j("Maximum quantity is 100"):(j(null),y(n))},onKeyDown:e=>{"Enter"===e.key&&(e.preventDefault(),C())},error:!!Z,helperText:Z||"Enter the number of items to add",inputProps:{inputMode:"numeric",pattern:"[0-9]*",sx:{fontSize:"1.2rem",padding:"12px"}},sx:{"& input[type=number]::-webkit-inner-spin-button, & input[type=number]::-webkit-outer-spin-button":{"-webkit-appearance":"none",margin:0},"& input[type=number]":{"-moz-appearance":"textfield"}}})]}),(0,r.jsxs)(b.Z,{sx:{p:2,pt:1},children:[(0,r.jsx)(g.Z,{onClick:n,color:"primary",variant:"outlined",sx:{mr:1,px:3,py:1},children:"Cancel"}),(0,r.jsx)(g.Z,{onClick:C,color:"primary",variant:"contained",disabled:!!Z,sx:{px:3,py:1},children:"Add Items"})]})]})},y=e=>{let{onLongPress:t,onClick:n,threshold:r=500}=e,s=(0,l.useRef)(null),i=(0,l.useRef)(!1),a=(0,l.useCallback)(e=>{i.current=!1,e.target&&e.preventDefault(),s.current=window.setTimeout(()=>{i.current=!0,t(e)},r)},[t,r]),o=(0,l.useCallback)(function(e){let t=!(arguments.length>1)||void 0===arguments[1]||arguments[1];e.type.includes("touch")&&e.preventDefault(),s.current&&(clearTimeout(s.current),s.current=null),t&&!i.current&&n&&n(e)},[n]);return{onMouseDown:a,onMouseUp:o,onMouseLeave:e=>o(e,!1),onTouchStart:a,onTouchEnd:o,onTouchCancel:e=>o(e,!1),cancelTimer:(0,l.useCallback)(()=>{s.current&&(clearTimeout(s.current),s.current=null)},[])}};var Z=n(32153),j=n(13400),C=n(61215),w=n(17047);let S=e=>{let{itemName:t,label:n,count:l,isUpdating:s,onItemClick:i,onLongPress:a,IconComponent:o}=e,c=y({onClick:()=>i(t),onLongPress:()=>a(t,n),threshold:500});return(0,r.jsx)(Z.Z,{...c,sx:{width:180,height:180,display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",cursor:"pointer",opacity:s?.5:1,transition:"all 0.2s","&:hover":{boxShadow:3},userSelect:"none",touchAction:"none"},children:(0,r.jsxs)(j.Z,{spacing:2,alignItems:"center",children:[(0,r.jsxs)(x.Z,{sx:{width:80,height:80,display:"flex",alignItems:"center",justifyContent:"center",border:1,borderColor:"grey.200",borderRadius:1,bgcolor:"white",position:"relative"},children:[(0,r.jsx)(o,{sx:{fontSize:48,color:"#662D91"}}),s&&(0,r.jsx)(C.Z,{size:24,sx:{position:"absolute",top:-12,right:-12,color:"primary.main"}})]}),(0,r.jsxs)(j.Z,{alignItems:"center",spacing:1,children:[(0,r.jsx)(p.Z,{variant:"body2",sx:{fontWeight:500,textAlign:"center",whiteSpace:"pre-line",lineHeight:"tight"},children:n}),(0,r.jsx)(w.Z,{label:l,sx:{bgcolor:"purple.100",color:"purple.600",fontWeight:"bold",fontSize:"0.875rem"}})]})]})})};var v=n(99371),k=n(89707),z=n(54384),I=n(8242),R=n(63839),W=n(57526),P=n(59224),D=n(24665),E=n(40697),F=n(43570),T=n(42137),M=n(63659),N=n(24472),U=n(39776),H=n(12550),A=n(92291),L=n(64286),_=n(58308),G=n(98106),O=n(60488),q=n(39467),$=n(89126),B=n(23279),K=n.n(B);let Q=()=>{let{activeFestival:e}=(0,i.C)(),{activeSiteLocation:t}=(0,a.C)(),[n,d]=(0,l.useState)(!0),[u,m]=(0,l.useState)([]),[h,b]=(0,l.useState)(null),[y,Z]=(0,l.useState)(null),[j,B]=(0,l.useState)(!1),[Q,J]=(0,l.useState)(!1),[V,X]=(0,l.useState)(""),[Y,ee]=(0,l.useState)(!1),[et,en]=(0,l.useState)(null);(0,l.useEffect)(()=>{let e=async()=>{try{let e=await s.R.hasPendingChanges();J(e)}catch(e){}},t=s.R.addSyncListener(()=>{e()});return e(),()=>{try{t()}catch(e){}}},[]);let er=(0,l.useCallback)(async()=>{if(!e){m([]),d(!1);return}try{d(!0),Z(null);let n=await s.R.getItemCountsByFestival(e._id),r=t?n.filter(e=>e.siteLocationId===t.id):n;m(r)}catch(e){Z("Failed to load counts")}finally{d(!1)}},[e,t]),el=(0,l.useCallback)(async()=>{if(!j)try{B(!0),Z(null),X("syncing"),await s.R.manualSync(),await er(),X("synced")}catch(e){Z("Failed to sync with server"),X("error")}finally{B(!1)}},[j,er]),es=(0,l.useCallback)(K()(async function(n){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:1;if(e)try{await s.R.addOrUpdateItemCount(n,e._id,t?.id,r),await er()}catch(e){Z("Failed to update count")}finally{b(null)}},300),[e,t,er]),ei=(0,l.useCallback)(t=>{e&&h!==t&&(b(t),Z(null),es(t,1))},[e,h,es]),ea=(0,l.useCallback)((e,t)=>{en({name:e,label:t}),ee(!0)},[]),eo=(0,l.useCallback)(e=>{et&&e>0&&(b(et.name),Z(null),es(et.name,e))},[et,es]);(0,l.useEffect)(()=>{er()},[er]);let ec=(0,l.useCallback)(e=>{let t=u.find(e=>{if(!e.timestamp)return!1;let t=new Date(e.timestamp),n=new Date;return t.toDateString()===n.toDateString()});return t&&t[e]||0},[u]),ed=(0,l.useCallback)((e,t,n)=>(0,r.jsx)(S,{itemName:e,label:n,count:ec(e),isUpdating:h===e,onItemClick:ei,onLongPress:ea,IconComponent:t}),[h,ei,ec,ea]),eu=[{field:"item",headerName:"Item",flex:1},{field:"count",headerName:"Count",width:120,renderCell:e=>(0,r.jsx)(w.Z,{label:e.row.count,sx:{bgcolor:"purple.100",color:"purple.600",fontWeight:"bold",fontSize:"0.875rem"}})},{field:"lastUpdated",headerName:"Last Updated",flex:1,renderCell:e=>e.row.lastUpdated?(0,o.WU)((0,c.D)(e.row.lastUpdated),"yyyy-MM-dd HH:mm:ss"):"N/A"}],em=u.flatMap(e=>Object.entries(e).filter(e=>{let[t,n]=e;return"number"==typeof n&&n>0}).map(t=>{let[n,r]=t;return{id:`${e._id}_${n}`,item:n,count:r,lastUpdated:e.timestamp||null}})),ex=t?`Front of House - ${"arena"===t.type?"Arena":"Campsite"}`:"Front of House";return(0,r.jsxs)(G.Z,{maxWidth:"lg",sx:{py:4},children:[(0,r.jsxs)(x.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:4},children:[(0,r.jsx)(p.Z,{variant:"h4",sx:{fontWeight:"bold",color:"text.primary"},children:ex}),(0,r.jsx)(O.Z,{title:y||"",children:(0,r.jsx)(g.Z,{variant:"contained",color:y?"error":Q?"warning":"primary",startIcon:j?(0,r.jsx)(C.Z,{size:20}):y?(0,r.jsx)(L.Z,{}):(0,r.jsx)(A.Z,{}),onClick:el,disabled:j,children:j?"Syncing...":y?"Sync Failed":Q?"Changes Pending":"Sync Now"})})]}),y&&(0,r.jsx)(q.Z,{severity:"error",sx:{mb:3},children:y}),(0,r.jsxs)(H.Z,{container:!0,spacing:3,sx:{mb:4},children:[(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("Suncream",v.Z,"Suncream")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("Poncho",k.Z,"Poncho")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("Water",z.Z,"Water")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("SanitaryProducts",I.Z,"Sanitary\nProducts")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("Earplugs",R.Z,"Earplugs")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("Condoms",W.Z,"Condoms")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("ChildrensWristbands",P.Z,"Children's\nWristbands")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("GeneralWristbands",D.Z,"General\nWristbands")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("Charging",E.Z,"Charging")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("Sanitizer",F.Z,"Sanitizer")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("ToiletRoll",T.Z,"Toilet Roll")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("GeneralEnqs",M.Z,"General\nEnquiries")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("HotWater",N.Z,"Hot Water")}),(0,r.jsx)(H.Z,{size:{xs:12,sm:6,md:4,lg:3},children:ed("RestAndRecuperation",U.Z,"Rest and\nRecuperation")})]}),(0,r.jsxs)($.Z,{elevation:3,sx:{bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,p:3},children:[(0,r.jsx)(x.Z,{sx:{display:"flex",alignItems:"center",mb:2},children:(0,r.jsx)(p.Z,{variant:"h6",color:"primary",children:"Items Given Out"})}),(0,r.jsx)(x.Z,{sx:{height:400,width:"100%"},children:n?(0,r.jsx)(x.Z,{sx:{p:3},children:(0,r.jsx)(p.Z,{children:"Loading items..."})}):(0,r.jsx)(_._,{rows:em,columns:eu,density:"compact",disableRowSelectionOnClick:!0,sx:{"& .MuiDataGrid-cell":{fontSize:"0.875rem"},border:"none",bgcolor:"background.paper",backdropFilter:"blur(8px)"}})})]}),(0,r.jsx)(f,{open:Y,onClose:()=>ee(!1),onConfirm:eo,itemName:et?.name||"",label:et?.label||""})]})}}}]);