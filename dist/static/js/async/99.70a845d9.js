"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["99"],{66236:function(e,t,s){s.d(t,{L:()=>U});var a=s(85893),l=s(67294),i=s(33991),n=s(61215),r=s(54757),o=s(39467),c=s(95438),d=s(89126),h=s(21183),x=s(52104),p=s(98913),u=s(60583),m=s(60187),j=s(17047),f=s(60488),Z=s(38953),b=s(70779),y=s(97454),g=s(60630),v=s(83502),C=s(64889),w=s(30925),k=s(81839),D=s(13319),S=s(7230);let F=e=>{let{feedback:t,open:s,onClose:l}=e;return t?(0,a.jsxs)(C.Z,{open:s,onClose:l,maxWidth:"md",fullWidth:!0,children:[(0,a.jsx)(w.Z,{children:"Feedback Details"}),(0,a.jsxs)(k.Z,{children:[(0,a.jsxs)(i.Z,{sx:{mb:3},children:[(0,a.jsx)(r.Z,{variant:"overline",color:"text.secondary",children:"Submitted by"}),(0,a.jsx)(r.Z,{variant:"body1",gutterBottom:!0,children:t.name})]}),(0,a.jsxs)(i.Z,{sx:{mb:3},children:[(0,a.jsx)(r.Z,{variant:"overline",color:"text.secondary",children:"Page"}),(0,a.jsx)(r.Z,{variant:"body1",gutterBottom:!0,children:t.page})]}),(0,a.jsxs)(i.Z,{sx:{mb:3},children:[(0,a.jsx)(r.Z,{variant:"overline",color:"text.secondary",children:"Status"}),(0,a.jsx)(i.Z,{children:(0,a.jsx)(j.Z,{label:t.resolved?"Resolved":"Open",color:t.resolved?"success":"default",size:"small"})})]}),(0,a.jsxs)(i.Z,{sx:{mb:3},children:[(0,a.jsx)(r.Z,{variant:"overline",color:"text.secondary",children:"Timestamp"}),(0,a.jsx)(r.Z,{variant:"body1",gutterBottom:!0,children:(0,g.WU)(new Date(t.timestamp||0),"dd/MM/yyyy HH:mm")})]}),(0,a.jsxs)(i.Z,{children:[(0,a.jsx)(r.Z,{variant:"overline",color:"text.secondary",children:"Feedback"}),(0,a.jsx)(r.Z,{variant:"body1",component:"div",sx:{whiteSpace:"pre-wrap",bgcolor:"grey.100",p:2,borderRadius:1,mt:1},children:t.feedback})]})]}),(0,a.jsx)(D.Z,{children:(0,a.jsx)(S.Z,{onClick:l,children:"Close"})})]}):null},U=()=>{let[e,t]=(0,l.useState)([]),[s,C]=(0,l.useState)(!0),[w,k]=(0,l.useState)(null),[D,S]=(0,l.useState)(null),U=async()=>{try{k(null),C(!0);let e=await v.R.getAllFeedback();if(!Array.isArray(e))throw Error("Invalid feedback data received");t(e)}catch(e){k(e instanceof Error?e.message:"Failed to load feedback. Please try again.")}finally{C(!1)}};(0,l.useEffect)(()=>{U()},[]);let L=async(e,t)=>{t.stopPropagation();try{k(null),await v.R.updateFeedbackStatus(e,!0),await U()}catch(e){k("Failed to update feedback status. Please try again.")}},z=async(e,t)=>{if(t.stopPropagation(),window.confirm("Are you sure you want to delete this feedback?"))try{k(null),await v.R.deleteFeedback(e),await U()}catch(e){k("Failed to delete feedback. Please try again.")}},R=e=>{S(e)};return s?(0,a.jsx)(i.Z,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:200,children:(0,a.jsx)(n.Z,{})}):(0,a.jsxs)(i.Z,{children:[(0,a.jsx)(r.Z,{variant:"h6",gutterBottom:!0,children:"User Feedback"}),w&&(0,a.jsx)(o.Z,{severity:"error",sx:{mb:2},children:w}),(0,a.jsx)(c.Z,{component:d.Z,children:(0,a.jsxs)(h.Z,{children:[(0,a.jsx)(x.Z,{children:(0,a.jsxs)(p.Z,{children:[(0,a.jsx)(u.Z,{children:"Date"}),(0,a.jsx)(u.Z,{children:"Name"}),(0,a.jsx)(u.Z,{children:"Page"}),(0,a.jsx)(u.Z,{children:"Feedback"}),(0,a.jsx)(u.Z,{children:"Status"}),(0,a.jsx)(u.Z,{children:"Actions"})]})}),(0,a.jsxs)(m.Z,{children:[e.map(e=>(0,a.jsxs)(p.Z,{onClick:()=>R(e),sx:{cursor:"pointer","&:hover":{bgcolor:"action.hover"}},children:[(0,a.jsx)(u.Z,{children:(0,g.WU)(new Date(e.timestamp||0),"dd/MM/yyyy HH:mm")}),(0,a.jsx)(u.Z,{children:e.name}),(0,a.jsx)(u.Z,{children:e.page}),(0,a.jsx)(u.Z,{children:e.feedback.length>100?`${e.feedback.substring(0,100)}...`:e.feedback}),(0,a.jsx)(u.Z,{children:(0,a.jsx)(j.Z,{label:e.resolved?"Resolved":"Open",color:e.resolved?"success":"default",size:"small"})}),(0,a.jsxs)(u.Z,{children:[!e.resolved&&(0,a.jsx)(f.Z,{title:"Mark as resolved",children:(0,a.jsx)(Z.Z,{onClick:t=>L(e._id,t),size:"small",color:"primary",children:(0,a.jsx)(b.Z,{})})}),(0,a.jsx)(f.Z,{title:"Delete",children:(0,a.jsx)(Z.Z,{onClick:t=>z(e._id,t),size:"small",color:"error",children:(0,a.jsx)(y.Z,{})})})]})]},e._id)),0===e.length&&(0,a.jsx)(p.Z,{children:(0,a.jsx)(u.Z,{colSpan:6,align:"center",children:"No feedback available"})})]})]})}),(0,a.jsx)(F,{feedback:D,open:null!==D,onClose:()=>S(null)})]})}},48066:function(e,t,s){s.r(t),s.d(t,{FestivalManagementPage:()=>et});var a=s(85893),l=s(67294),i=s(83502),n=s(5214),r=s(96847),o=s(21143),c=s(64889),d=s(30925),h=s(54757),x=s(38953),p=s(81839),u=s(12550),m=s(56099),j=s(73892),f=s(1156),Z=s(48346),b=s(73876),y=s(87884),g=s(89276),v=s(33991),C=s(13319),w=s(7230),k=s(89425);let D=(0,r.ZP)(o.Z)({margin:0,flexDirection:"column-reverse",".MuiFormControlLabel-label":{marginLeft:0,marginTop:4,fontSize:"0.875rem"}}),S=e=>{let{festival:t,isOpen:s,onClose:n,onSave:r}=e,[S,F]=(0,l.useState)({name:t.name,startDate:t.startDate,endDate:t.endDate,location:t.location,type:t.type,mainUrl:t.mainUrl||"",mapUrl:t.mapUrl||"",travelInfoUrl:t.travelInfoUrl||"",faqsUrl:t.faqsUrl||"",showAdmissions:t.showAdmissions??!0,showFrontOfHouse:t.showFrontOfHouse??!0,showLostProperty:t.showLostProperty??!0,showShifts:t.showShifts??!1,hasMultipleLocations:t.hasMultipleLocations,locations:t.locations||[]}),[U,L]=(0,l.useState)(t.locations?.some(e=>"arena"===e.type)??!0),[z,R]=(0,l.useState)(t.locations?.some(e=>"campsite"===e.type)??!1);(0,l.useEffect)(()=>{F({name:t.name,startDate:t.startDate,endDate:t.endDate,location:t.location,type:t.type,mainUrl:t.mainUrl||"",mapUrl:t.mapUrl||"",travelInfoUrl:t.travelInfoUrl||"",faqsUrl:t.faqsUrl||"",showAdmissions:t.showAdmissions??!0,showFrontOfHouse:t.showFrontOfHouse??!0,showLostProperty:t.showLostProperty??!0,showShifts:t.showShifts??!1,hasMultipleLocations:t.hasMultipleLocations,locations:t.locations||[]}),L(t.locations?.some(e=>"arena"===e.type)??!0),R(t.locations?.some(e=>"campsite"===e.type)??!1)},[t]);let A=async e=>{e.preventDefault();try{let e=[];if(U){let s=t.locations?.find(e=>"arena"===e.type);e.push(s||{id:`arena_${Date.now()}`,name:"Arena",type:"arena",description:"Main arena area"})}if(z){let s=t.locations?.find(e=>"campsite"===e.type);e.push(s||{id:`campsite_${Date.now()}`,name:"Campsite",type:"campsite",description:"Festival campsite"})}await i.R.updateFestival({...t,...S,hasMultipleLocations:e.length>0,locations:e}),r(),n()}catch(e){}};return(0,a.jsxs)(c.Z,{open:s,onClose:n,maxWidth:"sm",fullWidth:!0,sx:{"& .MuiDialog-paper":{m:2,maxHeight:"calc(100% - 32px)"}},children:[(0,a.jsxs)(d.Z,{sx:{m:0,p:2},children:[(0,a.jsx)(h.Z,{variant:"h6",children:"Edit Festival"}),(0,a.jsx)(x.Z,{onClick:n,sx:{position:"absolute",right:8,top:8,color:"text.secondary"},children:(0,a.jsx)(k.Z,{})})]}),(0,a.jsxs)("form",{onSubmit:A,children:[(0,a.jsx)(p.Z,{dividers:!0,children:(0,a.jsxs)(u.Z,{container:!0,spacing:2,children:[(0,a.jsx)(u.Z,{size:{xs:12},children:(0,a.jsx)(m.Z,{fullWidth:!0,required:!0,label:"Name",value:S.name,onChange:e=>F({...S,name:e.target.value})})}),(0,a.jsx)(u.Z,{size:{xs:12,sm:6},children:(0,a.jsx)(m.Z,{fullWidth:!0,required:!0,type:"date",label:"Start Date",value:S.startDate,onChange:e=>F({...S,startDate:e.target.value}),InputLabelProps:{shrink:!0}})}),(0,a.jsx)(u.Z,{size:{xs:12,sm:6},children:(0,a.jsx)(m.Z,{fullWidth:!0,required:!0,type:"date",label:"End Date",value:S.endDate,onChange:e=>F({...S,endDate:e.target.value}),InputLabelProps:{shrink:!0}})}),(0,a.jsx)(u.Z,{size:{xs:12},children:(0,a.jsx)(m.Z,{fullWidth:!0,required:!0,label:"Location",value:S.location,onChange:e=>F({...S,location:e.target.value})})}),(0,a.jsx)(u.Z,{size:{xs:12},children:(0,a.jsxs)(j.Z,{fullWidth:!0,children:[(0,a.jsx)(f.Z,{children:"Type"}),(0,a.jsxs)(Z.Z,{value:S.type,label:"Type",onChange:e=>F({...S,type:e.target.value}),children:[(0,a.jsx)(b.Z,{value:"festival",children:"Festival"}),(0,a.jsx)(b.Z,{value:"regular_event",children:"Regular Event"})]})]})}),(0,a.jsxs)(u.Z,{size:{xs:12},children:[(0,a.jsx)(h.Z,{variant:"subtitle1",gutterBottom:!0,children:"Festival Sites"}),(0,a.jsxs)(y.Z,{children:[(0,a.jsx)(o.Z,{control:(0,a.jsx)(g.Z,{checked:U,onChange:e=>L(e.target.checked)}),label:"Arena"}),(0,a.jsx)(o.Z,{control:(0,a.jsx)(g.Z,{checked:z,onChange:e=>R(e.target.checked)}),label:"Campsite"})]})]}),(0,a.jsx)(u.Z,{size:{xs:12},children:(0,a.jsx)(m.Z,{fullWidth:!0,type:"url",label:"Main Website URL",value:S.mainUrl,onChange:e=>F({...S,mainUrl:e.target.value}),placeholder:"https://example.com"})}),(0,a.jsx)(u.Z,{size:{xs:12},children:(0,a.jsx)(m.Z,{fullWidth:!0,type:"url",label:"Map URL",value:S.mapUrl,onChange:e=>F({...S,mapUrl:e.target.value}),placeholder:"https://example.com/map"})}),(0,a.jsx)(u.Z,{size:{xs:12},children:(0,a.jsx)(m.Z,{fullWidth:!0,type:"url",label:"Travel Information URL",value:S.travelInfoUrl,onChange:e=>F({...S,travelInfoUrl:e.target.value}),placeholder:"https://example.com/travel"})}),(0,a.jsx)(u.Z,{size:{xs:12},children:(0,a.jsx)(m.Z,{fullWidth:!0,type:"url",label:"FAQs URL",value:S.faqsUrl,onChange:e=>F({...S,faqsUrl:e.target.value}),placeholder:"https://example.com/faqs"})}),(0,a.jsx)(u.Z,{size:{xs:12},children:(0,a.jsxs)(v.Z,{display:"flex",justifyContent:"flex-end",gap:2,children:[(0,a.jsx)(D,{control:(0,a.jsx)(g.Z,{checked:S.showAdmissions,onChange:e=>F({...S,showAdmissions:e.target.checked})}),label:"Admissions"}),(0,a.jsx)(D,{control:(0,a.jsx)(g.Z,{checked:S.showFrontOfHouse,onChange:e=>F({...S,showFrontOfHouse:e.target.checked})}),label:"Front of House"}),(0,a.jsx)(D,{control:(0,a.jsx)(g.Z,{checked:S.showLostProperty,onChange:e=>F({...S,showLostProperty:e.target.checked})}),label:"Lost Property"}),(0,a.jsx)(D,{control:(0,a.jsx)(g.Z,{checked:S.showShifts,onChange:e=>F({...S,showShifts:e.target.checked})}),label:"Shifts"})]})})]})}),(0,a.jsxs)(C.Z,{sx:{px:3,py:2},children:[(0,a.jsx)(w.Z,{onClick:n,variant:"outlined",color:"inherit",children:"Cancel"}),(0,a.jsx)(w.Z,{type:"submit",variant:"contained",sx:{bgcolor:"ithink.pink"},children:"Save Changes"})]})]})]})};var F=s(87393),U=s(78319),L=s(69326),z=s(83588),R=s(61215),A=s(39467),P=s(13400),I=s(32153),W=s(90624),_=s(97454),H=s(49696);let E=()=>{let{festivals:e,activeFestival:t,setActiveFestival:s,loading:r,error:o}=(0,n.C)(),[u,m]=(0,l.useState)(!1),[j,f]=(0,l.useState)(null),[Z,b]=(0,l.useState)(null),y=e=>{f(e),m(!0)},g=async()=>{if(j){b(null);try{await i.R.deleteFestival(j._id),t?._id===j._id&&await s(null),m(!1),f(null)}catch(e){b("Failed to delete festival")}}};return r&&0===e.length?(0,a.jsx)(v.Z,{sx:{display:"flex",justifyContent:"center",p:2},children:(0,a.jsx)(R.Z,{})}):Z?(0,a.jsx)(A.Z,{severity:"error",children:Z}):(0,a.jsxs)(P.Z,{spacing:1,children:[e.map(l=>(0,a.jsxs)(I.Z,{variant:"outlined",sx:{p:1.5,display:"flex",alignItems:"center",justifyContent:"space-between",bgcolor:"background.paper"},children:[(0,a.jsx)(w.Z,{onClick:()=>{let t=e.find(e=>e._id===l._id);t&&s(t)},sx:{justifyContent:"flex-start",textAlign:"left",p:1,flex:1,color:t?._id===l._id?"ithink.pink":"text.primary","&:hover":{bgcolor:"action.hover"}},children:(0,a.jsxs)(P.Z,{spacing:.5,children:[(0,a.jsx)(h.Z,{variant:"body1",sx:{fontWeight:t?._id===l._id?600:400},children:l.name}),(0,U._)(new Date,{start:(0,L.D)(l.startDate),end:(0,L.D)(l.endDate)})&&(0,a.jsxs)(h.Z,{variant:"body2",color:"text.secondary",children:["Day ",(0,z.j)(new Date,(0,L.D)(l.startDate))+1," of"," ",(0,z.j)((0,L.D)(l.endDate),(0,L.D)(l.startDate))+1]})]})}),(0,a.jsx)(x.Z,{onClick:()=>y(l),size:"small",color:"default",sx:{"&:hover":{color:"error.main"}},children:(0,a.jsx)(_.Z,{})})]},l._id)),(0,a.jsxs)(c.Z,{open:u,onClose:()=>m(!1),maxWidth:"sm",fullWidth:!0,children:[(0,a.jsxs)(d.Z,{sx:{display:"flex",alignItems:"center",gap:1},children:[(0,a.jsx)(H.Z,{color:"error"}),"Delete Festival"]}),(0,a.jsx)(p.Z,{children:(0,a.jsxs)(W.Z,{children:["Are you sure you want to delete ",j?.name,"? This action cannot be undone."]})}),(0,a.jsxs)(C.Z,{children:[(0,a.jsx)(w.Z,{onClick:()=>m(!1),variant:"outlined",color:"inherit",children:"Cancel"}),(0,a.jsx)(w.Z,{onClick:g,variant:"contained",color:"error",disabled:r,children:"Delete"})]})]})]})};var M=s(89126);let q=e=>{let{onEditClick:t,onNotesClick:s,onSyncClick:l}=e;return(0,a.jsx)(M.Z,{elevation:1,sx:{bgcolor:"rgba(255, 255, 255, 0.9)",backdropFilter:"blur(8px)",p:{xs:2,sm:3},mb:3,borderRadius:1},children:(0,a.jsxs)(v.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,a.jsx)(v.Z,{sx:{flex:1},children:(0,a.jsx)(E,{})}),(0,a.jsxs)(P.Z,{direction:"row",spacing:1,children:[(0,a.jsx)(w.Z,{variant:"outlined",onClick:t,size:"medium",sx:{color:"text.primary",borderColor:"divider","&:hover":{bgcolor:"action.hover",borderColor:"divider"}},children:"Edit Festival"}),(0,a.jsx)(w.Z,{variant:"outlined",onClick:s,size:"medium",sx:{color:"text.primary",borderColor:"divider","&:hover":{bgcolor:"action.hover",borderColor:"divider"}},children:"Notes"}),(0,a.jsx)(w.Z,{variant:"contained",onClick:l,size:"medium",sx:{bgcolor:"ithink.pink","&:hover":{bgcolor:"ithink.pinkLight"}},children:"Sync Now"})]})]})})},O=e=>{let{formData:t,onSubmit:s,onChange:i}=e,n=e=>s=>{i({...t,[e]:"checkbox"===s.target.type?s.target.checked:s.target.value})},[r,c]=l.useState(!0),[d,x]=l.useState(!1),p=async e=>{e.preventDefault();let a=[];r&&a.push({id:`arena_${Date.now()}`,name:"Arena",type:"arena",description:"Main arena area"}),d&&a.push({id:`campsite_${Date.now()}`,name:"Campsite",type:"campsite",description:"Festival campsite"}),i({...t,hasMultipleLocations:a.length>0,locations:a}),await s(e)};return(0,a.jsx)(M.Z,{sx:{p:3},children:(0,a.jsx)("form",{onSubmit:p,children:(0,a.jsxs)(u.Z,{container:!0,spacing:3,children:[(0,a.jsx)(u.Z,{size:{xs:12},children:(0,a.jsx)(h.Z,{variant:"h6",gutterBottom:!0,children:"Create New Festival"})}),(0,a.jsx)(u.Z,{size:{xs:12,sm:6},children:(0,a.jsx)(m.Z,{fullWidth:!0,required:!0,label:"Festival Name",value:t.name,onChange:n("name")})}),(0,a.jsx)(u.Z,{size:{xs:12,sm:6},children:(0,a.jsxs)(j.Z,{fullWidth:!0,children:[(0,a.jsx)(f.Z,{children:"Event Type"}),(0,a.jsxs)(Z.Z,{value:t.type,label:"Event Type",onChange:e=>n("type")({target:{value:e.target.value}}),children:[(0,a.jsx)(b.Z,{value:"festival",children:"Festival"}),(0,a.jsx)(b.Z,{value:"regular_event",children:"Regular Event"})]})]})}),(0,a.jsx)(u.Z,{size:{xs:12,sm:6},children:(0,a.jsx)(m.Z,{fullWidth:!0,required:!0,type:"date",label:"Start Date",value:t.startDate,onChange:n("startDate"),InputLabelProps:{shrink:!0}})}),(0,a.jsx)(u.Z,{size:{xs:12,sm:6},children:(0,a.jsx)(m.Z,{fullWidth:!0,required:!0,type:"date",label:"End Date",value:t.endDate,onChange:n("endDate"),InputLabelProps:{shrink:!0}})}),(0,a.jsxs)(u.Z,{size:{xs:12},children:[(0,a.jsx)(h.Z,{variant:"subtitle1",gutterBottom:!0,children:"Festival Locations"}),(0,a.jsxs)(y.Z,{children:[(0,a.jsx)(o.Z,{control:(0,a.jsx)(g.Z,{checked:r,onChange:e=>c(e.target.checked)}),label:"Arena"}),(0,a.jsx)(o.Z,{control:(0,a.jsx)(g.Z,{checked:d,onChange:e=>x(e.target.checked)}),label:"Campsite"})]})]}),(0,a.jsx)(u.Z,{size:{xs:12},children:(0,a.jsx)(h.Z,{variant:"subtitle1",gutterBottom:!0,children:"External Links"})}),(0,a.jsx)(u.Z,{size:{xs:12,sm:6},children:(0,a.jsx)(m.Z,{fullWidth:!0,label:"Main Website URL",value:t.mainUrl,onChange:n("mainUrl")})}),(0,a.jsx)(u.Z,{size:{xs:12,sm:6},children:(0,a.jsx)(m.Z,{fullWidth:!0,label:"Map URL",value:t.mapUrl,onChange:n("mapUrl")})}),(0,a.jsx)(u.Z,{size:{xs:12,sm:6},children:(0,a.jsx)(m.Z,{fullWidth:!0,label:"Travel Info URL",value:t.travelInfoUrl,onChange:n("travelInfoUrl")})}),(0,a.jsx)(u.Z,{size:{xs:12,sm:6},children:(0,a.jsx)(m.Z,{fullWidth:!0,label:"FAQs URL",value:t.faqsUrl,onChange:n("faqsUrl")})}),(0,a.jsxs)(u.Z,{size:{xs:12},children:[(0,a.jsx)(h.Z,{variant:"subtitle1",gutterBottom:!0,children:"Enabled Features"}),(0,a.jsxs)(y.Z,{children:[(0,a.jsx)(o.Z,{control:(0,a.jsx)(g.Z,{checked:t.showAdmissions,onChange:n("showAdmissions")}),label:"Admissions"}),(0,a.jsx)(o.Z,{control:(0,a.jsx)(g.Z,{checked:t.showFrontOfHouse,onChange:n("showFrontOfHouse")}),label:"Front of House"}),(0,a.jsx)(o.Z,{control:(0,a.jsx)(g.Z,{checked:t.showLostProperty,onChange:n("showLostProperty")}),label:"Lost Property"}),(0,a.jsx)(o.Z,{control:(0,a.jsx)(g.Z,{checked:t.showShifts,onChange:n("showShifts")}),label:"Shifts"})]})]}),(0,a.jsx)(u.Z,{size:{xs:12},children:(0,a.jsx)(v.Z,{sx:{display:"flex",justifyContent:"flex-end"},children:(0,a.jsx)(w.Z,{type:"submit",variant:"contained",color:"primary",children:"Create Festival"})})})]})})})};var T=s(62403),B=s(9599),$=s(66236);let N=(e,t)=>{let s=Object.keys(e[0]||{}).filter(e=>!e.startsWith("_")),a=new Blob([[s.join(","),...e.map(e=>s.map(t=>JSON.stringify(e[t]||"")).join(","))].join("\n")],{type:"text/csv;charset=utf-8;"}),l=document.createElement("a");if(void 0!==l.download){let e=URL.createObjectURL(a);l.setAttribute("href",e),l.setAttribute("download",`${t}.csv`),l.style.visibility="hidden",document.body.appendChild(l),l.click(),document.body.removeChild(l)}},Q=async(e,t)=>{if(window.confirm(`Are you sure you want to clear all ${e} data for this festival? This action cannot be undone.`))try{let s;switch(e){case"admissions":for(let e of(await i.R.getAdmissionsByFestival(t)))await i.R.deleteAdmission(e._id);break;case"items":for(let e of(await i.R.getItemCountsByFestival(t)))await i.R.deleteItemCount(e._id);break;case"lostProperty":for(let e of(await i.R.getLostPropertyItems()))await i.R.deleteLostPropertyItem(e._id);break;case"shifts":for(let e of(await i.R.getShiftAssignments(t)))await i.R.deleteShiftAssignment(e._id);break;case"feedback":for(let e of(await i.R.getAllFeedback()))await i.R.deleteFeedback(e._id)}alert(`All ${e} data has been cleared successfully.`)}catch(t){alert(`Failed to clear ${e} data. Please try again.`)}},J=e=>{let{festivalId:t,type:s}=e,[n,r]=(0,l.useState)([]),[o,c]=(0,l.useState)(!0),[d,x]=(0,l.useState)(null);return((0,l.useEffect)(()=>{(async()=>{if(!t&&"feedback"!==s){r([]),c(!1);return}c(!0),x(null);try{let e;switch(s){case"admissions":e=await i.R.getAdmissionsByFestival(t);break;case"items":e=await i.R.getItemCountsByFestival(t);break;case"lostProperty":e=await i.R.getLostPropertyItems();break;case"shifts":e=await i.R.getShiftAssignments(t);break;case"feedback":e=await i.R.getAllFeedback();break;default:throw Error("Invalid type specified")}r(e)}catch(e){x("Failed to load data")}finally{c(!1)}})()},[t,s]),o)?(0,a.jsx)(v.Z,{sx:{display:"flex",justifyContent:"center",p:2},children:(0,a.jsx)(R.Z,{})}):d?(0,a.jsx)(A.Z,{severity:"error",children:d}):t||"feedback"===s?"feedback"===s?(0,a.jsx)($.L,{}):(0,a.jsxs)(P.Z,{spacing:2,children:[(0,a.jsxs)(h.Z,{variant:"h5",component:"h2",sx:{fontWeight:"bold",color:"ithink.purple"},children:[s.charAt(0).toUpperCase()+s.slice(1)," Management"]}),(0,a.jsxs)(P.Z,{direction:{xs:"column",sm:"row"},spacing:2,sx:{width:"100%"},children:[(0,a.jsxs)(w.Z,{variant:"outlined",size:"small",sx:{color:"ithink.purple",borderColor:"ithink.purple","&:hover":{backgroundColor:e=>(0,T.Fq)(e.palette.ithink.purple,.04),borderColor:"ithink.purple"},flex:1,minHeight:"64px",borderRadius:"12px",display:"flex",flexDirection:"column",gap:"4px",textAlign:"center",lineHeight:1.2,px:3,py:2},onClick:()=>N(n,`${s}-${t}`),children:[(0,a.jsx)(B.Z,{sx:{mb:.5}}),(0,a.jsxs)(v.Z,{component:"span",sx:{fontSize:"0.875rem"},children:["Download",(0,a.jsx)("br",{}),s," CSV"]})]}),(0,a.jsxs)(w.Z,{variant:"outlined",size:"small",sx:{color:"error.main",borderColor:"error.main","&:hover":{backgroundColor:e=>(0,T.Fq)(e.palette.error.main,.04),borderColor:"error.main"},flex:1,minHeight:"64px",borderRadius:"12px",display:"flex",flexDirection:"column",gap:"4px",textAlign:"center",lineHeight:1.2,px:3,py:2},onClick:()=>Q(s,t),children:[(0,a.jsx)(_.Z,{sx:{mb:.5}}),(0,a.jsxs)(v.Z,{component:"span",sx:{fontSize:"0.875rem"},children:["Clear All",(0,a.jsx)("br",{}),s," Data"]})]})]})]}):(0,a.jsx)(A.Z,{severity:"info",children:"Please select a festival first"})};var V=s(91849),G=s(84273);let K=e=>{let{festivalId:t}=e,[s,n]=(0,l.useState)(!1),[r,o]=(0,l.useState)(null),[x,u]=(0,l.useState)(null),[m,j]=(0,l.useState)(!1),[f,Z]=(0,l.useState)(null),b=async()=>{j(!1),n(!0),o(null),u(null);try{await i.R.resetDatabaseWithFreshSync(),u("Database cleared successfully. Fresh data will be synced.")}catch(e){o(e instanceof Error?e.message:"Failed to clear database. Please try again.")}finally{n(!1),Z(null)}},y=async()=>{j(!1),n(!0),o(null),u(null);try{let e=await i.R.performDatabaseCleanup();e.errors.length>0?o(`Cleanup completed with some errors: ${e.errors.join(", ")}`):u(`Database cleanup completed successfully. Cleaned ${e.totalCleaned} old records.`)}catch(e){o(e instanceof Error?e.message:"Failed to cleanup database. Please try again.")}finally{n(!1),Z(null)}},g=async()=>{n(!0),o(null),u(null);try{let e=await i.R.exportCompleteDatabase("json"),t=URL.createObjectURL(e),s=document.createElement("a");s.href=t,s.download=`database-export-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(t),u("Database exported successfully.")}catch(e){o(e instanceof Error?e.message:"Failed to export database. Please try again.")}finally{n(!1),Z(null)}},k=e=>{Z(e),j(!0),o(null),u(null)},D=(()=>{switch(f){case"clear":return{title:"Clear Local Database Cache",description:"This will clear all local data and resync from the server. This operation is useful for resolving sync issues on iPads that cannot clear their browser cache.\n\nWarning: All unsaved changes will be lost. Are you sure you want to continue?",action:"Clear Database"};case"cleanup":return{title:"Cleanup Old Database Records",description:"This will remove old records (older than 3 months) including discharged admissions, resolved feedback, and completed shifts. This helps improve database performance.\n\nAre you sure you want to continue?",action:"Cleanup Database"};default:return{title:"",description:"",action:""}}})();return(0,a.jsxs)(P.Z,{spacing:2,children:[(0,a.jsx)(h.Z,{variant:"h5",component:"h2",sx:{fontWeight:"bold",color:"ithink.purple"},children:"Database Operations"}),(0,a.jsx)(h.Z,{variant:"body2",color:"text.secondary",sx:{mb:2},children:"Database management tools for administrators"}),r&&(0,a.jsx)(A.Z,{severity:"error",sx:{mb:2},children:r}),x&&(0,a.jsx)(A.Z,{severity:"success",sx:{mb:2},children:x}),(0,a.jsxs)(P.Z,{direction:{xs:"column",sm:"row"},spacing:2,sx:{width:"100%"},children:[(0,a.jsxs)(w.Z,{variant:"outlined",size:"small",disabled:s,sx:{color:"warning.main",borderColor:"warning.main","&:hover":{backgroundColor:e=>(0,T.Fq)(e.palette.warning.main,.04),borderColor:"warning.main"},"&:disabled":{opacity:.6},flex:1,minHeight:"64px",borderRadius:"12px",display:"flex",flexDirection:"column",gap:"4px",textAlign:"center",lineHeight:1.2,px:3,py:2},onClick:()=>k("clear"),children:[s&&"clear"===f?(0,a.jsx)(R.Z,{size:24,sx:{mb:.5}}):(0,a.jsx)(V.Z,{sx:{mb:.5}}),(0,a.jsx)(v.Z,{component:"span",sx:{fontSize:"0.875rem"},children:s&&"clear"===f?"Clearing...":"Clear Local<br />Database Cache"})]}),(0,a.jsxs)(w.Z,{variant:"outlined",size:"small",disabled:s,sx:{color:"info.main",borderColor:"info.main","&:hover":{backgroundColor:e=>(0,T.Fq)(e.palette.info.main,.04),borderColor:"info.main"},"&:disabled":{opacity:.6},flex:1,minHeight:"64px",borderRadius:"12px",display:"flex",flexDirection:"column",gap:"4px",textAlign:"center",lineHeight:1.2,px:3,py:2},onClick:()=>k("cleanup"),children:[s&&"cleanup"===f?(0,a.jsx)(R.Z,{size:24,sx:{mb:.5}}):(0,a.jsx)(G.Z,{sx:{mb:.5}}),(0,a.jsx)(v.Z,{component:"span",sx:{fontSize:"0.875rem"},children:s&&"cleanup"===f?"Cleaning...":"Cleanup Old<br />Records"})]}),(0,a.jsxs)(w.Z,{variant:"outlined",size:"small",disabled:s,sx:{color:"ithink.purple",borderColor:"ithink.purple","&:hover":{backgroundColor:e=>(0,T.Fq)(e.palette.ithink.purple,.04),borderColor:"ithink.purple"},"&:disabled":{opacity:.6},flex:1,minHeight:"64px",borderRadius:"12px",display:"flex",flexDirection:"column",gap:"4px",textAlign:"center",lineHeight:1.2,px:3,py:2},onClick:()=>{Z("export"),g()},children:[s&&"export"===f?(0,a.jsx)(R.Z,{size:24,sx:{mb:.5}}):(0,a.jsx)(B.Z,{sx:{mb:.5}}),(0,a.jsx)(v.Z,{component:"span",sx:{fontSize:"0.875rem"},children:s&&"export"===f?"Exporting...":"Export Complete<br />Database"})]})]}),(0,a.jsxs)(c.Z,{open:m,onClose:()=>j(!1),"aria-labelledby":"confirm-dialog-title","aria-describedby":"confirm-dialog-description",children:[(0,a.jsx)(d.Z,{id:"confirm-dialog-title",children:D.title}),(0,a.jsx)(p.Z,{children:(0,a.jsx)(W.Z,{id:"confirm-dialog-description",sx:{whiteSpace:"pre-line"},children:D.description})}),(0,a.jsxs)(C.Z,{children:[(0,a.jsx)(w.Z,{onClick:()=>j(!1),color:"inherit",children:"Cancel"}),(0,a.jsx)(w.Z,{onClick:()=>{switch(f){case"clear":b();break;case"cleanup":y();break;default:j(!1),Z(null)}},color:"clear"===f?"warning":"primary",variant:"contained",autoFocus:!0,children:D.action})]})]})]})},X=e=>{let{festivalId:t}=e;return(0,a.jsxs)(v.Z,{sx:{mt:4},children:[(0,a.jsx)(h.Z,{variant:"h4",component:"h2",sx:{mb:3,fontWeight:"bold",color:"text.primary"},children:"Admin Panel"}),(0,a.jsxs)(u.Z,{container:!0,spacing:3,children:[(0,a.jsx)(u.Z,{size:{xs:12,md:6,lg:3},children:(0,a.jsx)(J,{festivalId:t,type:"admissions"})}),(0,a.jsx)(u.Z,{size:{xs:12,md:6,lg:3},children:(0,a.jsx)(J,{festivalId:t,type:"items"})}),(0,a.jsx)(u.Z,{size:{xs:12,md:6,lg:3},children:(0,a.jsx)(J,{festivalId:t,type:"lostProperty"})}),(0,a.jsx)(u.Z,{size:{xs:12,md:6,lg:3},children:(0,a.jsx)(J,{festivalId:t,type:"shifts"})}),(0,a.jsx)(u.Z,{size:{xs:12,md:6,lg:3},children:(0,a.jsx)(K,{festivalId:t})})]})]})};var Y=s(98106),ee=s(44064);let et=()=>{let{activeFestival:e,loading:t,error:s,manualSync:r}=(0,n.C)(),[o,c]=(0,l.useState)(null),[d,x]=(0,l.useState)(!1),[p,u]=(0,l.useState)({name:"",startDate:"",endDate:"",location:"",type:"festival",mainUrl:"",mapUrl:"",travelInfoUrl:"",faqsUrl:"",showAdmissions:!0,showFrontOfHouse:!0,showLostProperty:!0,showShifts:!1,hasMultipleLocations:!1,locations:[]}),[m,j]=(0,l.useState)(!1),[f,Z]=(0,l.useState)(!1);(0,l.useEffect)(()=>{let e=async()=>{try{let e=i.R.addSyncListener(()=>{x(!0)});return x(!0),()=>{e()}}catch(e){c(e),x(!0)}};d||e()},[d]);let b=async e=>{e.preventDefault();try{let e={...p,type:"festival",documentType:"festival",syncStatus:"sync_pending",isActive:!1,locations:p.locations||[],hasMultipleLocations:p.hasMultipleLocations||!1};await i.R.addFestival(e),u({name:"",startDate:"",endDate:"",location:"",type:"festival",mainUrl:"",mapUrl:"",travelInfoUrl:"",faqsUrl:"",showAdmissions:!0,showFrontOfHouse:!0,showLostProperty:!0,showShifts:!1,hasMultipleLocations:!1,locations:[]}),c(null)}catch(e){c(e)}};return!d||t?(0,a.jsxs)(v.Z,{sx:{display:"flex",flexDirection:"column",alignItems:"center",justifyContent:"center",minHeight:"200px",gap:2},children:[(0,a.jsx)(R.Z,{}),(0,a.jsx)(h.Z,{color:"text.secondary",children:"Initializing database..."})]}):(0,a.jsx)(Y.Z,{maxWidth:"lg",children:(0,a.jsxs)(P.Z,{spacing:3,children:[(0,a.jsx)(q,{onEditClick:()=>j(!0),onNotesClick:()=>Z(!0),onSyncClick:r}),(0,a.jsx)(O,{formData:p,onSubmit:b,onChange:u}),(o||s)&&(0,a.jsxs)(A.Z,{severity:"error",children:[(0,a.jsx)(ee.Z,{children:"Error"}),o?.message||s?.message]}),(0,a.jsx)(X,{festivalId:e?._id||null}),e&&m&&(0,a.jsx)(S,{festival:e,isOpen:!0,onClose:()=>j(!1),onSave:r}),f&&(0,a.jsx)(F.U,{onClose:()=>Z(!1)})]})})}}}]);