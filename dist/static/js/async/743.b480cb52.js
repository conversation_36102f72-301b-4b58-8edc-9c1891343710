"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["743"],{63709:function(e,t,r){r.d(t,{AC:()=>o,LU:()=>u,iG:()=>d,kT:()=>l,ol:()=>c});var a=r(67294),n=r(83502),i=r(5214);function s(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{activeFestival:r}=(0,i.C)(),{enabled:n=!0,onError:s}=t,[l,o]=(0,a.useState)({data:null,isLoading:!1,error:null,isFromCache:!1}),d=(0,a.useCallback)(async()=>{if(!n||!r)return void o(e=>({...e,data:null,isLoading:!1}));o(e=>({...e,isLoading:!0,error:null}));try{Date.now();let t=await e();Date.now(),o({data:t,isLoading:!1,error:null,isFromCache:!1})}catch(e){o({data:null,isLoading:!1,error:e instanceof Error?e.message:"Unknown error",isFromCache:!1}),s&&e instanceof Error&&s(e)}},[r?._id,n]),c=(0,a.useCallback)(async()=>{await d()},[d]);return(0,a.useEffect)(()=>{d()},[d]),{...l,refetch:c}}let l=function(e){let{activeFestival:t}=(0,i.C)();return s((0,a.useCallback)(()=>t?n.R.getAdmissionsByFestival(t._id):Promise.resolve([]),[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})},o=function(e){let{activeFestival:t}=(0,i.C)();return s((0,a.useCallback)(()=>t?n.R.getItemCountsByFestival(t._id):Promise.resolve([]),[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})},d=function(e){let{activeFestival:t}=(0,i.C)();return s((0,a.useCallback)(async()=>t?n.R.getLostPropertyItems(t._id):[],[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})},c=function(e){let{activeFestival:t}=(0,i.C)();return s((0,a.useCallback)(()=>t?n.R.getKnowledgeBaseItems(t._id):Promise.resolve([]),[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})},u=function(e){let{activeFestival:t}=(0,i.C)();return s((0,a.useCallback)(()=>t?n.R.getKnowledgeBaseCategories(t._id):Promise.resolve([]),[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})}},67347:function(e,t,r){r.r(t),r.d(t,{DashboardPage:()=>j});var a=r(85893),n=r(67294),i=r(33991),s=r(54757),l=r(5214),o=r(63709),d=r(89126),c=r(62983),u=r(11161),x=r(61215),h=r(39467),g=r(17047),m=r(7230),b=r(12550),y=r(49696),f=r(10857);let v=["Suncream","Poncho","Water","SanitaryProducts","Earplugs","Condoms","ChildrensWristbands","GeneralWristbands","Charging","Sanitizer","ToiletRoll","GeneralEnqs"],p=()=>{let{activeFestival:e}=(0,l.C)(),[t,r]=(0,n.useState)(null),[p,j]=(0,n.useState)([]),Z=(0,o.kT)({onError:e=>void 0}),C=(0,o.AC)({onError:e=>void 0}),w=(0,o.iG)({onError:e=>void 0});(0,n.useEffect)(()=>{let e=e=>{Promise.all([Z.refetch(),C.refetch(),w.refetch()]).catch(e=>void 0)};return window.addEventListener("initialSyncComplete",e),()=>{window.removeEventListener("initialSyncComplete",e)}},[Z.refetch,C.refetch,w.refetch]);let S=Z.data||[],D=C.data||[],A=w.data||[],L=Z.isLoading||C.isLoading||w.isLoading,k=Z.error||C.error||w.error,I=Z.isFromCache||C.isFromCache||w.isFromCache,B=async()=>{await Promise.all([Z.refetch(),C.refetch(),w.refetch()])},_=e=>{try{let t=new Date(e),r=new Date,a=r.getFullYear()-t.getFullYear(),n=r.getMonth()-t.getMonth();return(n<0||0===n&&r.getDate()<t.getDate())&&a--,a}catch{return null}},P=e=>{if("number"==typeof e.Age)return e.Age<18;if(e.DOB){let t=_(e.DOB);return null!==t&&t<18}return!1},F=e=>Array.isArray(e.SubstanceUsed)&&e.SubstanceUsed.length>0&&!e.SubstanceUsed.includes("Nothing"),E=e=>"string"==typeof e.Safeguarding&&""!==e.Safeguarding.trim(),U=e=>e.filter(e=>{let t=P(e),r=F(e);return t&&r&&e.InBayNow}),N=e=>e.filter(e=>{let t=P(e),r=E(e);return t&&r&&e.InBayNow}),R=e=>{let t=new Set;return e.forEach(e=>{Array.isArray(e.SubstanceUsed)&&e.SubstanceUsed.forEach(e=>{"Nothing"!==e&&t.add(e)})}),Array.from(t).join(", ")},O=e=>e.reduce((e,t)=>e+v.reduce((e,r)=>e+("number"==typeof t[r]?t[r]:0),0),0),W=()=>{if(!e)return[];let t=S.filter(e=>e.InBayNow).length,r=S.length,a=U(S),n=N(S),i=O(D),s=A.filter(e=>"unclaimed"===e.status).length;return[{id:"current-in-bay",title:"Currently In Bay",value:t,data:S.filter(e=>e.InBayNow)},{id:"total-admissions",title:"Total Admissions",value:r,data:S},{id:"under-18-substance",title:"U18s with Substance Issues",value:a.length,data:a,alert:a.length>0,subtitle:a.length>0?`Substances: ${R(a)}`:void 0},{id:"under-18-safeguarding",title:"U18s with Safeguarding",value:n.length,data:n,alert:n.length>0},{id:"total-items",title:"Total Items Handed Out",value:i,data:D},{id:"lost-property",title:"Current Lost Property Items",value:s,data:A.filter(e=>"unclaimed"===e.status)}]},T=(0,n.useCallback)(()=>W(),[S.length,D.length,A.length,e?._id]);(0,n.useEffect)(()=>{j(T())},[T]);let z=async e=>{r(t===e?null:e)},G=e=>(0,a.jsxs)(d.Z,{elevation:1,sx:{mt:2,p:{xs:2,sm:3},bgcolor:"background.paper"},children:[(0,a.jsxs)(s.Z,{variant:"h6",sx:{mb:{xs:2,sm:3},color:"text.primary",fontWeight:500},children:[e.title," Details"]}),("current-in-bay"===e.id||"total-admissions"===e.id)&&(0,a.jsx)(c.Z,{sx:{py:0},children:e.data.map(e=>(0,a.jsxs)(u.ZP,{sx:{flexDirection:"column",alignItems:"flex-start",borderBottom:1,borderColor:"divider",py:{xs:1.5,sm:2}},children:[(0,a.jsxs)(s.Z,{variant:"subtitle2",color:"text.primary",children:[e.FirstName," ",e.Surname]}),(0,a.jsxs)(s.Z,{variant:"body2",color:"text.secondary",children:[e.BaysOrChairs," ",e.Location?`#${e.Location}`:""]}),(0,a.jsxs)(s.Z,{variant:"body2",color:"text.secondary",children:["Admitted: ",e.Attended?new Date(e.Attended).toLocaleTimeString():"N/A"]})]},e._id))}),("under-18-substance"===e.id||"under-18-safeguarding"===e.id)&&(0,a.jsx)(c.Z,{sx:{py:0},children:e.data.map(t=>(0,a.jsxs)(u.ZP,{sx:{flexDirection:"column",alignItems:"flex-start",borderBottom:1,borderColor:"divider",py:{xs:1.5,sm:2}},children:[(0,a.jsxs)(s.Z,{variant:"subtitle2",color:"text.primary",children:[t.FirstName," ",t.Surname]}),(0,a.jsxs)(s.Z,{variant:"body2",color:"text.secondary",children:["Age: ",t.Age||_(t.DOB)]}),"under-18-substance"===e.id&&t.SubstanceUsed&&(0,a.jsxs)(s.Z,{variant:"body2",color:"text.secondary",children:["Substances: ",t.SubstanceUsed.join(", ")]}),"under-18-safeguarding"===e.id&&t.Safeguarding&&(0,a.jsxs)(s.Z,{variant:"body2",color:"text.secondary",children:["Safeguarding: ",t.Safeguarding]}),(0,a.jsxs)(s.Z,{variant:"body2",color:"text.secondary",children:["Admitted: ",t.Attended?new Date(t.Attended).toLocaleString():"N/A"]})]},t._id))}),"total-items"===e.id&&(0,a.jsx)(c.Z,{sx:{py:0},children:v.map(t=>{let r=e.data.reduce((e,r)=>e+("number"==typeof r[t]?r[t]:0),0);return r>0?(0,a.jsx)(u.ZP,{sx:{borderBottom:1,borderColor:"divider",py:{xs:1.5,sm:2}},children:(0,a.jsxs)(s.Z,{variant:"subtitle2",color:"text.primary",children:[t,": ",r]})},t):null})}),"lost-property"===e.id&&(0,a.jsx)(c.Z,{sx:{py:0},children:e.data.map(e=>(0,a.jsxs)(u.ZP,{sx:{flexDirection:"column",alignItems:"flex-start",borderBottom:1,borderColor:"divider",py:{xs:1.5,sm:2}},children:[(0,a.jsxs)(s.Z,{variant:"subtitle2",color:"text.primary",children:[e.category," - ",e.quickDescription]}),(0,a.jsxs)(s.Z,{variant:"body2",color:"text.secondary",children:["Found: ",new Date(e.timeFound).toLocaleString()]}),(0,a.jsxs)(s.Z,{variant:"body2",color:"text.secondary",children:["Location: ",e.whereFound]}),e.description&&(0,a.jsxs)(s.Z,{variant:"body2",color:"text.secondary",children:["Details: ",e.description]})]},e._id))})]});return L?(0,a.jsx)(i.Z,{sx:{display:"flex",justifyContent:"center",p:3},children:(0,a.jsx)(x.Z,{})}):k?(0,a.jsx)(i.Z,{sx:{p:3},children:(0,a.jsx)(h.Z,{severity:"error",children:k})}):e?(0,a.jsxs)(i.Z,{sx:{p:{xs:2,sm:3}},children:[(0,a.jsxs)(i.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3},children:[(0,a.jsxs)(i.Z,{children:[(0,a.jsxs)(s.Z,{variant:"h4",sx:{mb:1},children:["Dashboard - ",e.name]}),(0,a.jsxs)(i.Z,{sx:{display:"flex",alignItems:"center",gap:1},children:[I&&(0,a.jsx)(g.Z,{icon:(0,a.jsx)(f.Z,{}),label:"Cached Data",size:"small",color:"info",variant:"outlined"}),(0,a.jsxs)(s.Z,{variant:"body2",color:"text.secondary",children:[new Date(e.startDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]})]})]}),(0,a.jsx)(m.Z,{variant:"outlined",onClick:B,disabled:L,startIcon:(0,a.jsx)(f.Z,{}),children:"Refresh"})]}),(0,a.jsx)(b.Z,{container:!0,spacing:{xs:2,sm:3},children:p.map(e=>(0,a.jsx)(b.Z,{size:{xs:12,sm:6,lg:4},children:(0,a.jsxs)(i.Z,{sx:{position:"relative"},children:[(0,a.jsx)(m.Z,{onClick:()=>z(e.id),sx:{width:"100%",textAlign:"left",p:{xs:2,sm:3},bgcolor:"background.paper",borderRadius:1,boxShadow:1,"&:hover":{bgcolor:"action.hover"}},children:(0,a.jsxs)(i.Z,{sx:{width:"100%"},children:[(0,a.jsxs)(i.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,a.jsx)(s.Z,{variant:"body2",sx:{fontWeight:500,color:"text.secondary",overflow:"hidden",textOverflow:"ellipsis"},children:e.title}),e.alert&&(0,a.jsx)(y.Z,{sx:{color:"error.main",fontSize:20}})]}),(0,a.jsx)(s.Z,{variant:"h4",sx:{mt:1,fontWeight:600,color:e.alert?"error.main":"text.primary"},children:e.value}),e.subtitle&&(0,a.jsx)(s.Z,{variant:"body2",sx:{mt:1,color:"text.secondary",overflow:"hidden",textOverflow:"ellipsis"},children:e.subtitle})]})}),t===e.id&&G(e)]})},e.id))})]}):(0,a.jsx)(i.Z,{sx:{p:4,textAlign:"center"},children:(0,a.jsx)(s.Z,{color:"text.secondary",children:"Please select a festival to view dashboard data."})})},j=()=>(0,a.jsxs)(i.Z,{sx:{py:3},children:[(0,a.jsx)(s.Z,{variant:"h4",sx:{fontWeight:"bold",mb:3},children:"Dashboard"}),(0,a.jsx)(p,{})]})}}]);