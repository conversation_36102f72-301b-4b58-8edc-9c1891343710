"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["616"],{66156:function(e,a,s){s.d(a,{s:()=>r});var i=s(67294),n=s(83502),t=s(5214),l=s(59326);let r=()=>{let[e,a]=(0,i.useState)([]),[s,r]=(0,i.useState)(!0),[o,d]=(0,i.useState)(null),{activeFestival:h}=(0,t.C)(),{activeSiteLocation:c}=(0,l.C)(),u=(0,i.useCallback)(async()=>{r(!0);try{let e=h?await n.R.getAdmissionsByFestival(h._id):await n.R.getAdmissionsByFestival("all"),s=c?e.filter(e=>e.siteLocationId===c.id):e;a(s),d(null)}catch(e){d(e)}finally{r(!1)}},[h,c]);(0,i.useEffect)(()=>{u()},[u]);let x=(0,i.useCallback)(async e=>{try{await n.R.addAdmission(e),await u()}catch(e){throw e}},[u]);return{admissions:e,loading:s,error:o,addAdmission:x,updateAdmission:(0,i.useCallback)(async e=>{try{await n.R.updateAdmission(e),await u()}catch(e){throw e}},[u]),deleteAdmission:(0,i.useCallback)(async e=>{try{await n.R.deleteAdmission(e),await u()}catch(e){throw e}},[u]),refreshAdmissions:u}}},66713:function(e,a,s){s.r(a),s.d(a,{default:()=>q,NewAdmissionPage:()=>U});var i=s(85893),n=s(67294),t=s(96872),l=s(33991),r=s(61215),o=s(54757),d=s(89126),h=s(12550),c=s(7230),u=s(56099),x=s(73892),m=s(1156),g=s(48346),j=s(73876),Z=s(21143),f=s(73716),y=s(96847);let C=(0,y.ZP)(l.Z)(e=>{let{theme:a}=e;return{marginTop:a.spacing(3),marginBottom:a.spacing(3)}}),b=(0,y.ZP)(o.Z)(e=>{let{theme:a}=e;return{marginBottom:a.spacing(2),color:a.palette.primary.main,fontWeight:500}});var p=s(59326);let v=e=>{let{location:a,baysOrChairs:s,inBayNow:n,bayStatus:t,onLocationChange:r,onBaysOrChairsChange:d,onInBayNowChange:c}=e,{activeSiteLocation:y}=(0,p.C)();return(0,i.jsxs)(C,{children:[(0,i.jsx)(b,{variant:"h6",children:"Location Details"}),(0,i.jsxs)(h.Z,{container:!0,spacing:3,children:[y&&(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsxs)(o.Z,{variant:"body1",color:"textSecondary",children:["Site Location: ","arena"===y.type?"Arena":"Campsite"]})}),(0,i.jsxs)(h.Z,{size:{xs:12,sm:4},children:[(0,i.jsx)(u.Z,{fullWidth:!0,type:"number",label:"Bay/Chair Number",value:a||"",onChange:e=>r(e.target.value?Number(e.target.value):void 0)}),"Bay"===s&&a&&t&&(0,i.jsx)(o.Z,{sx:{mt:1,color:t.isOccupied?"error.main":"success.main",fontSize:"0.875rem"},children:t.message})]}),(0,i.jsx)(h.Z,{size:{xs:12,sm:4},children:(0,i.jsxs)(x.Z,{fullWidth:!0,children:[(0,i.jsx)(m.Z,{children:"Type"}),(0,i.jsxs)(g.Z,{value:s,label:"Type",onChange:e=>d(e.target.value),children:[(0,i.jsx)(j.Z,{value:"Bay",children:"Bay"}),(0,i.jsx)(j.Z,{value:"Chair",children:"Chair"}),(0,i.jsx)(j.Z,{value:"Outside",children:"Outside"})]})]})}),(0,i.jsx)(h.Z,{size:{xs:12,sm:4},children:(0,i.jsx)(l.Z,{sx:{mt:2},children:(0,i.jsx)(Z.Z,{control:(0,i.jsx)(f.Z,{checked:n,onChange:e=>c(e.target.checked)}),label:`Currently in ${s}`})})})]})]})};var S=s(39467),w=s(74542);let N=e=>{let{firstName:a,surname:s,dob:t,age:l,gender:r,pronoun:d,ethnicity:c,contactName:Z,contactNumber:f,onInputChange:y,onSelectChange:p,onAgeChange:v}=e;return(0,n.useEffect)(()=>{if(t){let e=new Date(t),a=new Date,s=a.getFullYear()-e.getFullYear(),i=a.getMonth()-e.getMonth();(i<0||0===i&&a.getDate()<e.getDate())&&s--,v(s)}},[t,v]),(0,i.jsxs)(C,{children:[(0,i.jsx)(b,{variant:"h6",children:"Personal Information"}),(0,i.jsxs)(h.Z,{container:!0,spacing:3,children:[(0,i.jsx)(h.Z,{size:{xs:12,sm:6,md:3},children:(0,i.jsx)(u.Z,{fullWidth:!0,required:!0,label:"First Name",name:"FirstName",value:a,onChange:y})}),(0,i.jsx)(h.Z,{size:{xs:12,sm:6,md:3},children:(0,i.jsx)(u.Z,{fullWidth:!0,label:"Surname",name:"Surname",value:s,onChange:y})}),(0,i.jsx)(h.Z,{size:{xs:12,sm:6,md:3},children:(0,i.jsx)(u.Z,{fullWidth:!0,type:"date",label:"Date of Birth",name:"DOB",value:t,onChange:y,InputLabelProps:{shrink:!0}})}),(0,i.jsxs)(h.Z,{size:{xs:12,sm:6,md:3},children:[(0,i.jsx)(u.Z,{fullWidth:!0,type:"number",label:"Age",name:"Age",value:l||"",onChange:e=>{v(e.target.value?Number(e.target.value):void 0)}}),void 0!==l&&l<18&&(0,i.jsx)(S.Z,{severity:"warning",sx:{mt:1},children:"Person is under 18 years old"})]}),(0,i.jsx)(h.Z,{size:{xs:12,sm:6,md:4},children:(0,i.jsxs)(x.Z,{fullWidth:!0,required:!0,children:[(0,i.jsx)(m.Z,{id:"gender-label",children:"Gender"}),(0,i.jsxs)(g.Z,{labelId:"gender-label",label:"Gender",name:"Gender",value:r,onChange:p,children:[(0,i.jsx)(j.Z,{value:"Male",children:"Male"}),(0,i.jsx)(j.Z,{value:"Female",children:"Female"}),(0,i.jsx)(j.Z,{value:"Other",children:"Other"}),(0,i.jsx)(j.Z,{value:"Prefer not to say",children:"Prefer not to say"})]})]})}),(0,i.jsx)(h.Z,{size:{xs:12,sm:6,md:4},children:(0,i.jsx)(u.Z,{fullWidth:!0,label:"Pronoun",name:"Pronoun",value:d,onChange:y,placeholder:"e.g., he/him, she/her, they/them"})}),(0,i.jsx)(h.Z,{size:{xs:12,sm:6,md:4},children:(0,i.jsxs)(x.Z,{fullWidth:!0,required:!0,children:[(0,i.jsx)(m.Z,{id:"ethnicity-label",children:"Ethnicity"}),(0,i.jsxs)(g.Z,{labelId:"ethnicity-label",label:"Ethnicity",name:"Ethnicity",value:c,onChange:p,children:[(0,i.jsx)(j.Z,{value:"White",children:"White"}),(0,i.jsx)(j.Z,{value:"Mixed",children:"Mixed"}),(0,i.jsx)(j.Z,{value:"Asian",children:"Asian"}),(0,i.jsx)(j.Z,{value:"Black",children:"Black"}),(0,i.jsx)(j.Z,{value:"Other",children:"Other"}),(0,i.jsx)(j.Z,{value:"Prefer not to say",children:"Prefer not to say"})]})]})}),(0,i.jsxs)(h.Z,{size:{xs:12},children:[(0,i.jsx)(w.Z,{sx:{my:2}}),(0,i.jsx)(o.Z,{variant:"subtitle1",sx:{mb:2},children:"Contact Information"}),(0,i.jsxs)(h.Z,{container:!0,spacing:3,children:[(0,i.jsx)(h.Z,{size:{xs:12,sm:6},children:(0,i.jsx)(u.Z,{fullWidth:!0,label:"Contact Name",name:"ContactName",value:Z,onChange:y,placeholder:"Name of friend/family member"})}),(0,i.jsx)(h.Z,{size:{xs:12,sm:6},children:(0,i.jsx)(u.Z,{fullWidth:!0,label:"Contact Number",name:"ContactNumber",value:f,onChange:y,placeholder:"e.g., 07700 900000"})})]})]})]})]})},A=e=>{let{hairColour:a,hairStyle:s,clothingTop:n,clothingBottom:t,footwear:l,otherFeatures:r,onInputChange:o}=e;return(0,i.jsxs)(C,{children:[(0,i.jsx)(b,{variant:"h6",children:"Physical Description"}),(0,i.jsxs)(h.Z,{container:!0,spacing:3,children:[(0,i.jsx)(h.Z,{size:{xs:12,sm:6,md:4},children:(0,i.jsx)(u.Z,{fullWidth:!0,label:"Hair Colour",name:"HairColour",value:a,onChange:o})}),(0,i.jsx)(h.Z,{size:{xs:12,sm:6,md:4},children:(0,i.jsx)(u.Z,{fullWidth:!0,label:"Hair Style",name:"HairStyle",value:s,onChange:o})}),(0,i.jsx)(h.Z,{size:{xs:12,sm:6,md:4},children:(0,i.jsx)(u.Z,{fullWidth:!0,label:"Clothing Top",name:"ClothingTop",value:n,onChange:o})}),(0,i.jsx)(h.Z,{size:{xs:12,sm:6,md:4},children:(0,i.jsx)(u.Z,{fullWidth:!0,label:"Clothing Bottom",name:"ClothingBottom",value:t,onChange:o})}),(0,i.jsx)(h.Z,{size:{xs:12,sm:6,md:4},children:(0,i.jsx)(u.Z,{fullWidth:!0,label:"Footwear",name:"Footwear",value:l,onChange:o})}),(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsx)(u.Z,{fullWidth:!0,multiline:!0,rows:2,label:"Other Features",name:"OtherFeatures",value:r,onChange:o})})]})]})};var z=s(3814),O=s(89276),B=s(19410);let I=["Alcohol","Cannabis","Ecstasy/MDMA","Ketamine","Cocaine","LSD","Mushrooms","GHB","Opiates","Nothing","Other (Please Specify)"],W=e=>{let{substancesUsed:a,onSelectChange:s}=e;return(0,i.jsxs)(C,{children:[(0,i.jsx)(b,{variant:"h6",children:"Substance Use"}),(0,i.jsx)(h.Z,{container:!0,spacing:3,children:(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsxs)(x.Z,{fullWidth:!0,children:[(0,i.jsx)(m.Z,{children:"Substances Used"}),(0,i.jsx)(g.Z,{multiple:!0,name:"SubstanceUsed",value:a,onChange:s,input:(0,i.jsx)(z.Z,{label:"Substances Used"}),renderValue:e=>e.join(", "),children:I.map(e=>(0,i.jsxs)(j.Z,{value:e,children:[(0,i.jsx)(O.Z,{checked:a.indexOf(e)>-1}),(0,i.jsx)(B.Z,{primary:e})]},e))})]})})})]})},R=["Sexual Assault","Physical Assault","Emotional Abuse","Other"],D=e=>{let{category:a,notes:s,reasonCategory:n,onSelectChange:t,onInputChange:l}=e;return"Safeguarding"!==n?null:(0,i.jsxs)(C,{children:[(0,i.jsx)(b,{variant:"h6",children:"Safeguarding Information"}),(0,i.jsxs)(h.Z,{container:!0,spacing:3,children:[(0,i.jsx)(h.Z,{size:{xs:12,md:6},children:(0,i.jsxs)(x.Z,{fullWidth:!0,sx:{"& .MuiOutlinedInput-root":{border:"2px solid #f50057",borderRadius:1}},children:[(0,i.jsx)(m.Z,{sx:{color:"#f50057",fontWeight:"bold"},children:"Safeguarding Category (Required)"}),(0,i.jsx)(g.Z,{name:"Safeguarding",value:a||"",onChange:t,label:"Safeguarding Category (Required)",required:!0,children:R.map(e=>(0,i.jsx)(j.Z,{value:e,children:e},e))})]})}),(0,i.jsx)(h.Z,{size:{xs:12,md:6},children:(0,i.jsx)(u.Z,{fullWidth:!0,multiline:!0,rows:4,label:"Safeguarding Notes (Required)",name:"SafeguardingNotes",value:s||"",onChange:l,required:!0,sx:{"& .MuiOutlinedInput-root":{border:"2px solid #f50057",borderRadius:1},"& .MuiInputLabel-root":{color:"#f50057",fontWeight:"bold"}}})})]})]})},k=["Self Referral","Medical","Security","Police","Friend","Staff","Other"],P=e=>{let{referredBy:a,reasonCategory:s,onSelectChange:n}=e;return(0,i.jsxs)(C,{children:[(0,i.jsx)(b,{variant:"h6",children:"Referral Information"}),(0,i.jsxs)(h.Z,{container:!0,spacing:3,children:[(0,i.jsx)(h.Z,{size:{xs:12,md:6},children:(0,i.jsxs)(x.Z,{fullWidth:!0,children:[(0,i.jsx)(m.Z,{children:"Referred By"}),(0,i.jsx)(g.Z,{name:"ReferredBy",value:a,onChange:n,label:"Referred By",children:k.map(e=>(0,i.jsx)(j.Z,{value:e,children:e},e))})]})}),(0,i.jsx)(h.Z,{size:{xs:12,md:6},children:(0,i.jsxs)(x.Z,{fullWidth:!0,children:[(0,i.jsx)(m.Z,{children:"Reason Category"}),(0,i.jsxs)(g.Z,{name:"ReasonCategory",value:s,onChange:n,label:"Reason Category",children:[(0,i.jsx)(j.Z,{value:"Substance Use",children:"Substance Use"}),(0,i.jsx)(j.Z,{value:"Mental Health",children:"Mental Health"}),(0,i.jsx)(j.Z,{value:"Physical Health",children:"Physical Health"}),(0,i.jsx)(j.Z,{value:"Safeguarding",children:"Safeguarding"}),(0,i.jsx)(j.Z,{value:"Other",children:"Other"})]})]})})]})]})},F=e=>{let{isEdit:a,admissionNotes:s,additionalNotes:n,onInputChange:t,onNewNoteChange:r,newNoteValue:d=""}=e,c=n.map(e=>`${new Date(e.timestamp).toLocaleString()} - ${e.author}: ${e.note}`).join("\n");return(0,i.jsxs)(C,{children:[(0,i.jsx)(b,{variant:"h6",children:"Admission Details"}),(0,i.jsxs)(h.Z,{container:!0,spacing:3,children:[!a&&(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsx)(u.Z,{fullWidth:!0,multiline:!0,rows:4,label:"Admission Notes",name:"AdmissionNotes",value:s,onChange:t})}),a&&(0,i.jsxs)(i.Fragment,{children:[s&&(0,i.jsxs)(h.Z,{size:{xs:12},children:[(0,i.jsx)(o.Z,{variant:"subtitle2",sx:{mb:1},children:"Original Admission Notes:"}),(0,i.jsx)(l.Z,{sx:{p:2,mb:2,bgcolor:"background.paper",border:"1px solid #ddd",borderRadius:1,maxHeight:"200px",overflowY:"auto",whiteSpace:"pre-line"},children:s})]}),n.length>0&&(0,i.jsxs)(h.Z,{size:{xs:12},children:[(0,i.jsx)(o.Z,{variant:"subtitle2",sx:{mb:1},children:"Additional Notes:"}),(0,i.jsx)(l.Z,{sx:{p:2,mb:2,bgcolor:"background.paper",border:"1px solid #ddd",borderRadius:1,maxHeight:"200px",overflowY:"auto",whiteSpace:"pre-line"},children:c})]}),(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsx)(u.Z,{fullWidth:!0,multiline:!0,rows:4,label:"Add New Note",name:"newNote",value:d,onChange:r||t,placeholder:"Enter new note here...",inputProps:{autoComplete:"off"}})})]})]})]})};var H=s(83502),E=s(5214);let M=e=>{let{initialData:a,onSubmit:s}=e,[i,t]=(0,n.useState)(a),[l,r]=(0,n.useState)(!1),[o,d]=(0,n.useState)(null),{activeFestival:h}=(0,E.C)(),c=(0,n.useCallback)(e=>{let{name:a,value:s}=e.target;t(e=>({...e,[a]:s}))},[]),u=(0,n.useCallback)(e=>{let{name:a,value:s}=e.target;t(e=>({...e,[a]:s}))},[]),x=(0,n.useCallback)(async()=>{if(!h)throw Error("No active festival selected");r(!0);try{let e=new Date().toISOString(),a={...i,festivalId:h._id,timestamp:e,updatedAt:e};s?await s(a):i._id?await H.R.updateAdmission(a):(a.createdAt=e,await H.R.addAdmission(a))}catch(e){throw e}finally{r(!1)}},[i,h,s]),m=(0,n.useCallback)(e=>{t(e)},[]);return{formData:i,setFormData:t,isSubmitting:l,bayStatus:o,handleInputChange:c,handleSelectChange:u,handleSubmit:x,resetForm:m}},L=()=>({type:"admission",documentType:"admission",status:"active",FirstName:"",Surname:"",Gender:"Prefer not to say",Attended:new Date().toISOString(),BaysOrChairs:"Bay",InBayNow:!0,ReferredBy:"",ReasonCategory:"",SubstanceUsed:[],AdmissionNotes:"",AdditionalNotes:[],History:[],DOB:"",Pronoun:"",Ethnicity:"Prefer not to say",ContactName:"",ContactNumber:"",DischargeTime:"",HairColour:"",HairStyle:"",ClothingTop:"",ClothingBottom:"",Footwear:"",OtherFeatures:"",syncStatus:"local_only",festivalId:"",Location:void 0,siteLocationId:void 0,siteLocationName:void 0,siteLocationType:void 0,SafeguardingNotes:"",timestamp:new Date().toISOString(),createdAt:new Date().toISOString()}),T=e=>{let{onSubmit:a,activeFestival:s,initialData:t}=e,{activeSiteLocation:r}=(0,p.C)(),[o,u]=(0,n.useState)(""),{formData:x,setFormData:m,bayStatus:g,handleSubmit:j,isSubmitting:Z,handleInputChange:f,handleSelectChange:y}=M({initialData:t||L(),onSubmit:async e=>{let i={...e,type:"admission",documentType:"admission",syncStatus:"sync_pending",festivalId:s?._id||"",timestamp:new Date().toISOString(),createdAt:e.createdAt||new Date().toISOString()};if(o.trim()){let e={timestamp:new Date().toISOString(),note:o.trim(),author:"Staff"};i.AdditionalNotes=[...i.AdditionalNotes||[],e],u("")}await a(i)}}),C=(0,n.useCallback)(e=>{m(a=>({...a,Location:e}))},[m]),b=(0,n.useCallback)(e=>{m(a=>({...a,BaysOrChairs:e}))},[m]),S=(0,n.useCallback)(e=>{m(a=>({...a,InBayNow:e}))},[m]),w=(0,n.useCallback)(e=>{m(a=>({...a,Age:e}))},[m]),z=["Sexual Assault","Physical Assault","Emotional Abuse","Other"].includes(x.Safeguarding)?x.Safeguarding:void 0;return(0,i.jsx)("form",{onSubmit:e=>{e.preventDefault(),j()},children:(0,i.jsx)(d.Z,{sx:{p:3},children:(0,i.jsxs)(h.Z,{container:!0,spacing:3,children:[(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsx)(v,{location:x.Location,baysOrChairs:x.BaysOrChairs,inBayNow:x.InBayNow,bayStatus:g,onLocationChange:C,onBaysOrChairsChange:b,onInBayNowChange:S})}),(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsx)(F,{isEdit:!!x._id,admissionNotes:x.AdmissionNotes,additionalNotes:x.AdditionalNotes,onInputChange:f,onNewNoteChange:e=>{u(e.target.value)},newNoteValue:o})}),(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsx)(N,{firstName:x.FirstName,surname:x.Surname,dob:x.DOB,age:x.Age,gender:x.Gender,pronoun:x.Pronoun,ethnicity:x.Ethnicity,contactName:x.ContactName,contactNumber:x.ContactNumber,onInputChange:f,onSelectChange:y,onAgeChange:w})}),(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsx)(W,{substancesUsed:x.SubstanceUsed,onSelectChange:y})}),(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsx)(D,{category:z,notes:x.SafeguardingNotes||"",reasonCategory:x.ReasonCategory,onSelectChange:y,onInputChange:f})}),(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsx)(P,{referredBy:x.ReferredBy,reasonCategory:x.ReasonCategory,onSelectChange:y})}),(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsx)(A,{hairColour:x.HairColour,hairStyle:x.HairStyle,clothingTop:x.ClothingTop,clothingBottom:x.ClothingBottom,footwear:x.Footwear,otherFeatures:x.OtherFeatures,onInputChange:f})}),(0,i.jsx)(h.Z,{size:{xs:12},children:(0,i.jsx)(l.Z,{sx:{display:"flex",justifyContent:"flex-end",mt:2},children:(0,i.jsx)(c.Z,{variant:"contained",color:"primary",type:"submit",disabled:Z,children:Z?"Saving...":"Save Admission"})})})]})})})};var _=s(66156);let U=()=>{let{id:e}=(0,t.UO)(),a=(0,t.s0)(),{addAdmission:s,updateAdmission:d,admissions:h,refreshAdmissions:c}=(0,_.s)(),{activeFestival:u}=(0,E.C)(),[x,m]=(0,n.useState)(!1),[g,j]=(0,n.useState)(null);(0,n.useEffect)(()=>{(async()=>{if(e){m(!0);try{let a=h.find(a=>a._id===e)||null;if(a||(await c(),a=h.find(a=>a._id===e)||null),!a)try{a=await H.R.getAdmissionById(e)}catch(e){}a?j(a):j(L())}catch(e){j(L())}finally{m(!1)}}else j(L())})()},[e,h,c]);let Z=async i=>{try{if(e){let a=h.find(a=>a._id===e);a&&await d({...a,...i})}else await s(i);a("/admissions")}catch(e){}};return x||!g?(0,i.jsx)(l.Z,{sx:{p:2,display:"flex",justifyContent:"center",alignItems:"center",height:"50vh"},children:(0,i.jsx)(r.Z,{})}):(0,i.jsxs)(l.Z,{sx:{p:2},children:[(0,i.jsx)(o.Z,{variant:"h4",sx:{fontWeight:"bold",mb:2},children:e?"Edit Admission":"New Admission"}),(0,i.jsx)(T,{onSubmit:Z,activeFestival:u,initialData:g})]})},q=U}}]);