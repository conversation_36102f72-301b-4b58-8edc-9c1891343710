"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["871"],{4733:function(e,t,r){r.r(t),r.d(t,{UserGuidePageSimple:()=>g});var i=r(85893),s=r(67294),a=r(54757),n=r(98106),o=r(89126),l=r(86001),c=r(12550),d=r(88957),h=r(46560);let m=e=>{let{activeTab:t,onTabChange:r}=e;return(0,i.jsx)(o.Z,{elevation:2,sx:{borderRadius:2,mb:3,overflow:"hidden",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.05)"},children:(0,i.jsxs)(d.Z,{value:t,onChange:(e,t)=>{r(t)},"aria-label":"user guide tabs",textColor:"primary",indicatorColor:"primary",variant:"scrollable",scrollButtons:"auto",sx:{"& .MuiTab-root":{textTransform:"none",minWidth:120,fontSize:"0.875rem",fontWeight:500,color:"text.secondary",py:2,"&.Mui-selected":{color:"primary.main",fontWeight:600},"&:hover":{backgroundColor:"rgba(0, 0, 0, 0.03)"}},"& .MuiTabs-indicator":{height:3,borderTopLeftRadius:3,borderTopRightRadius:3}},children:[(0,i.jsx)(h.Z,{label:"Getting Started",value:"getting-started"}),(0,i.jsx)(h.Z,{label:"Dashboard",value:"dashboard"}),(0,i.jsx)(h.Z,{label:"Admissions",value:"admissions"}),(0,i.jsx)(h.Z,{label:"Front of House",value:"front-of-house"}),(0,i.jsx)(h.Z,{label:"Shift Management",value:"shift-management"}),(0,i.jsx)(h.Z,{label:"Lost Property",value:"lost-property"}),(0,i.jsx)(h.Z,{label:"Knowledge Base",value:"knowledge-base"}),(0,i.jsx)(h.Z,{label:"Reports",value:"reports"}),(0,i.jsx)(h.Z,{label:"Feedback",value:"feedback"}),(0,i.jsx)(h.Z,{label:"Access Management",value:"access-management"}),(0,i.jsx)(h.Z,{label:"Data Management",value:"data-management"}),(0,i.jsx)(h.Z,{label:"Technical Support",value:"technical-support"}),(0,i.jsx)(h.Z,{label:"FAQ",value:"faq"})]})})};var u=r(33991);let x=(0,r(96847).ZP)("img")({width:"100%",maxWidth:"800px",borderRadius:"8px",boxShadow:"0 4px 12px rgba(0, 0, 0, 0.15)",marginTop:"16px",marginBottom:"24px",transition:"transform 0.3s ease","&:hover":{transform:"scale(1.02)"}}),p=e=>{let{imageName:t,altText:r}=e;return(0,i.jsx)(u.Z,{sx:{display:"flex",justifyContent:"center",mt:3,mb:2},children:(0,i.jsx)(x,{src:`/images/userguide/${t}.png`,alt:r||`Screenshot for ${t}`,style:{display:"block",maxWidth:"100%"}})})},g=()=>{let[e,t]=(0,s.useState)("dashboard");return(0,i.jsxs)(n.Z,{maxWidth:"lg",sx:{py:4},children:[(0,i.jsx)(o.Z,{elevation:0,sx:{p:3,mb:4,bgcolor:"background.paper",borderRadius:2},children:(0,i.jsx)(c.Z,{container:!0,spacing:2,alignItems:"center",children:(0,i.jsxs)(c.Z,{size:{xs:12},children:[(0,i.jsx)(a.Z,{variant:"h4",component:"h1",gutterBottom:!0,color:"primary",children:"iThink Welfare System User Guide"}),(0,i.jsx)(a.Z,{variant:"body1",color:"text.secondary",paragraph:!0,children:"This guide provides comprehensive information on using the iThink Welfare System. Select a section from the tabs below to learn more about specific features."})]})})}),(0,i.jsx)(m,{activeTab:e,onTabChange:t}),(0,i.jsx)(l.Z,{in:!0,timeout:500,children:(0,i.jsx)(o.Z,{sx:{p:4,mb:3,borderRadius:2},children:(()=>{switch(e){case"getting-started":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Getting Started"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"To access the iThink Welfare system, you'll need to:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Navigate to https://ithink-welfare.brisflix.workers.dev/"}),(0,i.jsx)("li",{children:"You'll be presented with a Cloudflare login page"}),(0,i.jsx)("li",{children:"Enter your email address when prompted"}),(0,i.jsx)("li",{children:"Complete the authentication process"})]}),(0,i.jsxs)(a.Z,{variant:"body1",paragraph:!0,children:[(0,i.jsx)("strong",{children:"Note:"})," This secure login process ensures that only authorized personnel can access the system. Access to specific features is controlled by your assigned role (Admin, Partner, User, or Public)."]}),(0,i.jsx)(p,{imageName:"getting-started-1",altText:"Active Festival selector in sidebar"})]});case"dashboard":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Dashboard"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"The Dashboard is your central hub for monitoring welfare activities. Here you can:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"View current festival status and key statistics"}),(0,i.jsx)("li",{children:"Access quick links to common tasks"}),(0,i.jsx)("li",{children:"Monitor recent admissions and activities"}),(0,i.jsx)("li",{children:"See current bay/chair occupancy status"})]}),(0,i.jsx)(p,{imageName:"dashboard-1",altText:"Dashboard overview"})]});case"admissions":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Admissions"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"The Admissions page shows all welfare admissions. You can:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"View all current and past admissions"}),(0,i.jsx)("li",{children:"Filter admissions by date, status, or other criteria"}),(0,i.jsx)("li",{children:"Access detailed information about each admission"}),(0,i.jsx)("li",{children:"Update admission status and add notes"}),(0,i.jsx)("li",{children:"Monitor bay/chair assignments"})]}),(0,i.jsx)(p,{imageName:"admissions-1",altText:"Admissions list"}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,color:"primary.dark",fontWeight:"500",sx:{mt:4},children:"Creating a New Admission"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"To create a new admission:"}),(0,i.jsxs)("ol",{children:[(0,i.jsx)("li",{children:'Click the "New Admission" button'}),(0,i.jsx)("li",{children:"Fill out the personal information"}),(0,i.jsx)("li",{children:"Complete all relevant sections"}),(0,i.jsx)("li",{children:"Add any additional notes"}),(0,i.jsx)("li",{children:"Save the admission to create the record"})]}),(0,i.jsx)(p,{imageName:"admissions-2",altText:"New admission form"})]});case"front-of-house":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Front of House"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"The Front of House page helps track welfare items given to festival attendees:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Record items provided to visitors"}),(0,i.jsx)("li",{children:"Track inventory of essential supplies"}),(0,i.jsx)("li",{children:"Monitor usage patterns"}),(0,i.jsx)("li",{children:"View low stock warnings"})]}),(0,i.jsx)(p,{imageName:"front-of-house-1",altText:"Front of house interface"}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,color:"primary.dark",fontWeight:"500",sx:{mt:4},children:"Item Distribution"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"There are two ways to record distributed items:"}),(0,i.jsxs)("ol",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Single Click"}),": Click once on an item button to add one of that item"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Long Press"}),": Press and hold an item button to enter a specific quantity",(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Especially useful for distributing multiple items at once"}),(0,i.jsx)("li",{children:"Optimized for iPad and touch devices"})]})]})]})]});case"shift-management":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Shift Management ** Work in Progress **"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"Manage staff scheduling through:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Viewing current shift patterns"}),(0,i.jsx)("li",{children:"Checking team leader assignments"}),(0,i.jsx)("li",{children:"Monitoring shift notes"}),(0,i.jsx)("li",{children:"Viewing team member schedules"}),(0,i.jsx)("li",{children:"Following the rotation pattern (A → B → C → D → E)"})]})]});case"lost-property":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Lost Property"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"Track and manage lost property items:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Log new found items with detailed descriptions"}),(0,i.jsx)("li",{children:"Categorize items (Phone, Passport, Keys, etc.)"}),(0,i.jsx)("li",{children:"Add photos of found items when possible"}),(0,i.jsx)("li",{children:"Record claimed items and return information"}),(0,i.jsx)("li",{children:"Search existing lost property using the search box"}),(0,i.jsx)("li",{children:"Generate lost property reports"})]}),(0,i.jsx)(p,{imageName:"lost-property-1",altText:"Lost property logging form"}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,color:"primary.dark",fontWeight:"500",sx:{mt:4},children:"Managing Returns"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"When an item is claimed:"}),(0,i.jsxs)("ol",{children:[(0,i.jsx)("li",{children:"Find the item in the list"}),(0,i.jsx)("li",{children:'Click "Mark as Returned"'}),(0,i.jsx)("li",{children:"Enter the return details"})]}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"If an item was accidentally marked as returned:"}),(0,i.jsxs)("ol",{children:[(0,i.jsx)("li",{children:"Find the item in the returned items list"}),(0,i.jsx)("li",{children:'Click "Unmark as Returned"'}),(0,i.jsx)("li",{children:"The item will be moved back to the active list"})]})]});case"knowledge-base":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Knowledge Base"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"The Knowledge Base provides quick access to important external resources:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Browse resources organized by category and subcategory"}),(0,i.jsx)("li",{children:"Access substance information, mental health resources, and support contacts"}),(0,i.jsx)("li",{children:"View phone numbers and website links for external services"}),(0,i.jsx)("li",{children:"Resources may be specific to the current festival or available across all festivals"}),(0,i.jsx)("li",{children:"Search for specific resources using the search function"})]}),(0,i.jsx)(p,{imageName:"knowledge-base-1",altText:"Knowledge Base tile view"}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,color:"primary.dark",fontWeight:"500",sx:{mt:4},children:"Adding Knowledge Base Resources"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"If you have admin access, you can add new resources:"}),(0,i.jsxs)("ol",{children:[(0,i.jsx)("li",{children:'Click the "Add Resource" button'}),(0,i.jsxs)("li",{children:["Enter the resource details:",(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Title and description"}),(0,i.jsx)("li",{children:"URL and/or phone number"}),(0,i.jsx)("li",{children:"Category and subcategory"}),(0,i.jsx)("li",{children:"Specify if the resource should be available for all festivals"})]})]}),(0,i.jsx)("li",{children:"Save the resource to make it available to all users"})]})]});case"reports":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Reports"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"Generate comprehensive analytics through:"}),(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:["Admissions Reports",(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"View admission trends"}),(0,i.jsx)("li",{children:"Track substance use patterns"}),(0,i.jsx)("li",{children:"Monitor safeguarding incidents"})]})]}),(0,i.jsxs)("li",{children:["Front of House Reports",(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Item distribution statistics"}),(0,i.jsx)("li",{children:"Stock level tracking"}),(0,i.jsx)("li",{children:"Usage patterns"})]})]}),(0,i.jsxs)("li",{children:["Lost Property Reports",(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Items found/claimed statistics"}),(0,i.jsx)("li",{children:"Category breakdowns"}),(0,i.jsx)("li",{children:"Time-based analysis"})]})]})]}),(0,i.jsx)(p,{imageName:"reports-1",altText:"Reports dashboard"}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,color:"primary.dark",fontWeight:"500",sx:{mt:4},children:"Generating Reports"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"To create a report:"}),(0,i.jsxs)("ol",{children:[(0,i.jsx)("li",{children:"Select the report type"}),(0,i.jsx)("li",{children:"Choose the date range"}),(0,i.jsx)("li",{children:"Select specific data points to include"}),(0,i.jsx)("li",{children:"Generate the report in your preferred format (PDF available)"})]}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,color:"primary.dark",fontWeight:"500",sx:{mt:4},children:"Interactive Reports"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"The reports include interactive elements:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Click on any bar in the admissions chart to see patients admitted during that time period"}),(0,i.jsx)("li",{children:"Click on any row in the tables to view detailed information about that record"}),(0,i.jsx)("li",{children:"Use the search and filter functions to find specific records"}),(0,i.jsx)("li",{children:"Sort columns by clicking on column headers"})]})]});case"feedback":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Feedback"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"Help improve the system:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Click the feedback button in the bottom-right corner"}),(0,i.jsx)("li",{children:"Rate your experience"}),(0,i.jsx)("li",{children:"Provide detailed comments"}),(0,i.jsx)("li",{children:"Report any issues encountered"}),(0,i.jsx)("li",{children:"Track the status of your submitted feedback"})]}),(0,i.jsx)(p,{imageName:"feedback-1",altText:"Feedback form"})]});case"access-management":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Access Management"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"For administrators, the Access Management page provides control over user permissions:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"View all users who have accessed the system"}),(0,i.jsx)("li",{children:"Assign roles to users (Admin, Partner, User, Public)"}),(0,i.jsx)("li",{children:"Set required access levels for different features"}),(0,i.jsx)("li",{children:"Monitor user activity and access patterns"})]}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,color:"primary.dark",fontWeight:"500",sx:{mt:4},children:"User Roles"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"The system uses a role-based access control system:"}),(0,i.jsxs)("ul",{children:[(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Admin"}),": Full access to all features and management functions"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Partner"}),": Access to most features except system administration"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"User"}),": Standard access to core welfare functions"]}),(0,i.jsxs)("li",{children:[(0,i.jsx)("strong",{children:"Public"}),": Limited access to basic information only"]})]})]});case"data-management":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Data Management"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"Important notes about data handling:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"All data is automatically synchronized between devices"}),(0,i.jsx)("li",{children:"The system works offline with automatic sync when online"}),(0,i.jsx)("li",{children:"Live sync with WebSocket support ensures real-time updates"}),(0,i.jsx)("li",{children:"Improved conflict resolution for simultaneous edits"}),(0,i.jsx)("li",{children:"Records are retained for 3 months"}),(0,i.jsx)("li",{children:"Regular backups ensure data safety"}),(0,i.jsx)("li",{children:"Export functionality available for data preservation"})]}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,color:"primary.dark",fontWeight:"500",sx:{mt:4},children:"Festival-Specific Data"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"How data is organized by festival:"}),(0,i.jsxs)("ul",{children:[(0,i.jsx)("li",{children:"Each festival maintains its own separate data"}),(0,i.jsx)("li",{children:"Some resources (like Knowledge Base items) can be shared across festivals"}),(0,i.jsx)("li",{children:"Your active festival selection is browser-specific, allowing different users to work with different festivals simultaneously"})]})]});case"technical-support":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Technical Support"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"If you encounter any issues:"}),(0,i.jsxs)("ol",{children:[(0,i.jsx)("li",{children:"Check your internet connection"}),(0,i.jsx)("li",{children:"Try refreshing the page"}),(0,i.jsx)("li",{children:"Use the feedback button to report specific problems"}),(0,i.jsx)("li",{children:"Contact your system administrator if problems persist"})]}),(0,i.jsxs)(a.Z,{variant:"body1",paragraph:!0,sx:{mt:3},children:[(0,i.jsx)("strong",{children:"Note:"})," Screenshots will be updated regularly to reflect the latest interface changes. For technical support or questions, please contact your system administrator."]})]});case"faq":return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(a.Z,{variant:"h5",component:"h2",gutterBottom:!0,color:"primary.dark",fontWeight:"500",children:"Frequently Asked Questions (FAQ)"}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:"Q: What is the iThinc Welfare Management System?"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"A: It's a web application designed for managing welfare services at festivals and events, covering admissions, shifts, inventory, lost property, and reporting across multiple sites if needed."}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:"Q: Can I use the system offline?"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"A: Yes, the system is designed with offline-first capability. It uses local storage (PouchDB) allowing you to continue working without an internet connection. Data syncs automatically with the central database (CouchDB) when you're back online."}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:"Q: How does the system handle multiple sites at a festival (e.g., Arena and Campsite)?"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"A: The system supports multi-site management. You can easily switch between configured sites (like Arena or Campsite) using the selector in the festival header. Data for admissions, inventory, and shifts is kept separate for each site, ensuring accurate tracking."}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:"Q: How do I add a new festival or manage existing ones?"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"A: Navigate to the Festival Management page. Here you can create new festivals, edit details of existing ones (name, dates, location), configure sites (Arena, Campsite), and set the active festival."}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:"Q: How is patient data recorded in Admissions?"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"A: The Admission form is comprehensive and modular. It includes sections for Personal Information, Location within the festival, Physical Description, Substance Use details, Safeguarding concerns, Referral information, and general Admission Notes. Fill in the relevant sections to create a detailed record."}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:"Q: Can I track inventory usage per site?"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"A: Yes, inventory management is site-specific. When you record item usage (e.g., handing out water, ponchos, first aid supplies), it's tracked against the currently selected site (Arena or Campsite)."}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:"Q: How does shift scheduling work?"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"A: Administrators can configure shift patterns and assign team leaders via the Shifts page. The system helps generate schedules, often following a predefined rotation pattern. Shift notes specific to each site can also be recorded."}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:"Q: What happens if two users edit the same record while offline?"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"A: The system uses PouchDB and CouchDB, which include mechanisms for conflict resolution. When data syncs, the system attempts to merge changes intelligently to maintain data integrity."}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:"Q: How do I find a specific lost property item?"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"A: Go to the Lost Property page. You can use the quick search bar for general searches or utilize the advanced filtering options (filter by category, status, date found, description keywords, etc.) to locate specific items efficiently."}),(0,i.jsx)(a.Z,{variant:"h6",gutterBottom:!0,sx:{mt:2},children:"Q: How can I provide feedback about the system?"}),(0,i.jsx)(a.Z,{variant:"body1",paragraph:!0,children:"A: A feedback button is available on most pages. Clicking this button opens a form where you can submit comments, suggestions, or report any issues you encounter. This feedback is reviewed by the administrators."})]})}})()})})]})}}}]);