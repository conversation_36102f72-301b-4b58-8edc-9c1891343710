"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["581"],{86202:function(e,i,s){s.d(i,{k:()=>Z});var t=s(85893);s(67294);var n=s(30925),r=s(54757),a=s(81839),l=s(12550),d=s(33991),c=s(74542),o=s(17047),h=s(64889),x=s(13319),j=s(7230);let Z=e=>{let{open:i,onClose:s,record:Z,recordType:u}=e;if(!Z)return null;let m=e=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(n.Z,{children:[(0,t.jsx)(r.Z,{variant:"h5",fontWeight:"bold",children:`${e.FirstName||""} ${e.Surname||""}`.trim()||"Unnamed Patient"}),(0,t.jsxs)(r.Z,{variant:"subtitle1",color:"text.secondary",children:["Admitted: ",e.Attended?new Date(e.Attended).toLocaleString():"Unknown"]})]}),(0,t.jsx)(a.Z,{dividers:!0,children:(0,t.jsxs)(l.Z,{container:!0,spacing:3,children:[(0,t.jsxs)(l.Z,{size:{xs:12,md:6},children:[(0,t.jsx)(r.Z,{variant:"h6",gutterBottom:!0,children:"Personal Information"}),(0,t.jsxs)(l.Z,{container:!0,spacing:2,children:[(0,t.jsxs)(l.Z,{size:{xs:6},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Age"}),(0,t.jsx)(r.Z,{children:e.Age||"N/A"})]}),(0,t.jsxs)(l.Z,{size:{xs:6},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Gender"}),(0,t.jsx)(r.Z,{children:e.Gender||"N/A"})]}),(0,t.jsxs)(l.Z,{size:{xs:12},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Physical Description"}),(0,t.jsxs)(d.Z,{children:[e.HairColour&&(0,t.jsxs)(r.Z,{children:["Hair: ",e.HairColour,", ",e.HairStyle]}),e.ClothingTop&&(0,t.jsxs)(r.Z,{children:["Clothes: ",e.ClothingTop," (top), ",e.ClothingBottom," (bottom)"]}),e.Footwear&&(0,t.jsxs)(r.Z,{children:["Footwear: ",e.Footwear]}),e.OtherFeatures&&(0,t.jsxs)(r.Z,{children:["Other features: ",e.OtherFeatures]})]})]})]}),(0,t.jsx)(c.Z,{sx:{my:2}}),(0,t.jsx)(r.Z,{variant:"h6",gutterBottom:!0,children:"Incident Details"}),(0,t.jsxs)(l.Z,{container:!0,spacing:2,children:[(0,t.jsxs)(l.Z,{size:{xs:6},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Reason"}),(0,t.jsx)(r.Z,{children:e.ReasonCategory||"N/A"})]}),(0,t.jsxs)(l.Z,{size:{xs:6},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Referred By"}),(0,t.jsx)(r.Z,{children:e.ReferredBy||"N/A"})]}),(0,t.jsxs)(l.Z,{size:{xs:12},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Bay/Chair"}),(0,t.jsx)(r.Z,{children:e.BaysOrChairs||"N/A"})]}),(0,t.jsxs)(l.Z,{size:{xs:12},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Location"}),(0,t.jsx)(r.Z,{children:e.Location||"N/A"})]})]})]}),(0,t.jsxs)(l.Z,{size:{xs:12,md:6},children:[(0,t.jsx)(r.Z,{variant:"h6",gutterBottom:!0,children:"Substance Use"}),(0,t.jsx)(d.Z,{sx:{mb:2},children:e.SubstanceUsed&&e.SubstanceUsed.length>0?e.SubstanceUsed.map((e,i)=>(0,t.jsx)(o.Z,{label:e,sx:{m:.5},color:"primary",variant:"outlined"},i)):(0,t.jsx)(r.Z,{children:"None reported"})}),(0,t.jsx)(c.Z,{sx:{my:2}}),(0,t.jsx)(r.Z,{variant:"h6",gutterBottom:!0,children:"Notes"}),e.AdditionalNotes&&e.AdditionalNotes.length>0?e.AdditionalNotes.map((e,i)=>(0,t.jsxs)(d.Z,{sx:{mb:2},children:[(0,t.jsx)(r.Z,{variant:"body2",color:"text.secondary",children:e.timestamp?new Date(e.timestamp).toLocaleString():"No timestamp"}),(0,t.jsx)(r.Z,{variant:"body1",children:e.note})]},i)):(0,t.jsx)(r.Z,{children:"No additional notes"}),e.Safeguarding&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(c.Z,{sx:{my:2}}),(0,t.jsx)(r.Z,{variant:"h6",gutterBottom:!0,children:"Safeguarding"}),(0,t.jsx)(r.Z,{variant:"body1",children:e.Safeguarding||"No details provided"})]})]})]})})]}),g=e=>{let i=["Sanitizer","ToiletRoll","Suncream","Poncho","Earplugs","Condoms","ChildrensWristbands","GeneralWristbands","Water","Charging","SanitaryProducts","GeneralEnqs"],s=i.reduce((i,s)=>i+(e[s]||0),0);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(n.Z,{children:[(0,t.jsx)(r.Z,{variant:"h5",fontWeight:"bold",children:"Item Distribution Record"}),(0,t.jsxs)(r.Z,{variant:"subtitle1",color:"text.secondary",children:["Recorded: ",e.createdAt?new Date(e.createdAt).toLocaleString():"Unknown"]})]}),(0,t.jsx)(a.Z,{dividers:!0,children:(0,t.jsx)(l.Z,{container:!0,spacing:3,children:(0,t.jsxs)(l.Z,{size:{xs:12},children:[(0,t.jsx)(r.Z,{variant:"h6",gutterBottom:!0,children:"Distribution Summary"}),(0,t.jsxs)(r.Z,{variant:"subtitle1",children:["Total Items Distributed: ",s]}),(0,t.jsxs)(r.Z,{variant:"subtitle1",children:["Location: ",e.locationName||e.locationType||"N/A"]}),(0,t.jsx)(c.Z,{sx:{my:2}}),(0,t.jsx)(r.Z,{variant:"h6",gutterBottom:!0,children:"Items Distributed"}),(0,t.jsx)(l.Z,{container:!0,spacing:2,children:i.map(i=>e[i]?(0,t.jsxs)(l.Z,{size:{xs:6,sm:4,md:3},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:i.replace(/([A-Z])/g," $1").trim()}),(0,t.jsx)(r.Z,{children:e[i]||0})]},i):null)})]})})})]})},b=e=>(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)(n.Z,{children:[(0,t.jsx)(r.Z,{variant:"h5",fontWeight:"bold",children:e.category||"Lost Item"}),(0,t.jsxs)(r.Z,{variant:"subtitle1",color:"text.secondary",children:["Status: ","claimed"===e.status?"Claimed":"Unclaimed"]})]}),(0,t.jsx)(a.Z,{dividers:!0,children:(0,t.jsxs)(l.Z,{container:!0,spacing:3,children:[(0,t.jsxs)(l.Z,{size:{xs:12,md:6},children:[(0,t.jsx)(r.Z,{variant:"h6",gutterBottom:!0,children:"Item Details"}),(0,t.jsxs)(l.Z,{container:!0,spacing:2,children:[(0,t.jsxs)(l.Z,{size:{xs:12},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Description"}),(0,t.jsx)(r.Z,{children:e.description||"No description provided"})]}),(0,t.jsxs)(l.Z,{size:{xs:6},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Category"}),(0,t.jsx)(r.Z,{children:e.category||"N/A"})]}),(0,t.jsxs)(l.Z,{size:{xs:6},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Time Found"}),(0,t.jsx)(r.Z,{children:e.timeFound?new Date(e.timeFound).toLocaleString():"N/A"})]}),(0,t.jsxs)(l.Z,{size:{xs:12},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Where Found"}),(0,t.jsx)(r.Z,{children:e.whereFound||"N/A"})]})]})]}),"claimed"===e.status&&(0,t.jsxs)(l.Z,{size:{xs:12,md:6},children:[(0,t.jsx)(r.Z,{variant:"h6",gutterBottom:!0,children:"Claim Information"}),(0,t.jsxs)(l.Z,{container:!0,spacing:2,children:[(0,t.jsxs)(l.Z,{size:{xs:12},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Item Returned To"}),(0,t.jsx)(r.Z,{children:e.itemReturned||"N/A"})]}),(0,t.jsxs)(l.Z,{size:{xs:12},children:[(0,t.jsx)(r.Z,{variant:"subtitle2",children:"Additional Information"}),(0,t.jsx)(r.Z,{children:"This item has been claimed and is no longer available in lost property."})]})]})]})]})})]});return(0,t.jsxs)(h.Z,{open:i,onClose:s,maxWidth:"md",fullWidth:!0,PaperProps:{sx:{borderRadius:2,bgcolor:"background.paper"}},children:[(()=>{switch(u){case"admission":return m(Z);case"itemCount":return g(Z);case"lostProperty":return b(Z);default:return(0,t.jsx)(a.Z,{children:"No details available"})}})(),(0,t.jsx)(x.Z,{children:(0,t.jsx)(j.Z,{onClick:s,variant:"contained",children:"Close"})})]})}}}]);