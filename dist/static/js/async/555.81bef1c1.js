"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["555"],{43119:function(e,t,r){r.d(t,{n:()=>x});var i=r(85893);r(67294);var s=r(64889),o=r(30925),l=r(81839),n=r(33991),a=r(56099),d=r(39467),c=r(13319),u=r(7230),h=r(54757);let x=e=>{let{showReAdmitModal:t,showConfirmReAdmit:r,showConfirmDischarge:x,reAdmitLocation:m,reAdmitNotes:p,dischargeNotes:j,dischargeTime:b,bayStatus:y,isSubmitting:g,onReAdmitLocationChange:f,onReAdmitNotesChange:w,onDischargeNotesChange:v,onDischargeTimeChange:Z,onCloseReAdmit:C,onCloseConfirmReAdmit:S,onCloseConfirmDischarge:D,onConfirmReAdmit:k,onConfirmDischarge:A,onShowConfirmReAdmit:R}=e;return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(s.Z,{open:t,onClose:C,children:[(0,i.jsx)(o.Z,{children:"Re-Admit Patient"}),(0,i.jsx)(l.Z,{children:(0,i.jsxs)(n.Z,{sx:{pt:2,width:"100%",minWidth:300},children:[(0,i.jsx)(a.Z,{fullWidth:!0,type:"number",label:"Location (Bay Number)",value:m,onChange:e=>f(Number(e.target.value)),sx:{mb:2}}),y&&(0,i.jsx)(d.Z,{severity:y.isOccupied?"error":"success",sx:{mb:2},children:y.message}),(0,i.jsx)(a.Z,{fullWidth:!0,multiline:!0,rows:3,label:"Additional Notes",value:p,onChange:e=>w(e.target.value)})]})}),(0,i.jsxs)(c.Z,{children:[(0,i.jsx)(u.Z,{onClick:C,children:"Cancel"}),(0,i.jsx)(u.Z,{onClick:R,variant:"contained",disabled:!m||(y?.isOccupied??!1),children:"Re-Admit"})]})]}),(0,i.jsxs)(s.Z,{open:r,onClose:S,children:[(0,i.jsx)(o.Z,{children:"Confirm Re-Admission"}),(0,i.jsx)(l.Z,{children:(0,i.jsx)(h.Z,{children:"Are you sure you want to re-admit this patient?"})}),(0,i.jsxs)(c.Z,{children:[(0,i.jsx)(u.Z,{onClick:S,children:"Cancel"}),(0,i.jsx)(u.Z,{onClick:k,variant:"contained",color:"primary",children:"Confirm"})]})]}),(0,i.jsxs)(s.Z,{open:x,onClose:D,children:[(0,i.jsx)(o.Z,{children:"Discharge Patient"}),(0,i.jsx)(l.Z,{children:(0,i.jsxs)(n.Z,{sx:{pt:2,width:"100%",minWidth:300},children:[(0,i.jsx)(a.Z,{fullWidth:!0,type:"datetime-local",label:"Discharge Time",value:b,onChange:e=>Z(e.target.value),sx:{mb:2},InputLabelProps:{shrink:!0}}),(0,i.jsx)(a.Z,{fullWidth:!0,multiline:!0,rows:3,label:"Discharge Notes",value:j,onChange:e=>v(e.target.value),required:!0,error:!j,helperText:j?"":"Discharge notes are required"})]})}),(0,i.jsxs)(c.Z,{children:[(0,i.jsx)(u.Z,{onClick:D,children:"Cancel"}),(0,i.jsx)(u.Z,{onClick:A,variant:"contained",color:"primary",disabled:g||!j,children:g?"Discharging...":"Confirm"})]})]})]})}},51780:function(e,t,r){r.r(t),r.d(t,{ReportsPage:()=>eO});var i=r(85893),s=r(67294),o=r(33991),l=r(39467),n=r(5214),a=r(83502),d=r(78181),c=r(69326),u=r(7360),h=r(59326);let x=(e,t)=>{let[r,i]=(0,s.useState)({admissions:[],itemCounts:[],lostPropertyItems:[],sensoryHubVisits:[]}),[o,l]=(0,s.useState)(!0),[n,x]=(0,s.useState)(null),{activeSiteLocation:m}=(0,h.C)();return(0,s.useEffect)(()=>{(async()=>{if(!e)return l(!1);l(!0);try{let[t,r,s,o]=await Promise.all([a.R.getAdmissionsByFestival(e),a.R.getItemCountsByFestival(e),a.R.getLostPropertyItems(),a.R.getSensoryHubVisitsByFestival(e)]),l=m?t.filter(e=>e.siteLocationId===m.id):t,n=m?r.filter(e=>e.siteLocationId===m.id):r,d=s.filter(t=>{let r=t.festivalId===e;return m?r&&t.siteLocationId===m.id:r}),c=m?o.filter(e=>e.siteLocationId===m.id):o;i({admissions:l||[],itemCounts:n||[],lostPropertyItems:d||[],sensoryHubVisits:c||[]}),x(null)}catch(e){x(e instanceof Error?e.message:"An error occurred while fetching data"),i({admissions:[],itemCounts:[],lostPropertyItems:[],sensoryHubVisits:[]})}finally{l(!1)}})()},[e,m]),{data:(0,s.useMemo)(()=>o||!r?{admissions:[],itemCounts:[],lostPropertyItems:[],sensoryHubVisits:[]}:"all"===t?r:{admissions:r.admissions.filter(e=>e.Attended&&(0,d.K)((0,c.D)(e.Attended),(0,u.b)((0,c.D)(t)))),itemCounts:r.itemCounts.filter(e=>e.timestamp&&(0,d.K)((0,c.D)(e.timestamp),(0,u.b)((0,c.D)(t)))),lostPropertyItems:r.lostPropertyItems.filter(e=>e.timeFound&&(0,d.K)((0,c.D)(e.timeFound),(0,u.b)((0,c.D)(t)))),sensoryHubVisits:r.sensoryHubVisits.filter(e=>e.visitTimestamp&&(0,d.K)((0,c.D)(e.visitTimestamp),(0,u.b)((0,c.D)(t))))},[r,t,o]),loading:o,error:n}};var m=r(61215),p=r(89126),j=r(98106),b=r(54757),y=r(60488),g=r(38953),f=r(41994),w=r(9599),v=r(73892),Z=r(48346),C=r(73876),S=r(13894),D=r(60630);let k=e=>{let{startDate:t,endDate:r,selectedDay:s,onChange:o}=e;return(0,i.jsx)(v.Z,{size:"small",sx:{minWidth:200,ml:2,"& .MuiOutlinedInput-root":{bgcolor:"background.paper","&:hover .MuiOutlinedInput-notchedOutline":{borderColor:"primary.main"}},"& .MuiSelect-select":{py:1}},children:(0,i.jsxs)(Z.Z,{value:s,onChange:e=>{o(e.target.value)},displayEmpty:!0,variant:"outlined",children:[(0,i.jsx)(C.Z,{value:"all",children:"All Days"}),(0,S.D)({start:(0,c.D)(t),end:(0,c.D)(r)}).map((e,t)=>(0,i.jsxs)(C.Z,{value:e.toISOString(),children:["Day ",t+1," - ",(0,D.WU)(e,"MMM d, yyyy")]},e.toISOString()))]})})};var A=r(88957),R=r(46560);let I=e=>{let{activeTab:t,onTabChange:r}=e;return(0,i.jsx)(o.Z,{sx:{borderBottom:1,borderColor:"divider"},children:(0,i.jsxs)(A.Z,{value:t,onChange:(e,t)=>{r(t)},"aria-label":"report tabs",textColor:"primary",indicatorColor:"primary",sx:{"& .MuiTab-root":{textTransform:"none",minWidth:120,fontSize:"0.875rem",fontWeight:500,color:"text.secondary","&.Mui-selected":{color:"primary.main"}}},children:[(0,i.jsx)(R.Z,{label:"Admissions",value:"admissions",sx:{borderRadius:"8px 8px 0 0"}}),(0,i.jsx)(R.Z,{label:"Front of House",value:"frontOfHouse",sx:{borderRadius:"8px 8px 0 0"}}),(0,i.jsx)(R.Z,{label:"Lost Property",value:"lostProperty",sx:{borderRadius:"8px 8px 0 0"}}),(0,i.jsx)(R.Z,{label:"Sensory Hub",value:"sensoryHub",sx:{borderRadius:"8px 8px 0 0"}})]})})};var T=r(39764),N=r(8837);let P=["#662D91","#E0338C","#8E44AD","#E74C3C"],F=["Suncream","Poncho","Water","SanitaryProducts","Earplugs","Condoms","ChildrensWristbands","GeneralWristbands","Charging","Sanitizer","ToiletRoll","GeneralEnqs"],B=e=>{let t=Array.from({length:24},(e,t)=>({hour:t,count:0,label:(0,D.WU)(new Date().setHours(t,0,0,0),"ha")}));return e.forEach(e=>{let r=(0,N.p)((0,c.D)(e.Attended)),i=t.findIndex(e=>e.hour===r);-1!==i&&t[i].count++}),t},L=e=>Object.entries(e.reduce((e,t)=>(e[t.ReasonCategory]=(e[t.ReasonCategory]||0)+1,e),{})).map(e=>{let[t,r]=e;return{reason:t,count:r}}),G=e=>Object.entries(e.reduce((e,t)=>{let r=10*Math.floor((t.Age||0)/10),i=`${r}-${r+9}`;return e[i]=(e[i]||0)+1,e},{})).map(e=>{let[t,r]=e;return{ageGroup:t,count:r}}),M=e=>Object.entries(e.reduce((e,t)=>(F.forEach(r=>{e[r]=(e[r]||0)+(t[r]||0)}),e),{})).map(e=>{let[t,r]=e;return{type:t,count:r}}).sort((e,t)=>t.count-e.count),W=e=>{let t=Array.from({length:24},(e,t)=>({hour:t,count:0,label:(0,D.WU)(new Date().setHours(t,0,0,0),"ha")}));return e.forEach(e=>{if(!e.timeFound)return;let r=(0,N.p)((0,c.D)(e.timeFound)),i=t.findIndex(e=>e.hour===r);-1!==i&&t[i].count++}),t},O=e=>Object.entries(e.reduce((e,t)=>(e[t.category]=(e[t.category]||0)+1,e),{})).map(e=>{let[t,r]=e;return{category:t,count:r}}).sort((e,t)=>t.count-e.count),$=e=>Object.entries(e.reduce((e,t)=>{let r=t.itemReturned?"Returned":"Pending";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{status:t,count:r}}),z=e=>Object.entries(e.reduce((e,t)=>{let r="look_around"===t.purpose?"Look Around":"Use Service";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{purpose:t,count:r}}),H=e=>Object.entries(e.reduce((e,t)=>{let r="crew"===t.userType?"Crew":"Public";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{userType:t,count:r}}),E=e=>Object.entries(e.filter(e=>"crew"===e.userType&&e.teamName).reduce((e,t)=>{let r=t.teamName||"Unknown";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{teamName:t,count:r}}).sort((e,t)=>t.count-e.count);var K=r(15858);let V=T.mM.create({page:{padding:30},header:{marginBottom:30,borderBottom:"2px solid #662D91",paddingBottom:10,alignItems:"center"},logo:{width:200,marginBottom:10},section:{marginBottom:20},title:{fontSize:18,marginBottom:10,color:"#662D91"},sectionSubtitle:{fontSize:14,marginBottom:5,color:"#662D91"},row:{flexDirection:"row",paddingVertical:3},cell:{flex:1},text:{fontSize:12},footer:{position:"absolute",bottom:30,left:30,right:30,textAlign:"center",color:"#662D91",fontSize:10,borderTop:"1px solid #662D91",paddingTop:10}}),U=e=>{let{admissions:t,itemCounts:r,lostPropertyItems:s,sensoryHubVisits:o}=e,l=L(t),n=G(t),a=M(r),d=$(s),c=z(o),u=H(o),h=E(o),x=new Date().toLocaleDateString();return(0,i.jsx)(T.BB,{children:(0,i.jsxs)(T.T3,{size:"A4",style:V.page,children:[(0,i.jsx)(T.G7,{style:V.header,children:(0,i.jsx)(T.Ee,{src:"/ithinklogo.png",style:V.logo})}),(0,i.jsxs)(T.G7,{style:V.section,children:[(0,i.jsx)(T.xv,{style:V.title,children:"Admissions Report"}),(0,i.jsx)(T.xv,{style:V.sectionSubtitle,children:"Admission Reasons"}),l.map((e,t)=>(0,i.jsxs)(T.G7,{style:V.row,children:[(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.reason}),(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.count})]},t)),(0,i.jsx)(T.xv,{style:[V.sectionSubtitle,{marginTop:10}],children:"Age Distribution"}),n.map((e,t)=>(0,i.jsxs)(T.G7,{style:V.row,children:[(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.ageGroup}),(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.count})]},t))]}),(0,i.jsxs)(T.G7,{style:V.section,children:[(0,i.jsx)(T.xv,{style:V.title,children:"Front of House Report"}),a.map((e,t)=>(0,i.jsxs)(T.G7,{style:V.row,children:[(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.type}),(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.count})]},t))]}),(0,i.jsxs)(T.G7,{style:V.section,children:[(0,i.jsx)(T.xv,{style:V.title,children:"Lost Property Report"}),d.map((e,t)=>(0,i.jsxs)(T.G7,{style:V.row,children:[(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.status}),(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.count})]},t))]}),(0,i.jsxs)(T.G7,{style:V.section,children:[(0,i.jsx)(T.xv,{style:V.title,children:"Sensory Hub Report"}),(0,i.jsx)(T.xv,{style:V.sectionSubtitle,children:"Visit Purpose"}),c.map((e,t)=>(0,i.jsxs)(T.G7,{style:V.row,children:[(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.purpose}),(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.count})]},t)),(0,i.jsx)(T.xv,{style:[V.sectionSubtitle,{marginTop:10}],children:"User Type"}),u.map((e,t)=>(0,i.jsxs)(T.G7,{style:V.row,children:[(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.userType}),(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.count})]},t)),h.length>0&&(0,i.jsxs)(i.Fragment,{children:[(0,i.jsx)(T.xv,{style:[V.sectionSubtitle,{marginTop:10}],children:"Crew Teams"}),h.map((e,t)=>(0,i.jsxs)(T.G7,{style:V.row,children:[(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.teamName}),(0,i.jsx)(T.xv,{style:[V.cell,V.text],children:e.count})]},t))]}),(0,i.jsxs)(T.xv,{style:[V.sectionSubtitle,{marginTop:10}],children:["Total Visits: ",o.length]})]}),(0,i.jsxs)(T.xv,{style:V.footer,children:["Generated on ",x," • iTHINK Welfare System"]})]})})},_=e=>{let{admissions:t,itemCounts:r,lostPropertyItems:s,sensoryHubVisits:l,open:n,onClose:a}=e;return(0,i.jsx)(K.Z,{open:n,onClose:a,"aria-labelledby":"pdf-preview-modal",sx:{display:"flex",alignItems:"center",justifyContent:"center"},children:(0,i.jsx)(o.Z,{sx:{width:"90%",height:"90%",bgcolor:"background.paper"},children:(0,i.jsx)(T.Z$,{style:{width:"100%",height:"100%"},children:(0,i.jsx)(U,{admissions:t,itemCounts:r,lostPropertyItems:s,sensoryHubVisits:l})})})})},q=async function(e,t,r){let s=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],o=(0,i.jsx)(U,{admissions:e,itemCounts:t,lostPropertyItems:r,sensoryHubVisits:s}),l=await (0,T.eA)(o).toBlob(),n=URL.createObjectURL(l),a=document.createElement("a");a.href=n,a.download=`welfare-report-${new Date().toISOString().split("T")[0]}.pdf`,a.click(),URL.revokeObjectURL(n)},Q=e=>{let{festival:t,selectedDay:r,onDayChange:n,activeTab:a,onTabChange:d,loading:c,error:u,children:h,data:x}=e,[v,Z]=(0,s.useState)(!1),C=async()=>{x&&await q(x.admissions,x.itemCounts,x.lostPropertyItems,x.sensoryHubVisits)};return c?(0,i.jsx)(o.Z,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"200px",children:(0,i.jsx)(m.Z,{})}):u?(0,i.jsx)(o.Z,{p:2,children:(0,i.jsxs)(l.Z,{severity:"error",children:["Error: ",u]})}):(0,i.jsx)(p.Z,{elevation:0,sx:{minHeight:"100vh",bgcolor:"background.paper",backdropFilter:"blur(8px)",py:4},children:(0,i.jsxs)(j.Z,{maxWidth:"xl",children:[(0,i.jsxs)(o.Z,{mb:4,children:[(0,i.jsxs)(b.Z,{variant:"h4",color:"text.primary",gutterBottom:!0,children:["Reports for ",t.name]}),(0,i.jsxs)(o.Z,{display:"flex",alignItems:"center",gap:2,mt:1,children:[(0,i.jsxs)(b.Z,{variant:"body2",color:"text.secondary",children:[new Date(t.startDate).toLocaleDateString()," - ",new Date(t.endDate).toLocaleDateString()]}),(0,i.jsx)(k,{startDate:t.startDate,endDate:t.endDate,selectedDay:r,onChange:n})]})]}),(0,i.jsxs)(o.Z,{display:"flex",justifyContent:"space-between",alignItems:"center",mb:3,children:[(0,i.jsx)(b.Z,{variant:"h5",color:"primary",children:"Reports"}),(0,i.jsxs)(o.Z,{display:"flex",alignItems:"center",gap:2,children:[(0,i.jsx)(I,{activeTab:a,onTabChange:d}),(0,i.jsxs)(o.Z,{children:[(0,i.jsx)(y.Z,{title:"Preview PDF",children:(0,i.jsx)(g.Z,{color:"primary",onClick:()=>Z(!0),disabled:!x,children:(0,i.jsx)(f.Z,{})})}),(0,i.jsx)(y.Z,{title:"Download PDF",children:(0,i.jsx)(g.Z,{color:"primary",onClick:C,disabled:!x,children:(0,i.jsx)(w.Z,{})})})]})]})]}),h,x&&(0,i.jsx)(_,{admissions:x.admissions,itemCounts:x.itemCounts,lostPropertyItems:x.lostPropertyItems,sensoryHubVisits:x.sensoryHubVisits,open:v,onClose:()=>Z(!1)})]})})};var J=r(12550),X=r(29009),Y=r(94831),ee=r(14195),et=r(3023),er=r(75358),ei=r(62722),es=r(64963),eo=r(43815),el=r(90722),en=r(34354),ea=r(46208),ed=r(64889),ec=r(30925),eu=r(81839),eh=r(62983),ex=r(11161),em=r(19410),ep=r(74542),ej=r(13319),eb=r(7230),ey=r(86202);let eg=e=>{let{admissions:t}=e,[r,l]=(0,s.useState)(null),[n,a]=(0,s.useState)(null),[d,u]=(0,s.useState)(!1),[h,x]=(0,s.useState)(!1),m=e=>{a(e),x(!0)},j=null!==r?t.filter(e=>!!e.Attended&&(0,N.p)((0,c.D)(e.Attended))===r):[];return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(J.Z,{container:!0,spacing:3,children:[(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsxs)(p.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Admissions by Time of Day"}),(0,i.jsx)("div",{style:{height:300,width:"100%"},children:(0,i.jsx)(X.h,{children:(0,i.jsxs)(Y.v,{data:B(t),children:[(0,i.jsx)(ee.q,{strokeDasharray:"3 3"}),(0,i.jsx)(et.K,{dataKey:"label",interval:2}),(0,i.jsx)(er.B,{}),(0,i.jsx)(ei.u,{formatter:e=>[e,"Admissions"]}),(0,i.jsx)(es.$,{dataKey:"count",fill:"#662D91",name:"Admissions",onClick:e=>{l(e.hour),u(!0)},cursor:"pointer",children:B(t).map((e,t)=>(0,i.jsx)(eo.b,{fill:e.count>0?"#662D91":"#E0E0E0"},`cell-${t}`))})]})})})]})}),(0,i.jsx)(J.Z,{size:{xs:12,md:6},children:(0,i.jsxs)(p.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,height:"100%"},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Admissions by Reason"}),(0,i.jsx)("div",{style:{height:300,width:"100%"},children:(0,i.jsx)(X.h,{children:(0,i.jsxs)(Y.v,{data:L(t),children:[(0,i.jsx)(ee.q,{strokeDasharray:"3 3"}),(0,i.jsx)(et.K,{dataKey:"reason"}),(0,i.jsx)(er.B,{}),(0,i.jsx)(ei.u,{}),(0,i.jsx)(el.D,{}),(0,i.jsx)(es.$,{dataKey:"count",name:"Count",children:L(t).map((e,t)=>(0,i.jsx)(eo.b,{fill:P[t%P.length]},`cell-${t}`))})]})})})]})}),(0,i.jsx)(J.Z,{size:{xs:12,md:6},children:(0,i.jsxs)(p.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,height:"100%"},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Age Distribution"}),(0,i.jsx)("div",{style:{height:300,width:"100%"},children:(0,i.jsx)(X.h,{children:(0,i.jsxs)(en.u,{children:[(0,i.jsx)(ea.b,{data:G(t),dataKey:"count",nameKey:"ageGroup",cx:"50%",cy:"50%",outerRadius:100,label:!0,children:G(t).map((e,t)=>(0,i.jsx)(eo.b,{fill:P[t%P.length]},`cell-${t}`))}),(0,i.jsx)(ei.u,{}),(0,i.jsx)(el.D,{})]})})})]})})]}),(0,i.jsxs)(ed.Z,{open:d,onClose:()=>u(!1),maxWidth:"md",fullWidth:!0,PaperProps:{sx:{borderRadius:2,bgcolor:"background.paper"}},children:[(0,i.jsxs)(ec.Z,{children:[(0,i.jsx)(b.Z,{variant:"h5",fontWeight:"bold",children:null!==r?`Patients Admitted at ${B(t).find(e=>e.hour===r)?.label||""}`:"Patients"}),(0,i.jsxs)(b.Z,{variant:"subtitle1",color:"text.secondary",children:[j.length," ",1===j.length?"patient":"patients"," admitted"]})]}),(0,i.jsx)(eu.Z,{dividers:!0,children:j.length>0?(0,i.jsx)(eh.Z,{children:j.map((e,t)=>(0,i.jsxs)(s.Fragment,{children:[(0,i.jsx)(ex.ZP,{component:"button",onClick:()=>m(e),sx:{borderRadius:1,"&:hover":{bgcolor:"action.hover"},transition:"background-color 0.2s"},children:(0,i.jsx)(em.Z,{primary:(0,i.jsx)(b.Z,{variant:"subtitle1",fontWeight:"medium",children:`${e.FirstName||""} ${e.Surname||""}`.trim()||"Unnamed Patient"}),secondary:(0,i.jsxs)(o.Z,{children:[(0,i.jsxs)(b.Z,{variant:"body2",component:"span",children:[e.Gender||"Unknown gender",e.Age?`, ${e.Age} years`:""]}),(0,i.jsx)(b.Z,{variant:"body2",component:"div",color:"text.secondary",children:e.ReasonCategory||"No reason specified"})]})})}),t<j.length-1&&(0,i.jsx)(ep.Z,{component:"li"})]},e._id||t))}):(0,i.jsx)(b.Z,{variant:"body1",align:"center",sx:{py:2},children:"No patient details available for this time period."})}),(0,i.jsx)(ej.Z,{children:(0,i.jsx)(eb.Z,{onClick:()=>u(!1),variant:"contained",children:"Close"})})]}),(0,i.jsx)(ey.k,{open:h,onClose:()=>x(!1),record:n,recordType:"admission"})]})};var ef=r(58308),ew=r(82400);let ev=e=>{let{admissions:t,selectedItems:r,onSelectAll:l,onSelectItem:n,onDelete:a,onDischarge:d}=e,[c,u]=(0,s.useState)(!1),[h,x]=(0,s.useState)(null),m=e=>{x(e),u(!0)},j=[{field:"name",headerName:"Name",flex:1,sortable:!0,filterable:!0,renderCell:e=>{let t=e.row;return`${t.FirstName||""} ${t.Surname||""}`.trim()||"N/A"},valueGetter:e=>{if(!e||!e.row||"object"!=typeof e.row)return"n/a";let t=e.row;return`${t.FirstName||""} ${t.Surname||""}`.trim().toLowerCase()||"n/a"}},{field:"Age",headerName:"Age",width:100,sortable:!0,filterable:!0,valueGetter:e=>{if(!e||!e.row||"object"!=typeof e.row)return;let t=e.row.Age;return"number"==typeof t?t:void 0},renderCell:e=>{let t=e.row;return"number"==typeof t.Age?t.Age:"N/A"}},{field:"ReferredBy",headerName:"Referred By",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.ReferredBy||"n/a",renderCell:e=>e.row.ReferredBy||"N/A"},{field:"ReasonCategory",headerName:"Reason",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.ReasonCategory||"n/a",renderCell:e=>e.row.ReasonCategory||"N/A"},{field:"SubstanceUsed",headerName:"Substances",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.SubstanceUsed?.join(", ").toLowerCase()||"n/a",renderCell:e=>{let t=e.row;return t.SubstanceUsed?.join(", ")||"N/A"}},{field:"Attended",headerName:"Admitted",flex:1,sortable:!0,filterable:!0,type:"dateTime",valueGetter:e=>e&&e.row&&"object"==typeof e.row&&"Attended"in e.row&&e.row.Attended?new Date(e.row.Attended):null,renderCell:e=>{if(!e||!e.row||"object"!=typeof e.row||!("Attended"in e.row))return"N/A";let t=e.row;return t.Attended?new Date(t.Attended).toLocaleString():"N/A"}},{field:"BaysOrChairs",headerName:"Bay/Chair",width:100,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.BaysOrChairs||"n/a",renderCell:e=>e.row.BaysOrChairs||"N/A"},{field:"Location",headerName:"Location",width:100,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.Location||"n/a",renderCell:e=>e.row.Location||"N/A"},{field:"actions",headerName:"Actions",width:120,sortable:!1,filterable:!1,renderCell:e=>{let t=e.row;return t.InBayNow&&d?(0,i.jsx)(eb.Z,{variant:"contained",color:"primary",size:"small",onClick:e=>{e.stopPropagation(),d(t)},children:"Discharge"}):null}}];return(0,i.jsxs)(p.Z,{elevation:3,sx:{bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,p:3},children:[(0,i.jsxs)(o.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",children:"Raw Data"}),r.length>0&&(0,i.jsxs)(eb.Z,{variant:"contained",color:"error",onClick:a,children:["Delete Selected (",r.length,")"]})]}),(0,i.jsx)("div",{style:{height:400,width:"100%"},children:(0,i.jsx)(ef._,{rows:t||[],columns:j,getRowId:e=>e._id,checkboxSelection:!0,disableRowSelectionOnClick:!0,rowSelectionModel:{type:"include",ids:new Set(r)},onRowSelectionModelChange:e=>{let t=Array.isArray(e)?e.map(e=>e.toString()):[];t.forEach(e=>{r.includes(e)||n(e)}),r.forEach(e=>{t.includes(e)||n(e)})},density:"compact",slots:{toolbar:ew.n},slotProps:{toolbar:{showQuickFilter:!0,quickFilterProps:{debounceMs:300}}},initialState:{sorting:{sortModel:[{field:"Attended",sort:"desc"}]}},onRowClick:e=>m(e.row),sx:{"& .MuiDataGrid-cell":{fontSize:"0.875rem"},"& .MuiDataGrid-toolbarContainer":{padding:"8px 24px"},"& .MuiDataGrid-toolbar":{"& .MuiFormControl-root":{width:"100%",maxWidth:"300px"}},"& .MuiDataGrid-row":{cursor:"pointer"},border:"none",bgcolor:"background.paper",backdropFilter:"blur(8px)"}})}),(0,i.jsx)(ey.k,{open:c,onClose:()=>{u(!1)},record:h,recordType:"admission"})]})};var eZ=r(43119);let eC=e=>{let{admissions:t,selectedItems:r,onSelectAll:o,onSelectItem:l,onDelete:n,onUpdateAdmission:a}=e,[d,c]=(0,s.useState)(!1),[u,h]=(0,s.useState)(null),[x,m]=(0,s.useState)(""),[p,j]=(0,s.useState)(new Date().toISOString().slice(0,16)),[b,y]=(0,s.useState)(!1),g=async()=>{if(u&&a&&x.trim()){y(!0);try{let e={...u,DischargeTime:p,AdditionalNotes:[...u.AdditionalNotes,{timestamp:p,note:`Discharge Notes: ${x}`,author:"Staff"}],History:[...u.History,{timestamp:p,action:"Discharged",details:x,author:"Staff"}],InBayNow:!1,status:"discharged"};await a(e),f()}catch(e){}finally{y(!1)}}},f=()=>{c(!1),h(null),m(""),j(new Date().toISOString().slice(0,16))};return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(J.Z,{container:!0,spacing:3,children:[(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsx)(eg,{admissions:t})}),(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsx)(ev,{admissions:t,selectedItems:r,onSelectAll:o,onSelectItem:l,onDelete:n,onDischarge:e=>{h(e),j(new Date().toISOString().slice(0,16)),m(""),c(!0)}})})]}),(0,i.jsx)(eZ.n,{showReAdmitModal:!1,showConfirmReAdmit:!1,showConfirmDischarge:d,reAdmitLocation:0,reAdmitNotes:"",dischargeNotes:x,dischargeTime:p,bayStatus:null,isSubmitting:b,onReAdmitLocationChange:()=>{},onReAdmitNotesChange:()=>{},onDischargeNotesChange:m,onDischargeTimeChange:j,onCloseReAdmit:()=>{},onCloseConfirmReAdmit:()=>{},onCloseConfirmDischarge:f,onConfirmReAdmit:()=>{},onConfirmDischarge:g,onShowConfirmReAdmit:()=>{}})]})},eS=e=>{let{itemCounts:t}=e,r=M(t);return(0,i.jsx)(J.Z,{container:!0,spacing:3,children:(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsxs)(p.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Items by Type"}),(0,i.jsx)("div",{style:{height:300,width:"100%"},children:(0,i.jsx)(X.h,{children:(0,i.jsxs)(Y.v,{data:r,children:[(0,i.jsx)(ee.q,{strokeDasharray:"3 3"}),(0,i.jsx)(et.K,{dataKey:"type"}),(0,i.jsx)(er.B,{}),(0,i.jsx)(ei.u,{}),(0,i.jsx)(el.D,{}),(0,i.jsx)(es.$,{dataKey:"count",name:"Count",children:r.map((e,t)=>(0,i.jsx)(eo.b,{fill:P[t%P.length]},`cell-${t}`))})]})})})]})})})},eD=["Sanitizer","ToiletRoll","Suncream","Poncho","Earplugs","Condoms","ChildrensWristbands","GeneralWristbands","Water","Charging","SanitaryProducts","GeneralEnqs"],ek=e=>{let{itemCounts:t,selectedItems:r,onSelectAll:l,onSelectItem:n,onDelete:a}=e,[d,c]=(0,s.useState)(!1),[u,h]=(0,s.useState)(null),x=e=>{h(e),c(!0)},m=[{field:"items",headerName:"Items",flex:2,renderCell:e=>{let t=e.row;return t?eD.filter(e=>t[e]).map(e=>`${e}: ${t[e]}`).join(", "):""}},{field:"totalCount",headerName:"Total Count",flex:1,renderCell:e=>{let t=e.row;return t?eD.reduce((e,r)=>e+(t[r]||0),0):0}},{field:"createdAt",headerName:"Timestamp",flex:1,renderCell:e=>{let t=e.row;return t.createdAt?new Date(t.createdAt).toLocaleString():"N/A"}}];return(0,i.jsxs)(p.Z,{elevation:3,sx:{bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,p:3},children:[(0,i.jsxs)(o.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",children:"Raw Data"}),r.length>0&&(0,i.jsxs)(eb.Z,{variant:"contained",color:"error",onClick:a,children:["Delete Selected (",r.length,")"]})]}),(0,i.jsx)("div",{style:{height:400,width:"100%"},children:(0,i.jsx)(ef._,{rows:t||[],columns:m,getRowId:e=>e._id,checkboxSelection:!0,disableRowSelectionOnClick:!0,rowSelectionModel:{type:"include",ids:new Set(r)},onRowSelectionModelChange:e=>{let t=e&&"ids"in e?Array.from(e.ids).map(e=>e.toString()):[];t.forEach(e=>{r.includes(e)||n(e)}),r.forEach(e=>{t.includes(e)||n(e)})},density:"compact",onRowClick:e=>x(e.row),sx:{"& .MuiDataGrid-cell":{fontSize:"0.875rem"},"& .MuiDataGrid-row":{cursor:"pointer"},border:"none",bgcolor:"background.paper",backdropFilter:"blur(8px)"}})}),(0,i.jsx)(ey.k,{open:d,onClose:()=>{c(!1)},record:u,recordType:"itemCount"})]})},eA=e=>{let{itemCounts:t,selectedItems:r,onSelectAll:s,onSelectItem:o,onDelete:l}=e;return(0,i.jsxs)(J.Z,{container:!0,spacing:3,children:[(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsx)(eS,{itemCounts:t})}),(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsx)(ek,{itemCounts:t,selectedItems:r,onSelectAll:s,onSelectItem:o,onDelete:l})})]})},eR=e=>{let{items:t}=e,r=W(t),s=O(t),o=$(t);return(0,i.jsxs)(J.Z,{container:!0,spacing:3,children:[(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsxs)(p.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Lost Property by Time of Day"}),(0,i.jsx)("div",{style:{height:300,width:"100%"},children:(0,i.jsx)(X.h,{children:(0,i.jsxs)(Y.v,{data:r,children:[(0,i.jsx)(ee.q,{strokeDasharray:"3 3"}),(0,i.jsx)(et.K,{dataKey:"label"}),(0,i.jsx)(er.B,{}),(0,i.jsx)(ei.u,{formatter:e=>[e,"Items"]}),(0,i.jsx)(es.$,{dataKey:"count",fill:"#662D91",name:"Items",children:r.map((e,t)=>(0,i.jsx)(eo.b,{fill:e.count>0?"#662D91":"#E0E0E0"},`cell-${t}`))})]})})})]})}),(0,i.jsx)(J.Z,{size:{xs:12,md:6},children:(0,i.jsxs)(p.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,height:"100%"},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Lost Property by Category"}),(0,i.jsx)("div",{style:{height:300,width:"100%"},children:(0,i.jsx)(X.h,{children:(0,i.jsxs)(Y.v,{data:s,children:[(0,i.jsx)(ee.q,{strokeDasharray:"3 3"}),(0,i.jsx)(et.K,{dataKey:"category"}),(0,i.jsx)(er.B,{}),(0,i.jsx)(ei.u,{}),(0,i.jsx)(el.D,{}),(0,i.jsx)(es.$,{dataKey:"count",name:"Count",children:s.map((e,t)=>(0,i.jsx)(eo.b,{fill:P[t%P.length]},`cell-${t}`))})]})})})]})}),(0,i.jsx)(J.Z,{size:{xs:12,md:6},children:(0,i.jsxs)(p.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,height:"100%"},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Lost Property Status"}),(0,i.jsx)("div",{style:{height:300,width:"100%"},children:(0,i.jsx)(X.h,{children:(0,i.jsxs)(en.u,{children:[(0,i.jsx)(ea.b,{data:o,dataKey:"count",nameKey:"status",cx:"50%",cy:"50%",outerRadius:100,label:!0,children:o.map((e,t)=>(0,i.jsx)(eo.b,{fill:P[t%P.length]},`cell-${t}`))}),(0,i.jsx)(ei.u,{}),(0,i.jsx)(el.D,{})]})})})]})})]})},eI=e=>{let{items:t,selectedItems:r,onSelectAll:l,onSelectItem:n,onDelete:a}=e,[d,c]=(0,s.useState)(!1),[u,h]=(0,s.useState)(null),x=e=>{h(e),c(!0)},m=[{field:"description",headerName:"Description",flex:2,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row?(e.row.description||"").toLowerCase():"",renderCell:e=>{let{row:t}=e;return t.description||"N/A"}},{field:"category",headerName:"Category",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row?(e.row.category||"").toLowerCase():"",renderCell:e=>{let{row:t}=e;return t.category||"N/A"}},{field:"status",headerName:"Status",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"claimed"===e.row.status?"claimed":"unclaimed",renderCell:e=>{let{row:t}=e;return"claimed"===t.status?"Claimed":"Unclaimed"}},{field:"timeFound",headerName:"Time Found",flex:1,sortable:!0,filterable:!0,type:"dateTime",valueGetter:e=>{if(!e||!e.row||"object"!=typeof e.row||!("timeFound"in e.row))return null;let t=e.row;return t.timeFound?new Date(t.timeFound):null},renderCell:e=>{let{row:t}=e;return t.timeFound?new Date(t.timeFound).toLocaleString():"N/A"}},{field:"whereFound",headerName:"Where Found",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row?(e.row.whereFound||"").toLowerCase():"",renderCell:e=>{let{row:t}=e;return t.whereFound||"N/A"}}];return(0,i.jsxs)(p.Z,{elevation:3,sx:{bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,p:3},children:[(0,i.jsxs)(o.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",children:"Raw Data"}),r.length>0&&(0,i.jsxs)(eb.Z,{variant:"contained",color:"error",onClick:a,children:["Delete Selected (",r.length,")"]})]}),(0,i.jsx)("div",{style:{height:400,width:"100%"},children:(0,i.jsx)(ef._,{rows:(t||[]).filter(Boolean),columns:m,getRowId:e=>e._id,checkboxSelection:!0,disableRowSelectionOnClick:!0,rowSelectionModel:{type:"include",ids:new Set(r)},onRowSelectionModelChange:e=>{let t=Array.isArray(e)?e.map(e=>e.toString()):[];t.forEach(e=>{r.includes(e)||n(e)}),r.forEach(e=>{t.includes(e)||n(e)})},density:"compact",slots:{toolbar:ew.n},slotProps:{toolbar:{showQuickFilter:!0,quickFilterProps:{debounceMs:300}}},initialState:{sorting:{sortModel:[{field:"timeFound",sort:"desc"}]}},onRowClick:e=>x(e.row),sx:{"& .MuiDataGrid-cell":{fontSize:"0.875rem"},"& .MuiDataGrid-toolbarContainer":{padding:"8px 24px"},"& .MuiDataGrid-toolbar":{"& .MuiFormControl-root":{width:"100%",maxWidth:"300px"}},"& .MuiDataGrid-row":{cursor:"pointer"},border:"none",bgcolor:"background.paper",backdropFilter:"blur(8px)"}})}),(0,i.jsx)(ey.k,{open:d,onClose:()=>{c(!1)},record:u,recordType:"lostProperty"})]})},eT=e=>{let{items:t,selectedItems:r,onSelectAll:s,onSelectItem:o,onDelete:l}=e;return(0,i.jsxs)(J.Z,{container:!0,spacing:3,children:[(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsx)(eR,{items:t})}),(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsx)(eI,{items:t,selectedItems:r,onSelectAll:s,onSelectItem:o,onDelete:l})})]})},eN=e=>{let t=Array.from({length:24},(e,t)=>({hour:t,label:`${t.toString().padStart(2,"0")}:00`,count:0}));return e.forEach(e=>{if(e.visitTimestamp){let r=(0,N.p)((0,c.D)(e.visitTimestamp));t[r].count++}}),t},eP=e=>Object.entries(e.reduce((e,t)=>{let r="look_around"===t.purpose?"Look Around":"Use Service";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{purpose:t,count:r}}),eF=e=>Object.entries(e.reduce((e,t)=>{let r="crew"===t.userType?"Crew":"Public";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{userType:t,count:r}}),eB=e=>Object.entries(e.filter(e=>"crew"===e.userType&&e.teamName).reduce((e,t)=>{let r=t.teamName||"Unknown Team";return e[r]=(e[r]||0)+1,e},{})).map(e=>{let[t,r]=e;return{team:t,count:r}}),eL=e=>{let{visits:t}=e,[r,l]=(0,s.useState)(null),[n,a]=(0,s.useState)(null),[d,u]=(0,s.useState)(!1),[h,x]=(0,s.useState)(!1),m=e=>{a(e),x(!0)},j=null!==r?t.filter(e=>!!e.visitTimestamp&&(0,N.p)((0,c.D)(e.visitTimestamp))===r):[];return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(J.Z,{container:!0,spacing:3,children:[(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsxs)(p.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Visits by Time of Day"}),(0,i.jsx)("div",{style:{height:300,width:"100%"},children:(0,i.jsx)(X.h,{children:(0,i.jsxs)(Y.v,{data:eN(t),children:[(0,i.jsx)(ee.q,{strokeDasharray:"3 3"}),(0,i.jsx)(et.K,{dataKey:"label",interval:2}),(0,i.jsx)(er.B,{}),(0,i.jsx)(ei.u,{formatter:e=>[e,"Visits"]}),(0,i.jsx)(es.$,{dataKey:"count",fill:"#662D91",name:"Visits",onClick:e=>{l(e.hour),u(!0)},cursor:"pointer",children:eN(t).map((e,t)=>(0,i.jsx)(eo.b,{fill:e.count>0?"#662D91":"#E0E0E0"},`cell-${t}`))})]})})})]})}),(0,i.jsx)(J.Z,{size:{xs:12,md:6},children:(0,i.jsxs)(p.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,height:"100%"},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Visits by Purpose"}),(0,i.jsx)("div",{style:{height:300,width:"100%"},children:(0,i.jsx)(X.h,{children:(0,i.jsxs)(en.u,{children:[(0,i.jsx)(ea.b,{data:eP(t),dataKey:"count",nameKey:"purpose",cx:"50%",cy:"50%",outerRadius:100,label:!0,children:eP(t).map((e,t)=>(0,i.jsx)(eo.b,{fill:P[t%P.length]},`cell-${t}`))}),(0,i.jsx)(ei.u,{}),(0,i.jsx)(el.D,{})]})})})]})}),(0,i.jsx)(J.Z,{size:{xs:12,md:6},children:(0,i.jsxs)(p.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,height:"100%"},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Visits by User Type"}),(0,i.jsx)("div",{style:{height:300,width:"100%"},children:(0,i.jsx)(X.h,{children:(0,i.jsxs)(en.u,{children:[(0,i.jsx)(ea.b,{data:eF(t),dataKey:"count",nameKey:"userType",cx:"50%",cy:"50%",outerRadius:100,label:!0,children:eF(t).map((e,t)=>(0,i.jsx)(eo.b,{fill:P[t%P.length]},`cell-${t}`))}),(0,i.jsx)(ei.u,{}),(0,i.jsx)(el.D,{})]})})})]})}),eB(t).length>0&&(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsxs)(p.Z,{elevation:3,sx:{p:3,bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",gutterBottom:!0,children:"Crew Visits by Team"}),(0,i.jsx)("div",{style:{height:300,width:"100%"},children:(0,i.jsx)(X.h,{children:(0,i.jsxs)(Y.v,{data:eB(t),children:[(0,i.jsx)(ee.q,{strokeDasharray:"3 3"}),(0,i.jsx)(et.K,{dataKey:"team"}),(0,i.jsx)(er.B,{}),(0,i.jsx)(ei.u,{}),(0,i.jsx)(el.D,{}),(0,i.jsx)(es.$,{dataKey:"count",name:"Visits",children:eB(t).map((e,t)=>(0,i.jsx)(eo.b,{fill:P[t%P.length]},`cell-${t}`))})]})})})]})})]}),(0,i.jsxs)(ed.Z,{open:d,onClose:()=>u(!1),maxWidth:"md",fullWidth:!0,PaperProps:{sx:{borderRadius:2,bgcolor:"background.paper"}},children:[(0,i.jsxs)(ec.Z,{children:[(0,i.jsx)(b.Z,{variant:"h5",fontWeight:"bold",children:null!==r?`Visits at ${eN(t).find(e=>e.hour===r)?.label||""}`:"Visits"}),(0,i.jsxs)(b.Z,{variant:"subtitle1",color:"text.secondary",children:[j.length," ",1===j.length?"visit":"visits"," recorded"]})]}),(0,i.jsx)(eu.Z,{dividers:!0,children:j.length>0?(0,i.jsx)(eh.Z,{children:j.map((e,t)=>(0,i.jsxs)(s.Fragment,{children:[(0,i.jsx)(ex.ZP,{component:"button",onClick:()=>m(e),sx:{borderRadius:1,"&:hover":{bgcolor:"action.hover"},transition:"background-color 0.2s"},children:(0,i.jsx)(em.Z,{primary:(0,i.jsx)(b.Z,{variant:"subtitle1",fontWeight:"medium",children:"look_around"===e.purpose?"Look Around":"Use Service"}),secondary:(0,i.jsxs)(o.Z,{children:[(0,i.jsxs)(b.Z,{variant:"body2",component:"span",children:["crew"===e.userType?"Crew":"Public",e.teamName&&` - ${e.teamName}`]}),(0,i.jsx)(b.Z,{variant:"body2",component:"div",color:"text.secondary",children:new Date(e.visitTimestamp).toLocaleString()})]})})}),t<j.length-1&&(0,i.jsx)(ep.Z,{component:"li"})]},e._id||t))}):(0,i.jsx)(b.Z,{variant:"body1",align:"center",sx:{py:2},children:"No visit details available for this time period."})}),(0,i.jsx)(ej.Z,{children:(0,i.jsx)(eb.Z,{onClick:()=>u(!1),variant:"contained",children:"Close"})})]}),(0,i.jsxs)(ed.Z,{open:h,onClose:()=>x(!1),maxWidth:"sm",fullWidth:!0,PaperProps:{sx:{borderRadius:2,bgcolor:"background.paper"}},children:[(0,i.jsx)(ec.Z,{children:(0,i.jsx)(b.Z,{variant:"h5",fontWeight:"bold",children:"Visit Details"})}),(0,i.jsx)(eu.Z,{dividers:!0,children:n&&(0,i.jsxs)(o.Z,{children:[(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"Purpose:"})," ","look_around"===n.purpose?"Look Around":"Use Service"]}),(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"User Type:"})," ","crew"===n.userType?"Crew":"Public"]}),n.teamName&&(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"Team:"})," ",n.teamName]}),(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"Visit Time:"})," ",new Date(n.visitTimestamp).toLocaleString()]}),(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"Festival ID:"})," ",n.festivalId]}),n.siteLocationId&&(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"Site Location:"})," ",n.siteLocationId]})]})}),(0,i.jsx)(ej.Z,{children:(0,i.jsx)(eb.Z,{onClick:()=>x(!1),variant:"contained",children:"Close"})})]})]})};var eG=r(17047);let eM=e=>{let{visits:t,selectedItems:r,onSelectAll:l,onSelectItem:n,onDelete:a}=e,[d,c]=(0,s.useState)(!1),[u,h]=(0,s.useState)(null),x=e=>{h(e),c(!0)},m=()=>{c(!1)},j=[{field:"visitTimestamp",headerName:"Visit Time",flex:1,sortable:!0,filterable:!0,type:"dateTime",valueGetter:e=>e&&e.row&&"object"==typeof e.row&&"visitTimestamp"in e.row&&e.row.visitTimestamp?new Date(e.row.visitTimestamp):null,renderCell:e=>{if(!e||!e.row||"object"!=typeof e.row||!("visitTimestamp"in e.row))return"N/A";let t=e.row;return t.visitTimestamp?new Date(t.visitTimestamp).toLocaleString():"N/A"}},{field:"purpose",headerName:"Purpose",width:150,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.purpose||"n/a",renderCell:e=>{let t=e.row,r="look_around"===t.purpose?"Look Around":"Use Service";return(0,i.jsx)(eG.Z,{label:r,color:"use_service"===t.purpose?"primary":"default",variant:"outlined",size:"small"})}},{field:"userType",headerName:"User Type",width:120,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.userType||"n/a",renderCell:e=>{let t=e.row,r="crew"===t.userType?"Crew":"Public";return(0,i.jsx)(eG.Z,{label:r,color:"crew"===t.userType?"secondary":"default",variant:"filled",size:"small"})}},{field:"teamName",headerName:"Team",flex:1,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.teamName||"n/a",renderCell:e=>{let t=e.row;return t.teamName||("crew"===t.userType?"No Team":"N/A")}},{field:"festivalId",headerName:"Festival",width:120,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.festivalId||"n/a",renderCell:e=>{let t=e.row;return t.festivalId?t.festivalId.split("_").pop()||t.festivalId:"N/A"}},{field:"siteLocationId",headerName:"Site",width:100,sortable:!0,filterable:!0,valueGetter:e=>e&&e.row&&"object"==typeof e.row&&e.row.siteLocationId||"n/a",renderCell:e=>e.row.siteLocationId||"N/A"}];return(0,i.jsxs)(p.Z,{elevation:3,sx:{bgcolor:"background.paper",backdropFilter:"blur(8px)",borderRadius:2,p:3},children:[(0,i.jsxs)(o.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center",mb:2},children:[(0,i.jsx)(b.Z,{variant:"h6",color:"primary",children:"Raw Data"}),r.length>0&&(0,i.jsxs)(eb.Z,{variant:"contained",color:"error",onClick:a,children:["Delete Selected (",r.length,")"]})]}),(0,i.jsx)("div",{style:{height:400,width:"100%"},children:(0,i.jsx)(ef._,{rows:t||[],columns:j,getRowId:e=>e._id,checkboxSelection:!0,disableRowSelectionOnClick:!0,rowSelectionModel:{type:"include",ids:new Set(r)},onRowSelectionModelChange:e=>{let t=Array.isArray(e)?e.map(e=>e.toString()):[];t.forEach(e=>{r.includes(e)||n(e)}),r.forEach(e=>{t.includes(e)||n(e)})},density:"compact",slots:{toolbar:ew.n},slotProps:{toolbar:{showQuickFilter:!0,quickFilterProps:{debounceMs:300}}},initialState:{sorting:{sortModel:[{field:"visitTimestamp",sort:"desc"}]}},onRowClick:e=>x(e.row),sx:{"& .MuiDataGrid-cell":{fontSize:"0.875rem"},"& .MuiDataGrid-toolbarContainer":{padding:"8px 24px"},"& .MuiDataGrid-toolbar":{"& .MuiFormControl-root":{width:"100%",maxWidth:"300px"}},"& .MuiDataGrid-row":{cursor:"pointer"},border:"none",bgcolor:"background.paper",backdropFilter:"blur(8px)"}})}),(0,i.jsxs)(ed.Z,{open:d,onClose:m,maxWidth:"sm",fullWidth:!0,PaperProps:{sx:{borderRadius:2,bgcolor:"background.paper"}},children:[(0,i.jsx)(ec.Z,{children:(0,i.jsx)(b.Z,{variant:"h5",fontWeight:"bold",children:"Sensory Hub Visit Details"})}),(0,i.jsx)(eu.Z,{dividers:!0,children:u&&(0,i.jsxs)(o.Z,{children:[(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"Purpose:"})," ","look_around"===u.purpose?"Look Around":"Use Service"]}),(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"User Type:"})," ","crew"===u.userType?"Crew":"Public"]}),u.teamName&&(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"Team:"})," ",u.teamName]}),(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"Visit Time:"})," ",new Date(u.visitTimestamp).toLocaleString()]}),(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"Festival ID:"})," ",u.festivalId]}),u.siteLocationId&&(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"Site Location:"})," ",u.siteLocationId]}),(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"Record ID:"})," ",u._id]}),u.createdAt&&(0,i.jsxs)(b.Z,{variant:"subtitle1",gutterBottom:!0,children:[(0,i.jsx)("strong",{children:"Created:"})," ",new Date(u.createdAt).toLocaleString()]})]})}),(0,i.jsx)(ej.Z,{children:(0,i.jsx)(eb.Z,{onClick:m,variant:"contained",children:"Close"})})]})]})},eW=e=>{let{visits:t,selectedItems:r,onSelectAll:s,onSelectItem:o,onDelete:l}=e;return(0,i.jsxs)(J.Z,{container:!0,spacing:3,children:[(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsx)(eL,{visits:t})}),(0,i.jsx)(J.Z,{size:{xs:12},children:(0,i.jsx)(eM,{visits:t,selectedItems:r,onSelectAll:s,onSelectItem:o,onDelete:l})})]})},eO=()=>{let{activeFestival:e}=(0,n.C)(),[t,r]=(0,s.useState)("all"),[d,c]=(0,s.useState)("admissions"),[u,h]=(0,s.useState)([]),{data:m,loading:p,error:j}=x(e?._id,t);if(!e)return(0,i.jsx)(o.Z,{display:"flex",justifyContent:"center",alignItems:"center",minHeight:"200px",children:(0,i.jsx)(l.Z,{severity:"info",children:"Please select a festival to view reports."})});let b=e=>{e&&(u.length===e.length?h([]):h(e.map(e=>e._id).filter(Boolean)))},y=e=>{h(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},g=async()=>{try{switch(d){case"admissions":await Promise.all(u.map(e=>a.R.deleteAdmission(e)));break;case"frontOfHouse":await Promise.all(u.map(e=>a.R.deleteItemCount(e)));break;case"lostProperty":await Promise.all(u.map(e=>a.R.deleteLostPropertyItem(e)));break;case"sensoryHub":await Promise.all(u.map(e=>a.R.deleteSensoryHubVisit(e)))}h([])}catch(e){}},f=async e=>{try{let t={...e,type:"admission",documentType:"admission",syncStatus:"sync_pending",AdditionalNotes:e.AdditionalNotes||[],History:e.History||[],timestamp:e.timestamp||new Date().toISOString(),createdAt:e.createdAt||e.timestamp||new Date().toISOString(),updatedAt:new Date().toISOString()};await a.R.updateAdmission(t)}catch(e){}};return(0,i.jsx)(Q,{festival:e,selectedDay:t,onDayChange:r,activeTab:d,onTabChange:c,loading:p,error:j,data:m,children:(()=>{if(!m)return null;switch(d){case"admissions":return(0,i.jsx)(eC,{admissions:m.admissions||[],selectedItems:u,onSelectAll:b,onSelectItem:y,onDelete:g,onUpdateAdmission:f});case"frontOfHouse":return(0,i.jsx)(eA,{itemCounts:m.itemCounts||[],selectedItems:u,onSelectAll:b,onSelectItem:y,onDelete:g});case"lostProperty":return(0,i.jsx)(eT,{items:m.lostPropertyItems||[],selectedItems:u,onSelectAll:b,onSelectItem:y,onDelete:g});case"sensoryHub":return(0,i.jsx)(eW,{visits:m.sensoryHubVisits||[],selectedItems:u,onSelectAll:b,onSelectItem:y,onDelete:g});default:return null}})()})}},77761:function(){}}]);