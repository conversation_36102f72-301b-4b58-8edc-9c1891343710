"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["880"],{63709:function(e,t,r){r.d(t,{AC:()=>o,LU:()=>u,iG:()=>d,kT:()=>n,ol:()=>c});var l=r(67294),s=r(83502),a=r(5214);function i(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{activeFestival:r}=(0,a.C)(),{enabled:s=!0,onError:i}=t,[n,o]=(0,l.useState)({data:null,isLoading:!1,error:null,isFromCache:!1}),d=(0,l.useCallback)(async()=>{if(!s||!r)return void o(e=>({...e,data:null,isLoading:!1}));o(e=>({...e,isLoading:!0,error:null}));try{Date.now();let t=await e();Date.now(),o({data:t,isLoading:!1,error:null,isFromCache:!1})}catch(e){o({data:null,isLoading:!1,error:e instanceof Error?e.message:"Unknown error",isFromCache:!1}),i&&e instanceof Error&&i(e)}},[r?._id,s]),c=(0,l.useCallback)(async()=>{await d()},[d]);return(0,l.useEffect)(()=>{d()},[d]),{...n,refetch:c}}let n=function(e){let{activeFestival:t}=(0,a.C)();return i((0,l.useCallback)(()=>t?s.R.getAdmissionsByFestival(t._id):Promise.resolve([]),[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})},o=function(e){let{activeFestival:t}=(0,a.C)();return i((0,l.useCallback)(()=>t?s.R.getItemCountsByFestival(t._id):Promise.resolve([]),[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})},d=function(e){let{activeFestival:t}=(0,a.C)();return i((0,l.useCallback)(async()=>t?s.R.getLostPropertyItems(t._id):[],[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})},c=function(e){let{activeFestival:t}=(0,a.C)();return i((0,l.useCallback)(()=>t?s.R.getKnowledgeBaseItems(t._id):Promise.resolve([]),[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})},u=function(e){let{activeFestival:t}=(0,a.C)();return i((0,l.useCallback)(()=>t?s.R.getKnowledgeBaseCategories(t._id):Promise.resolve([]),[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})}},24856:function(e,t,r){r.r(t),r.d(t,{KnowledgeBasePage:()=>q});var l=r(85893),s=r(67294),a=r(33991),i=r(61215),n=r(39467),o=r(98106),d=r(89126),c=r(54757),u=r(64889),x=r(30925),h=r(81839),m=r(56099),g=r(73892),p=r(1156),f=r(48346),j=r(17047),y=r(73876),b=r(19410),Z=r(85714),C=r(7230),v=r(21143),w=r(89276),S=r(13319),k=r(60488),_=r(38953),W=r(11419),I=r(14540),L=r(97454),R=r(10446),A=r(68450),B=r(83502),F=r(5214),P=r(37231),T=r(63709);let z=e=>{let{open:t,onClose:r,onSubmit:i,initialCategories:n,editItem:o,festivalId:d,existingItems:c}=e,[k,_]=(0,s.useState)(""),[W,I]=(0,s.useState)(""),[L,R]=(0,s.useState)(""),[A,B]=(0,s.useState)(""),[F,P]=(0,s.useState)(""),[T,z]=(0,s.useState)(""),[N,E]=(0,s.useState)(""),[q,K]=(0,s.useState)(""),[U,D]=(0,s.useState)([]),[O,G]=(0,s.useState)([]),[V,H]=(0,s.useState)(!1),[M,Y]=(0,s.useState)(!1),[$,J]=(0,s.useState)(!1),[Q,X]=(0,s.useState)(""),[ee,et]=(0,s.useState)(""),[er,el]=(0,s.useState)(""),[es,ea]=(0,s.useState)("");(0,s.useEffect)(()=>{D(n),o?(_(o.title),I(o.url),R(o.phoneNumber||""),B(o.description),P(o.category),z(o.subcategory||""),J(o.showForAllFestivals||!1)):(_(""),I(""),R(""),B(""),P(n.length>0?n[0]:""),z(""),J(!1)),Y(!1)},[o,n]),(0,s.useEffect)(()=>{F?G(c.filter(e=>e.category===F&&e.subcategory).map(e=>e.subcategory).filter((e,t,r)=>r.indexOf(e)===t)):G([])},[F,c]);let ei=()=>{let e=!0;return k.trim()?X(""):(X("Title is required"),e=!1),W.trim()?et(""):(et("URL is required"),e=!1),L.trim()?/^[+]?[(]?[0-9]{1,4}[)]?[-\s.]?[0-9]{1,4}[-\s.]?[0-9]{1,9}$/.test(L.trim())?ea(""):(ea("Please enter a valid phone number"),e=!1):ea(""),V?N.trim()?el(""):(el("Category is required"),e=!1):F?el(""):(el("Category is required"),e=!1),M&&!q.trim()&&(e=!1),e};return(0,l.jsxs)(u.Z,{open:t,onClose:r,maxWidth:"sm",fullWidth:!0,children:[(0,l.jsx)(x.Z,{children:o?"Edit Resource":"Add New Resource"}),(0,l.jsx)(h.Z,{children:(0,l.jsxs)(a.Z,{component:"form",sx:{display:"flex",flexDirection:"column",gap:2,mt:1},children:[(0,l.jsx)(m.Z,{label:"Title",fullWidth:!0,required:!0,value:k,onChange:e=>_(e.target.value),error:!!Q,helperText:Q}),(0,l.jsx)(m.Z,{label:"URL",fullWidth:!0,required:!0,value:W,onChange:e=>I(e.target.value),placeholder:"e.g., example.com",error:!!ee,helperText:ee||"The URL where this resource can be found"}),(0,l.jsx)(m.Z,{label:"Phone Number",fullWidth:!0,value:L,onChange:e=>R(e.target.value),placeholder:"e.g., +44 1234 567890",error:!!es,helperText:es||"A contact phone number (optional)"}),(0,l.jsx)(m.Z,{label:"Description",fullWidth:!0,multiline:!0,rows:3,value:A,onChange:e=>B(e.target.value),placeholder:"What is this resource about? Why would someone use it?"}),V?(0,l.jsx)(m.Z,{label:"New Category",fullWidth:!0,required:!0,value:N,onChange:e=>E(e.target.value),error:!!er,helperText:er||"Create a new category for this resource"}):(0,l.jsxs)(g.Z,{fullWidth:!0,required:!0,error:!!er,children:[(0,l.jsx)(p.Z,{children:"Category"}),(0,l.jsx)(f.Z,{value:F,onChange:e=>P(e.target.value),renderValue:e=>(0,l.jsx)(a.Z,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:(0,l.jsx)(j.Z,{label:e})}),children:U.map(e=>(0,l.jsx)(y.Z,{value:e,children:(0,l.jsx)(b.Z,{primary:e})},e))}),er&&(0,l.jsx)(Z.Z,{children:er})]}),(0,l.jsx)(C.Z,{size:"small",onClick:()=>{H(!V),V?E(""):P("")},sx:{alignSelf:"flex-start"},children:V?"Select Existing Category":"Create New Category"}),M?(0,l.jsx)(m.Z,{label:"New Subcategory",fullWidth:!0,value:q,onChange:e=>K(e.target.value),helperText:"Create a new subcategory (optional)"}):(0,l.jsxs)(g.Z,{fullWidth:!0,children:[(0,l.jsx)(p.Z,{children:"Subcategory"}),(0,l.jsxs)(f.Z,{value:T,onChange:e=>z(e.target.value),renderValue:e=>(0,l.jsx)(a.Z,{sx:{display:"flex",flexWrap:"wrap",gap:.5},children:(0,l.jsx)(j.Z,{label:e})}),children:[(0,l.jsx)(y.Z,{value:"",children:(0,l.jsx)(b.Z,{primary:"None (optional)"})}),O.map(e=>(0,l.jsx)(y.Z,{value:e,children:(0,l.jsx)(b.Z,{primary:e})},e))]})]}),(0,l.jsx)(C.Z,{size:"small",onClick:()=>Y(!M),sx:{alignSelf:"flex-start"},children:M?"Use Simple Subcategory":"Create New Subcategory"}),(0,l.jsx)(a.Z,{sx:{mt:2,mb:1},children:(0,l.jsx)(v.Z,{control:(0,l.jsx)(w.Z,{checked:$,onChange:e=>J(e.target.checked),color:"primary"}),label:"Show this resource for all festivals (not just this one)"})})]})}),(0,l.jsxs)(S.Z,{children:[(0,l.jsx)(C.Z,{onClick:r,children:"Cancel"}),(0,l.jsx)(C.Z,{onClick:()=>{if(!ei())return;let e=V?N.trim():F,t=M?q.trim():T,r={title:k.trim(),url:W.trim(),phoneNumber:L.trim()||void 0,description:A.trim()||"",category:e,subcategory:t||void 0,festivalId:d,showForAllFestivals:$,type:"knowledgeBase",documentType:"knowledgeBase",syncStatus:"sync_pending"};o?i({...r,_id:o._id,_rev:o._rev,createdAt:o.createdAt}):i(r)},variant:"contained",color:"primary",children:o?"Update":"Add"})]})]})},N=()=>{let{activeFestival:e,loading:t}=(0,F.C)(),{hasAccess:r,userEmail:o}=(0,P.a)(),[u,x]=(0,s.useState)(!1),[h,m]=(0,s.useState)(null),[g,p]=(0,s.useState)(!1),f=r("knowledge-base-edit"),j=(0,T.ol)({onError:e=>void 0}),y=(0,T.LU)({onError:e=>void 0}),b=j.data||[],Z=y.data||["Substance Info","Mental Health","Support Contacts"],v=j.isLoading||y.isLoading,w=j.error||y.error;j.isFromCache||y.isFromCache;let S=async()=>{await Promise.all([j.refetch(),y.refetch()])},N=e=>{e.startsWith("http://")||e.startsWith("https://")||(e="https://"+e),window.open(e,"_blank","noopener,noreferrer")},E=e=>{m(e),x(!0)},q=async e=>{if(window.confirm("Are you sure you want to delete this item?"))try{await B.R.deleteKnowledgeBaseItem(e),await S()}catch(e){}},K=async e=>{try{"_id"in e?await B.R.updateKnowledgeBaseItem(e):await B.R.addKnowledgeBaseItem(e),await S(),x(!1),m(null)}catch(e){}};if(!e)return(0,l.jsx)(a.Z,{sx:{p:4,textAlign:"center"},children:(0,l.jsx)(c.Z,{color:"text.secondary",children:"Please select a festival first to access the Knowledge Base."})});if(v)return(0,l.jsx)(a.Z,{sx:{display:"flex",justifyContent:"center",p:3},children:(0,l.jsx)(i.Z,{})});if(w)return(0,l.jsx)(a.Z,{sx:{p:3},children:(0,l.jsx)(n.Z,{severity:"error",children:w})});let U=e=>b.filter(t=>t.category===e),D=e=>U(e).map(e=>e.subcategory||"").filter((e,t,r)=>""!==e&&r.indexOf(e)===t),O=(e,t)=>b.filter(r=>r.category===e&&r.subcategory===t),G=e=>b.filter(t=>t.category===e&&(!t.subcategory||""===t.subcategory)),V=e=>{let{item:t}=e;return(0,l.jsxs)(d.Z,{elevation:2,sx:{p:2,height:"100%",display:"flex",flexDirection:"column",cursor:"pointer",transition:"all 0.2s ease-in-out","&:hover":{transform:"translateY(-5px)",boxShadow:6},position:"relative",borderLeft:t.showForAllFestivals?"4px solid":"none",borderColor:t.showForAllFestivals?"secondary.main":"transparent"},onClick:()=>N(t.url),children:[f&&(0,l.jsxs)(a.Z,{sx:{position:"absolute",top:8,right:8,display:"flex",gap:.5},children:[(0,l.jsx)(k.Z,{title:"Edit",children:(0,l.jsx)(_.Z,{size:"small",onClick:e=>{e.stopPropagation(),E(t)},children:(0,l.jsx)(I.Z,{fontSize:"small"})})}),(0,l.jsx)(k.Z,{title:"Delete",children:(0,l.jsx)(_.Z,{size:"small",onClick:e=>{e.stopPropagation(),q(t._id)},children:(0,l.jsx)(L.Z,{fontSize:"small"})})})]}),(0,l.jsx)(a.Z,{sx:{mt:1},children:(0,l.jsxs)(c.Z,{variant:"h6",sx:{mb:1,fontSize:"1.1rem",display:"flex",alignItems:"center",gap:1},children:[(0,l.jsx)(R.Z,{fontSize:"small"}),t.title]})}),(0,l.jsx)(c.Z,{variant:"body2",sx:{display:"flex",alignItems:"center",gap:.5,mb:1,color:"primary.main"},children:t.phoneNumber?(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(A.Z,{fontSize:"small"}),t.phoneNumber]}):(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(R.Z,{fontSize:"small"}),t.url]})}),(0,l.jsx)(c.Z,{variant:"body2",color:"text.secondary",sx:{flexGrow:1,overflow:"hidden",textOverflow:"ellipsis",display:"-webkit-box",WebkitLineClamp:3,WebkitBoxOrient:"vertical"},children:t.description})]})};return(0,l.jsxs)(a.Z,{sx:{p:{xs:2,sm:3}},children:[(0,l.jsxs)(a.Z,{sx:{display:"flex",justifyContent:"space-between",mb:3,alignItems:"center"},children:[o&&(0,l.jsxs)(c.Z,{variant:"body2",color:"text.secondary",children:["Logged in as: ",o]}),f&&(0,l.jsx)(C.Z,{variant:"contained",color:"primary",startIcon:(0,l.jsx)(W.Z,{}),onClick:()=>{m(null),x(!0)},children:"Add Resource"})]}),0===Z.length?(0,l.jsx)(n.Z,{severity:"info",sx:{mt:2},children:"No resource categories yet. Add your first resource to get started."}):(0,l.jsx)(a.Z,{sx:{mb:4},children:Z.map(e=>{let t=U(e),r=D(e),s=G(e);return(0,l.jsxs)(a.Z,{sx:{mb:4},children:[(0,l.jsxs)(c.Z,{variant:"h5",sx:{mb:2,borderBottom:"2px solid",borderColor:"primary.main",paddingBottom:1},children:[e," (",t.length,")"]}),s.length>0&&(0,l.jsx)(a.Z,{sx:{mb:3},children:(0,l.jsx)(a.Z,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",sm:"repeat(2, 1fr)",md:"repeat(3, 1fr)",lg:"repeat(4, 1fr)"},gap:2},children:s.map(e=>(0,l.jsx)(V,{item:e},e._id))})}),r.map(t=>{let r=O(e,t);return r.length>0?(0,l.jsxs)(a.Z,{sx:{mb:3},children:[(0,l.jsxs)(c.Z,{variant:"h6",sx:{mb:2,ml:2,color:"secondary.main",display:"flex",alignItems:"center"},children:[(0,l.jsx)(a.Z,{component:"span",sx:{width:8,height:8,bgcolor:"secondary.main",borderRadius:"50%",display:"inline-block",mr:1}}),t," (",r.length,")"]}),(0,l.jsx)(a.Z,{sx:{display:"grid",gridTemplateColumns:{xs:"1fr",sm:"repeat(2, 1fr)",md:"repeat(3, 1fr)",lg:"repeat(4, 1fr)"},gap:2},children:r.map(e=>(0,l.jsx)(V,{item:e},e._id))})]},t):null})]},e)})}),u&&(0,l.jsx)(z,{open:u,onClose:()=>{x(!1),m(null)},onSubmit:K,initialCategories:Z,editItem:h,festivalId:e?._id||"",existingItems:b})]})};var E=r(18422);let q=()=>{let{activeFestival:e}=(0,F.C)(),[t,r]=(0,s.useState)(!0),[u,x]=(0,s.useState)(null);return((0,s.useEffect)(()=>{if(!e){x("Please select a festival first"),r(!1);return}r(!1)},[e]),t)?(0,l.jsx)(a.Z,{sx:{display:"flex",justifyContent:"center",p:3},children:(0,l.jsx)(i.Z,{})}):u?(0,l.jsx)(a.Z,{sx:{p:3},children:(0,l.jsx)(n.Z,{severity:"error",children:u})}):(0,l.jsxs)(o.Z,{maxWidth:"xl",sx:{py:3},children:[(0,l.jsx)(d.Z,{elevation:0,sx:{p:3,mb:4,bgcolor:"primary.light",color:"primary.contrastText",borderRadius:2},children:(0,l.jsxs)(a.Z,{sx:{display:"flex",alignItems:"center",gap:2},children:[(0,l.jsx)(E.Z,{fontSize:"large"}),(0,l.jsxs)(a.Z,{children:[(0,l.jsx)(c.Z,{variant:"h4",sx:{fontWeight:"bold"},children:"Knowledge Base"}),(0,l.jsx)(c.Z,{variant:"subtitle1",children:"Access important resources organized by category and subcategory"})]})]})}),e&&(0,l.jsx)(N,{})]})}}}]);