"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["435"],{86755:function(e,t,i){i.r(t),i.d(t,{LostPropertyPage:()=>B});var r=i(85893),a=i(67294),n=i(83502),l=i(5214),s=i(63709),o=i(33991),d=i(54757),c=i(98106),u=i(13400),h=i(17047),x=i(7230),m=i(39467),p=i(12550),g=i(89126),y=i(36762),j=i(21143),f=i(54791),b=i(56099),Z=i(73892),v=i(1156),w=i(48346),C=i(73876),S=i(14094),D=i(32153),k=i(89717),F=i(3840),L=i(10857),I=i(58308),R=i(82400),P=i(86202);let _=["Phone","Passport","Keys","Bag","Sanity","Driving License","Medication","ID/Cards","Glasses","Camera","Tickets","Cuddly Toy","Watch/Jewellery","Headphone","Wallet","Other"],B=()=>{let{activeFestival:e}=(0,l.C)(),[t,i]=(0,a.useState)(null),[B,T]=(0,a.useState)(""),[W,z]=(0,a.useState)(new Date().toISOString().split("T")[0]),[G,M]=(0,a.useState)(new Date().toLocaleTimeString("en-GB",{hour:"2-digit",minute:"2-digit"})),[E,U]=(0,a.useState)(""),[N,O]=(0,a.useState)(""),[A,q]=(0,a.useState)(""),[K,Q]=(0,a.useState)(null),[Y,$]=(0,a.useState)(null),[H,J]=(0,a.useState)(""),[V,X]=(0,a.useState)(!1),[ee,et]=(0,a.useState)(null),ei=(0,s.iG)({onError:e=>void 0}),er=ei.data||[],ea=ei.isLoading,en=ei.error,el=ei.isFromCache,es=async()=>{await ei.refetch()};(0,a.useEffect)(()=>{e&&eh()},[e?._id]);let eo=e=>{try{i(e.category),T(e.quickDescription);let t=new Date(e.timeFound);z(t.toISOString().split("T")[0]),M(t.toLocaleTimeString("en-GB",{hour:"2-digit",minute:"2-digit"})),U(e.foundBy),O(e.whereFound),q(e.description),Q(e.itemReturned||""),$(e)}catch(e){}},ed=async()=>{if(Y)try{let e=new Date().toISOString(),t={...Y,itemReturned:e,status:"claimed"};await n.R.updateLostPropertyItem(t),await es(),Q(e)}catch(e){}},ec=async()=>{if(Y)try{let e={...Y,itemReturned:void 0,status:"unclaimed"};await n.R.updateLostPropertyItem(e),await es(),Q(null)}catch(e){}},eu=async i=>{if(i.preventDefault(),t&&e)try{let i=new Date(`${W}T${G}`).toISOString(),r={type:"lost_property",documentType:"lost_property",category:t,quickDescription:B,timeFound:i,foundBy:E,whereFound:N,description:A,itemReturned:void 0,festivalId:e?._id||"",syncStatus:"sync_pending",status:"unclaimed"};Y?await n.R.updateLostPropertyItem({...Y,...r}):await n.R.addLostPropertyItem(r),eh(),await es();try{await n.R.manualSync()}catch(e){}}catch(e){}},eh=()=>{i(null),T(""),z(new Date().toISOString().split("T")[0]),M(new Date().toLocaleTimeString("en-GB",{hour:"2-digit",minute:"2-digit"})),U(""),O(""),q(""),Q(null),$(null)},ex=e=>{et(e),X(!0)},em=(0,a.useMemo)(()=>{if(!H.trim())return er;let e=H.toLowerCase().trim();return er.filter(t=>[t.category,t.quickDescription,t.description,t.whereFound,t.foundBy].map(e=>(e||"").toLowerCase()).some(t=>t.includes(e)))},[er,H]);if(!e)return(0,r.jsx)(o.Z,{sx:{p:4,textAlign:"center"},children:(0,r.jsx)(d.Z,{color:"text.secondary",children:"Please select a festival to manage lost property items."})});let ep=[{field:"category",headerName:"Category",flex:1,sortable:!0,filterable:!0},{field:"quickDescription",headerName:"Description",flex:2,sortable:!0,filterable:!0},{field:"timeFound",headerName:"Time Found",flex:1,sortable:!0,filterable:!0,type:"dateTime",valueGetter:e=>e&&e.row&&"object"==typeof e.row&&"timeFound"in e.row&&e.row.timeFound?new Date(e.row.timeFound):null,renderCell:e=>e&&e.row&&"object"==typeof e.row&&"timeFound"in e.row&&e.row.timeFound?new Date(e.row.timeFound).toLocaleString():"N/A"},{field:"foundBy",headerName:"Found By",flex:1,sortable:!0,filterable:!0},{field:"whereFound",headerName:"Where Found",flex:1,sortable:!0,filterable:!0},{field:"status",headerName:"Status",flex:1,sortable:!0,filterable:!0,renderCell:e=>"claimed"===e.row.status?"Claimed":"Unclaimed"}];return(0,r.jsxs)(c.Z,{maxWidth:!1,sx:{p:3},children:[(0,r.jsxs)(u.Z,{spacing:4,children:[(0,r.jsxs)(o.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"center"},children:[(0,r.jsxs)(o.Z,{children:[(0,r.jsxs)(d.Z,{variant:"h4",sx:{mb:1},children:["Lost Property - ",e.name]}),(0,r.jsxs)(o.Z,{sx:{display:"flex",alignItems:"center",gap:1},children:[el&&(0,r.jsx)(h.Z,{icon:(0,r.jsx)(L.Z,{}),label:"Cached Data",size:"small",color:"info",variant:"outlined"}),(0,r.jsxs)(d.Z,{variant:"body2",color:"text.secondary",children:[new Date(e.startDate).toLocaleDateString()," - ",new Date(e.endDate).toLocaleDateString()]})]})]}),(0,r.jsx)(x.Z,{variant:"outlined",onClick:es,disabled:ea,startIcon:(0,r.jsx)(L.Z,{}),children:"Refresh"})]}),en&&(0,r.jsx)(m.Z,{severity:"error",children:en}),(0,r.jsxs)(p.Z,{container:!0,spacing:3,children:[(0,r.jsx)(p.Z,{size:{xs:12,md:8},children:(0,r.jsx)(g.Z,{elevation:1,sx:{p:3},children:(0,r.jsx)("form",{onSubmit:eu,children:(0,r.jsxs)(u.Z,{spacing:3,children:[(0,r.jsxs)(o.Z,{children:[(0,r.jsx)(d.Z,{variant:"subtitle2",sx:{mb:1},children:"Category"}),(0,r.jsx)(y.Z,{row:!0,value:t||"",onChange:e=>i(e.target.value),children:(0,r.jsx)(p.Z,{container:!0,spacing:1,children:_.map(e=>(0,r.jsx)(p.Z,{size:{xs:6,sm:4},children:(0,r.jsx)(j.Z,{value:e,control:(0,r.jsx)(f.Z,{}),label:(0,r.jsx)(d.Z,{variant:"body2",children:e})})},e))})})]}),(0,r.jsxs)(p.Z,{container:!0,spacing:2,children:[(0,r.jsx)(p.Z,{size:{xs:12,md:6},children:(0,r.jsx)(b.Z,{fullWidth:!0,label:"Quick Description",value:B,onChange:e=>T(e.target.value),placeholder:"Quick Description of item"})}),(0,r.jsx)(p.Z,{size:{xs:6,md:3},children:(0,r.jsx)(b.Z,{fullWidth:!0,type:"date",label:"Date Found",value:W,onChange:e=>z(e.target.value),InputLabelProps:{shrink:!0}})}),(0,r.jsx)(p.Z,{size:{xs:6,md:3},children:(0,r.jsx)(b.Z,{fullWidth:!0,type:"time",label:"Time Found",value:G,onChange:e=>M(e.target.value),InputLabelProps:{shrink:!0}})}),(0,r.jsx)(p.Z,{size:{xs:12,md:6},children:(0,r.jsxs)(Z.Z,{fullWidth:!0,children:[(0,r.jsx)(v.Z,{children:"Found By"}),(0,r.jsxs)(w.Z,{value:E,onChange:e=>U(e.target.value),label:"Found By",children:[(0,r.jsx)(C.Z,{value:"",children:"Select who found it"}),(0,r.jsx)(C.Z,{value:"CREW",children:"CREW"}),(0,r.jsx)(C.Z,{value:"SECURITY",children:"SECURITY"}),(0,r.jsx)(C.Z,{value:"PUBLIC",children:"PUBLIC"})]})]})}),(0,r.jsx)(p.Z,{size:{xs:12,md:6},children:(0,r.jsx)(b.Z,{fullWidth:!0,label:"Where Found",value:N,onChange:e=>O(e.target.value),placeholder:"Where was the item found?"})})]}),(0,r.jsx)(b.Z,{fullWidth:!0,multiline:!0,rows:4,label:"Description",value:A,onChange:e=>q(e.target.value),placeholder:"Please add further details here"}),(0,r.jsx)(p.Z,{container:!0,spacing:2,children:(0,r.jsx)(p.Z,{size:{xs:12},children:(0,r.jsx)(b.Z,{fullWidth:!0,type:"datetime-local",label:"Item Returned",value:K||"",onChange:e=>Q(e.target.value||null),InputLabelProps:{shrink:!0}})})}),(0,r.jsxs)(o.Z,{sx:{display:"flex",gap:2,justifyContent:"space-between"},children:[(0,r.jsx)(x.Z,{type:"submit",variant:"contained",color:"primary",children:Y?"Update Item":"Add Item"}),Y&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsx)(x.Z,{variant:"outlined",color:"inherit",onClick:()=>{eh(),$(null)},children:"Cancel Edit"}),Y.itemReturned?(0,r.jsx)(x.Z,{variant:"contained",color:"warning",onClick:ec,children:"Unmark as Returned"}):(0,r.jsx)(x.Z,{variant:"contained",color:"success",onClick:ed,children:"Mark as Returned"})]})]})]})})})}),(0,r.jsx)(p.Z,{size:{xs:12,md:4},children:(0,r.jsxs)(g.Z,{elevation:1,sx:{p:3},children:[(0,r.jsxs)(o.Z,{sx:{mb:3},children:[(0,r.jsxs)(d.Z,{variant:"h6",sx:{mb:2},children:[em.length," Current items"]}),(0,r.jsx)(b.Z,{fullWidth:!0,placeholder:"Search items...",value:H,onChange:e=>J(e.target.value),InputProps:{startAdornment:(0,r.jsx)(S.Z,{position:"start",children:(0,r.jsx)(F.Z,{})})},sx:{mb:2}})]}),(0,r.jsx)(o.Z,{sx:{height:655,overflowY:"auto",pr:1},children:(0,r.jsx)(u.Z,{spacing:2,children:em.map(e=>(0,r.jsx)(D.Z,{variant:"outlined",onClick:()=>eo(e),sx:{cursor:"pointer","&:hover":{bgcolor:"action.hover"},...Y?._id===e._id&&{borderColor:"primary.main",bgcolor:"primary.lighter"}},children:(0,r.jsxs)(k.Z,{children:[(0,r.jsx)(o.Z,{sx:{display:"flex",justifyContent:"space-between",alignItems:"flex-start"},children:(0,r.jsxs)(u.Z,{spacing:.5,children:[(0,r.jsxs)(d.Z,{variant:"subtitle2",children:[e.category," - ",new Date(e.timeFound).toLocaleDateString("en-GB",{hour:"2-digit",minute:"2-digit"})]}),(0,r.jsx)(d.Z,{variant:"body2",color:"text.secondary",children:e.quickDescription}),(0,r.jsxs)(d.Z,{variant:"body2",color:"text.secondary",children:["Found by: ",e.foundBy]}),(0,r.jsxs)(d.Z,{variant:"body2",color:"text.secondary",children:["Location: ",e.whereFound]})]})}),e.itemReturned&&(0,r.jsxs)(d.Z,{variant:"body2",color:"success.main",sx:{mt:1},children:["Returned: ",new Date(e.itemReturned).toLocaleDateString()]})]})},e._id))})})]})})]}),(0,r.jsxs)(g.Z,{elevation:1,sx:{p:3,mt:4},children:[(0,r.jsxs)(o.Z,{sx:{mb:3},children:[(0,r.jsx)(d.Z,{variant:"h5",sx:{mb:1},children:"Lost Property Items Table"}),(0,r.jsx)(d.Z,{variant:"body2",color:"text.secondary",children:"Search, filter, and sort all lost property items"})]}),(0,r.jsx)("div",{style:{height:500,width:"100%"},children:(0,r.jsx)(I._,{rows:er,columns:ep,getRowId:e=>e._id,density:"standard",slots:{toolbar:R.n},slotProps:{toolbar:{showQuickFilter:!0,quickFilterProps:{debounceMs:300}}},initialState:{sorting:{sortModel:[{field:"timeFound",sort:"desc"}]},pagination:{paginationModel:{pageSize:10}}},pageSizeOptions:[10,25,50,100],onRowClick:e=>ex(e.row),sx:{"& .MuiDataGrid-cell":{fontSize:"0.875rem"},"& .MuiDataGrid-toolbarContainer":{padding:"8px 24px"},"& .MuiDataGrid-toolbar":{"& .MuiFormControl-root":{width:"100%",maxWidth:"300px"}},"& .MuiDataGrid-row":{cursor:"pointer"},border:"none"}})})]})]}),(0,r.jsx)(P.k,{open:V,onClose:()=>{X(!1)},record:ee,recordType:"lostProperty"})]})}},63709:function(e,t,i){i.d(t,{AC:()=>o,LU:()=>u,iG:()=>d,kT:()=>s,ol:()=>c});var r=i(67294),a=i(83502),n=i(5214);function l(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},{activeFestival:i}=(0,n.C)(),{enabled:a=!0,onError:l}=t,[s,o]=(0,r.useState)({data:null,isLoading:!1,error:null,isFromCache:!1}),d=(0,r.useCallback)(async()=>{if(!a||!i)return void o(e=>({...e,data:null,isLoading:!1}));o(e=>({...e,isLoading:!0,error:null}));try{Date.now();let t=await e();Date.now(),o({data:t,isLoading:!1,error:null,isFromCache:!1})}catch(e){o({data:null,isLoading:!1,error:e instanceof Error?e.message:"Unknown error",isFromCache:!1}),l&&e instanceof Error&&l(e)}},[i?._id,a]),c=(0,r.useCallback)(async()=>{await d()},[d]);return(0,r.useEffect)(()=>{d()},[d]),{...s,refetch:c}}let s=function(e){let{activeFestival:t}=(0,n.C)();return l((0,r.useCallback)(()=>t?a.R.getAdmissionsByFestival(t._id):Promise.resolve([]),[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})},o=function(e){let{activeFestival:t}=(0,n.C)();return l((0,r.useCallback)(()=>t?a.R.getItemCountsByFestival(t._id):Promise.resolve([]),[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})},d=function(e){let{activeFestival:t}=(0,n.C)();return l((0,r.useCallback)(async()=>t?a.R.getLostPropertyItems(t._id):[],[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})},c=function(e){let{activeFestival:t}=(0,n.C)();return l((0,r.useCallback)(()=>t?a.R.getKnowledgeBaseItems(t._id):Promise.resolve([]),[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})},u=function(e){let{activeFestival:t}=(0,n.C)();return l((0,r.useCallback)(()=>t?a.R.getKnowledgeBaseCategories(t._id):Promise.resolve([]),[t?._id]),{...e,enabled:!!t&&(e?.enabled??!0)})}}}]);