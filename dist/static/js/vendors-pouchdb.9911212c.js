"use strict";(self.webpackChunkithinc_welfare=self.webpackChunkithinc_welfare||[]).push([["64"],{83087:function(e,t,n){n.d(t,{Z:()=>ny});var r,i,o,s,a=n(8322),c=n.n(a),u=new Uint8Array(16);let l=/^(?:[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}|00000000-0000-0000-0000-000000000000)$/i;for(var f=[],d=0;d<256;++d)f.push((d+256).toString(16).substr(1));let h=function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:0,n=(f[e[t+0]]+f[e[t+1]]+f[e[t+2]]+f[e[t+3]]+"-"+f[e[t+4]]+f[e[t+5]]+"-"+f[e[t+6]]+f[e[t+7]]+"-"+f[e[t+8]]+f[e[t+9]]+"-"+f[e[t+10]]+f[e[t+11]]+f[e[t+12]]+f[e[t+13]]+f[e[t+14]]+f[e[t+15]]).toLowerCase();if(!("string"==typeof n&&l.test(n)))throw TypeError("Stringified UUID is invalid");return n},p=function(e,t,n){var r=(e=e||{}).random||(e.rng||function(){if(!i&&!(i="undefined"!=typeof crypto&&crypto.getRandomValues&&crypto.getRandomValues.bind(crypto)||"undefined"!=typeof msCrypto&&"function"==typeof msCrypto.getRandomValues&&msCrypto.getRandomValues.bind(msCrypto)))throw Error("crypto.getRandomValues() not supported. See https://github.com/uuidjs/uuid#getrandomvalues-not-supported");return i(u)})();if(r[6]=15&r[6]|64,r[8]=63&r[8]|128,t){n=n||0;for(var o=0;o<16;++o)t[n+o]=r[o];return t}return h(r)};var v=n(33684),y=n(17187),g=n.n(y),_=Function.prototype.toString,m=_.call(Object);function b(e){if(!e||"object"!=typeof e)return e;if(Array.isArray(e)){for(n=0,t=[],r=e.length;n<r;n++)t[n]=b(e[n]);return t}if(e instanceof Date&&isFinite(e))return e.toISOString();if("undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer||"undefined"!=typeof Blob&&e instanceof Blob)return e instanceof ArrayBuffer?e.slice(0):e.slice(0,e.size,e.type);if(!function(e){var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=t.constructor;return"function"==typeof n&&n instanceof n&&_.call(n)==m}(e))return e;for(n in t={},e)if(Object.prototype.hasOwnProperty.call(e,n)){var t,n,r,i=b(e[n]);void 0!==i&&(t[n]=i)}return t}function w(e){var t=!1;return function(...n){if(t)throw Error("once called more than once");t=!0,e.apply(this,n)}}function k(e){return function(...t){t=b(t);var n=this,r="function"==typeof t[t.length-1]&&t.pop(),i=new Promise(function(r,i){var o;try{var s=w(function(e,t){e?i(e):r(t)});t.push(s),(o=e.apply(n,t))&&"function"==typeof o.then&&r(o)}catch(e){i(e)}});return r&&i.then(function(e){r(null,e)},r),i}}function $(e,t){return k(function(...n){if(this._closed)return Promise.reject(Error("database is closed"));if(this._destroyed)return Promise.reject(Error("database is destroyed"));var r=this;if(r.constructor.listeners("debug").length){for(var i=["api",r.name,e],o=0;o<n.length-1;o++)i.push(n[o]);r.constructor.emit("debug",i);var s=n[n.length-1];n[n.length-1]=function(t,n){var i=["api",r.name,e];i=i.concat(t?["error",t]:["success",n]),r.constructor.emit("debug",i),s(t,n)}}return this.taskqueue.isReady?t.apply(this,n):new Promise(function(t,i){r.taskqueue.addTask(function(o){o?i(o):t(r[e].apply(r,n))})})})}function j(e,t){for(var n={},r=0,i=t.length;r<i;r++){var o=t[r];o in e&&(n[o]=e[o])}return n}function O(e){return e}function q(e){return[{ok:e}]}function x(e,t,n){var r=t.docs,i=new Map;r.forEach(function(e){i.has(e.id)?i.get(e.id).push(e):i.set(e.id,[e])});var o=i.size,s=0,a=Array(o),c=[];i.forEach(function(e,t){c.push(t)});var u=0;!function r(){if(!(u>=c.length)){var l,f,d=Math.min(u+6,c.length),h=c.slice(u,d);l=h,f=u,l.forEach(function(c,u){var l=f+u,d=i.get(c),h=j(d[0],["atts_since","attachments"]);h.open_revs=d.map(function(e){return e.rev}),h.open_revs=h.open_revs.filter(O);var p=O;0===h.open_revs.length&&(delete h.open_revs,p=q),["revs","attachments","binary","ajax","latest"].forEach(function(e){e in t&&(h[e]=t[e])}),e.get(c,h,function(e,t){var i,u;i=e?[{error:e}]:p(t),a[l]={id:c,docs:i},++s===o&&(u=[],a.forEach(function(e){e.docs.forEach(function(t){u.push({id:e.id,docs:[t]})})}),n(null,{results:u})),r()})}),u+=h.length}}()}try{localStorage.setItem("_pouch_check_localstorage",1),o=!!localStorage.getItem("_pouch_check_localstorage")}catch(e){o=!1}let S="function"==typeof queueMicrotask?queueMicrotask:function(e){Promise.resolve().then(e)};class A extends g(){constructor(){super(),this._listeners={},o&&addEventListener("storage",e=>{this.emit(e.key)})}addListener(e,t,n,r){if(!this._listeners[t]){var i=!1,o=this;this._listeners[t]=s,this.on(e,s)}function s(){if(o._listeners[t]){if(i){i="waiting";return}i=!0;var e=j(r,["style","include_docs","attachments","conflicts","filter","doc_ids","view","since","query_params","binary","return_docs"]);n.changes(e).on("change",function(e){e.seq>r.since&&!r.cancelled&&(r.since=e.seq,r.onChange(e))}).on("complete",function(){"waiting"===i&&S(s),i=!1}).on("error",function(){i=!1})}}}removeListener(e,t){t in this._listeners&&(super.removeListener(e,this._listeners[t]),delete this._listeners[t])}notifyLocalWindows(e){o&&(localStorage[e]="a"===localStorage[e]?"b":"a")}notify(e){this.emit(e),this.notifyLocalWindows(e)}}function E(e){if("undefined"!=typeof console&&"function"==typeof console[e]){var t=Array.prototype.slice.call(arguments,1);console[e].apply(console,t)}}function P(e){var t,n,r=0;return e||(r=2e3),t=e,n=r,t=parseInt(t,10)||0,(n=parseInt(n,10))!=n||n<=t?n=(t||1)<<1:n+=1,n>6e5&&(t=3e5,n=6e5),~~((n-t)*Math.random()+t)}function D(e,t){E("info","The above "+e+" is totally normal. "+t)}class C extends Error{constructor(e,t,n){super(),this.status=e,this.name=t,this.message=n,this.error=!0}toString(){return JSON.stringify({status:this.status,name:this.name,message:this.message,reason:this.reason})}}new C(401,"unauthorized","Name or password is incorrect.");var I=new C(400,"bad_request","Missing JSON list of 'docs'"),B=new C(404,"not_found","missing"),T=new C(409,"conflict","Document update conflict"),M=new C(400,"bad_request","_id field must contain a string"),L=new C(412,"missing_id","_id is required for puts"),R=new C(400,"bad_request","Only reserved document ids may start with underscore.");new C(412,"precondition_failed","Database not open");var N=new C(500,"unknown_error","Database encountered an unknown error"),F=new C(500,"badarg","Some query argument is invalid");new C(400,"invalid_request","Request was invalid");var J=new C(400,"query_parse_error","Some query parameter is invalid"),z=new C(500,"doc_validation","Bad special document member"),K=new C(400,"bad_request","Something wrong with the request"),U=new C(400,"bad_request","Document must be a JSON object");new C(404,"not_found","Database not found");var V=new C(500,"indexed_db_went_bad","unknown");new C(500,"web_sql_went_bad","unknown"),new C(500,"levelDB_went_went_bad","unknown"),new C(403,"forbidden","Forbidden by design doc validate_doc_update function");var Q=new C(400,"bad_request","Invalid rev format");new C(412,"file_exists","The database could not be created, the file already exists.");var G=new C(412,"missing_stub","A pre-existing attachment stub wasn't found");function W(e,t){function n(t){for(var n=Object.getOwnPropertyNames(e),r=0,i=n.length;r<i;r++)"function"!=typeof e[n[r]]&&(this[n[r]]=e[n[r]]);void 0===this.stack&&(this.stack=Error().stack),void 0!==t&&(this.reason=t)}return n.prototype=C.prototype,new n(t)}function X(e){if("object"!=typeof e){var t=e;(e=N).data=t}return"error"in e&&"conflict"===e.error&&(e.name="conflict",e.status=409),"name"in e||(e.name=e.error||"unknown"),"status"in e||(e.status=500),"message"in e||(e.message=e.message||e.reason),"stack"in e||(e.stack=Error().stack),e}function Y(e){var t={},n=e.filter&&"function"==typeof e.filter;return t.query=e.query_params,function(r){r.doc||(r.doc={});var i=n&&function(e,t,n){try{return!e(t,n)}catch(e){return W(K,"Filter function threw: "+e.toString())}}(e.filter,r.doc,t);if("object"==typeof i)return i;if(i)return!1;if(e.include_docs){if(!e.attachments)for(var o in r.doc._attachments)Object.prototype.hasOwnProperty.call(r.doc._attachments,o)&&(r.doc._attachments[o].stub=!0)}else delete r.doc;return!0}}function H(e){var t;if(e?"string"!=typeof e?t=W(M):/^_/.test(e)&&!/^_(design|local)/.test(e)&&(t=W(R)):t=W(L),t)throw t}function Z(e){return"boolean"==typeof e._remote?e._remote:"function"==typeof e.type&&(E("warn","db.type() is deprecated and will be removed in a future version of PouchDB"),"http"===e.type())}function ee(e){if(!e)return null;var t=e.split("/");return 2===t.length?t:1===t.length?[e,e]:null}function et(e){var t=ee(e);return t?t.join("/"):null}new C(413,"invalid_url","Provided URL is invalid");var en=["source","protocol","authority","userInfo","user","password","host","port","relative","path","directory","file","query","anchor"],er="queryKey",ei=/(?:^|&)([^&=]*)=?([^&]*)/g,eo=/^(?:(?![^:@]+:[^:@\/]*@)([^:\/?#.]+):)?(?:\/\/)?((?:(([^:@]*)(?::([^:@]*))?)?@)?([^:\/?#]*)(?::(\d*))?)(((\/(?:[^?#](?![^?#\/]*\.[^?#\/.]+(?:[?#]|$)))*\/?)?([^?#\/]*))(?:\?([^#]*))?(?:#(.*))?)/;function es(e){for(var t=eo.exec(e),n={},r=14;r--;){var i=en[r],o=t[r]||"",s=-1!==["user","password"].indexOf(i);n[i]=s?decodeURIComponent(o):o}return n[er]={},n[en[12]].replace(ei,function(e,t,r){t&&(n[er][t]=r)}),n}function ea(e,t){var n=[],r=[];for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&(n.push(i),r.push(t[i]));return n.push(e),Function.apply(null,n).apply(null,r)}function ec(e,t,n){return e.get(t).catch(function(e){if(404!==e.status)throw e;return{}}).then(function(r){var i,o,s,a=r._rev,c=n(r);return c?(c._id=t,c._rev=a,i=e,o=c,s=n,i.put(o).then(function(e){return{updated:!0,rev:e.rev}},function(e){if(409!==e.status)throw e;return ec(i,o._id,s)})):{updated:!1,rev:a}})}var eu=function(e){return atob(e)},el=function(e){return btoa(e)};function ef(e,t){e=e||[],t=t||{};try{return new Blob(e,t)}catch(i){if("TypeError"!==i.name)throw i;for(var n=new("undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof MSBlobBuilder?MSBlobBuilder:"undefined"!=typeof MozBlobBuilder?MozBlobBuilder:WebKitBlobBuilder),r=0;r<e.length;r+=1)n.append(e[r]);return n.getBlob(t.type)}}function ed(e,t){return ef([function(e){for(var t=e.length,n=new ArrayBuffer(t),r=new Uint8Array(n),i=0;i<t;i++)r[i]=e.charCodeAt(i);return n}(e)],{type:t})}function eh(e,t){var n=new FileReader,r="function"==typeof n.readAsBinaryString;n.onloadend=function(e){var n=e.target.result||"";if(r)return t(n);t(function(e){for(var t="",n=new Uint8Array(e),r=n.byteLength,i=0;i<r;i++)t+=String.fromCharCode(n[i]);return t}(n))},r?n.readAsBinaryString(e):n.readAsArrayBuffer(e)}function ep(e,t){eh(e,function(e){t(e)})}function ev(e,t){ep(e,function(e){t(el(e))})}var ey=self.setImmediate||self.setTimeout;function eg(e,t,n,r,i){var o,s,a;(n>0||r<t.size)&&(t=t.slice(n,r)),o=t,s=function(t){e.append(t),i()},(a=new FileReader).onloadend=function(e){s(e.target.result||new ArrayBuffer(0))},a.readAsArrayBuffer(o)}function e_(e,t,n,r,i){(n>0||r<t.length)&&(t=t.substring(n,r)),e.appendBinary(t),i()}function em(e,t){var n="string"==typeof e,r=n?e.length:e.size,i=Math.min(32768,r),o=Math.ceil(r/i),s=0,a=n?new(c()):new(c()).ArrayBuffer,u=n?e_:eg;function l(){ey(d)}function f(){t(el(a.end(!0))),a.destroy()}function d(){var t=s*i,n=t+i;++s<o?u(a,e,t,n,l):u(a,e,t,n,f)}d()}function eb(e,t){if(!t)return p().replace(/-/g,"").toLowerCase();var n,r=Object.assign({},e);return delete r._rev_tree,n=JSON.stringify(r),c().hash(n)}function ew(e){for(var t,n,r,i,o=e.rev_tree.slice();i=o.pop();){var s=i.ids,a=s[2],c=i.pos;if(a.length){for(var u=0,l=a.length;u<l;u++)o.push({pos:c+1,ids:a[u]});continue}var f=!!s[1].deleted,d=s[0];(!t||(r!==f?r:n!==c?n<c:t<d))&&(t=d,n=c,r=f)}return n+"-"+t}function ek(e,t){for(var n,r=e.slice();n=r.pop();)for(var i=n.pos,o=n.ids,s=o[2],a=t(0===s.length,i,o[0],n.ctx,o[1]),c=0,u=s.length;c<u;c++)r.push({pos:i+1,ids:s[c],ctx:a})}function e$(e,t){return e.pos-t.pos}function ej(e){var t=[];ek(e,function(e,n,r,i,o){e&&t.push({rev:n+"-"+r,pos:n,opts:o})}),t.sort(e$).reverse();for(var n=0,r=t.length;n<r;n++)delete t[n].pos;return t}function eO(e){for(var t=ew(e),n=ej(e.rev_tree),r=[],i=0,o=n.length;i<o;i++){var s=n[i];s.rev===t||s.opts.deleted||r.push(s.rev)}return r}function eq(e){for(var t,n=[],r=e.slice();t=r.pop();){var i=t.pos,o=t.ids,s=o[0],a=o[1],c=o[2],u=0===c.length,l=t.history?t.history.slice():[];l.push({id:s,opts:a}),u&&n.push({pos:i+1-l.length,ids:l});for(var f=0,d=c.length;f<d;f++)r.push({pos:i+1,ids:c[f],history:l})}return n.reverse()}function ex(e,t){return e.pos-t.pos}function eS(e,t){for(var n,r,i=t,o=e.length;i<o;i++){var s=e[i],a=[s.id,s.opts,[]];r?(r[2].push(a),r=a):n=r=a}return n}function eA(e,t){return e[0]<t[0]?-1:1}function eE(e,t){for(var n=[{tree1:e,tree2:t}],r=!1;n.length>0;){var i=n.pop(),o=i.tree1,s=i.tree2;(o[1].status||s[1].status)&&(o[1].status="available"===o[1].status||"available"===s[1].status?"available":"missing");for(var a=0;a<s[2].length;a++){if(!o[2][0]){r="new_leaf",o[2][0]=s[2][a];continue}for(var c=!1,u=0;u<o[2].length;u++)o[2][u][0]===s[2][a][0]&&(n.push({tree1:o[2][u],tree2:s[2][a]}),c=!0);c||(r="new_branch",function(e,t,n){var r=function(e,t,n){for(var r,i=0,o=e.length;i<o;)0>n(e[r=i+o>>>1],t)?i=r+1:o=r;return i}(e,t,n);e.splice(r,0,t)}(o[2],s[2][a],eA))}}return{conflicts:r,tree:e}}function eP(e,t,n){var r,i=[],o=!1,s=!1;if(!e.length)return{tree:[t],conflicts:"new_leaf"};for(var a=0,c=e.length;a<c;a++){var u=e[a];if(u.pos===t.pos&&u.ids[0]===t.ids[0])r=eE(u.ids,t.ids),i.push({pos:u.pos,ids:r.tree}),o=o||r.conflicts,s=!0;else if(!0!==n){var l=u.pos<t.pos?u:t,f=u.pos<t.pos?t:u,d=f.pos-l.pos,h=[],p=[];for(p.push({ids:l.ids,diff:d,parent:null,parentIdx:null});p.length>0;){var v=p.pop();if(0===v.diff){v.ids[0]===f.ids[0]&&h.push(v);continue}for(var y=v.ids[2],g=0,_=y.length;g<_;g++)p.push({ids:y[g],diff:v.diff-1,parent:v.ids,parentIdx:g})}var m=h[0];m?(r=eE(m.ids,f.ids),m.parent[2][m.parentIdx]=r.tree,i.push({pos:l.pos,ids:l.ids}),o=o||r.conflicts,s=!0):i.push(u)}else i.push(u)}return s||i.push(t),i.sort(ex),{tree:i,conflicts:o||"internal_node"}}function eD(e,t,n){var r=eP(e,t),i=function(e,t){for(var n=eq(e),r=0,i=n.length;r<i;r++){var o,s,a,c=n[r],u=c.ids;if(u.length>t){o||(o={});var l=u.length-t;a={pos:c.pos+l,ids:eS(u,l)};for(var f=0;f<l;f++)o[c.pos+f+"-"+u[f].id]=!0}else a={pos:c.pos,ids:eS(u,0)};s=s?eP(s,a,!0).tree:[a]}return o&&ek(s,function(e,t,n){delete o[t+"-"+n]}),{tree:s,revs:o?Object.keys(o):[]}}(r.tree,n);return{tree:i.tree,stemmedRevs:i.revs,conflicts:r.conflicts}}function eC(e){return e.ids}function eI(e,t){t||(t=ew(e));for(var n,r=t.substring(t.indexOf("-")+1),i=e.rev_tree.map(eC);n=i.pop();){if(n[0]===r)return!!n[1].deleted;i=i.concat(n[2])}}function eB(e){return"string"==typeof e&&e.startsWith("_local/")}function eT(e,t,n){var r=[{rev:e._rev}];"all_docs"===n.style&&(r=ej(t.rev_tree).map(function(e){return{rev:e.rev}}));var i={id:t.id,changes:r,doc:e};return eI(t,e._rev)&&(i.deleted=!0),n.conflicts&&(i.doc._conflicts=eO(t),i.doc._conflicts.length||delete i.doc._conflicts),i}class eM extends g(){constructor(e,t,n){super(),this.db=e;var r=(t=t?b(t):{}).complete=w((t,n)=>{if(t){var r;r="error",("listenerCount"in this?this.listenerCount(r):g().listenerCount(this,r))>0&&this.emit("error",t)}else this.emit("complete",n);this.removeAllListeners(),e.removeListener("destroyed",i)});n&&(this.on("complete",function(e){n(null,e)}),this.on("error",n));let i=()=>{this.cancel()};e.once("destroyed",i),t.onChange=(e,t,n)=>{if(!this.isCancelled)try{this.emit("change",e,t,n)}catch(e){E("error",'Error in .on("change", function):',e)}};var o=new Promise(function(e,n){t.complete=function(t,r){t?n(t):e(r)}});this.once("cancel",function(){e.removeListener("destroyed",i),t.complete(null,{status:"cancelled"})}),this.then=o.then.bind(o),this.catch=o.catch.bind(o),this.then(function(e){r(null,e)},r),e.taskqueue.isReady?this.validateChanges(t):e.taskqueue.addTask(e=>{e?t.complete(e):this.isCancelled?this.emit("cancel"):this.validateChanges(t)})}cancel(){this.isCancelled=!0,this.db.taskqueue.isReady&&this.emit("cancel")}validateChanges(e){var t=e.complete;eQ._changesFilterPlugin?eQ._changesFilterPlugin.validate(e,n=>{if(n)return t(n);this.doChanges(e)}):this.doChanges(e)}doChanges(e){var t=e.complete;if("live"in(e=b(e))&&!("continuous"in e)&&(e.continuous=e.live),e.processChange=eT,"latest"===e.since&&(e.since="now"),e.since||(e.since=0),"now"===e.since)return void this.db.info().then(n=>{if(this.isCancelled)return void t(null,{status:"cancelled"});e.since=n.update_seq,this.doChanges(e)},t);if(eQ._changesFilterPlugin){if(eQ._changesFilterPlugin.normalize(e),eQ._changesFilterPlugin.shouldFilter(this,e))return eQ._changesFilterPlugin.filter(this,e)}else["doc_ids","filter","selector","view"].forEach(function(t){t in e&&E("warn",'The "'+t+'" option was passed in to changes/replicate, but pouchdb-changes-filter plugin is not installed, so it was ignored. Please install the plugin to enable filtering.')});"descending"in e||(e.descending=!1),e.limit=0===e.limit?1:e.limit,e.complete=t;var n=this.db._changes(e);if(n&&"function"==typeof n.cancel){let e=this.cancel;this.cancel=(...t)=>{n.cancel(),e.apply(this,t)}}}}function eL(e,t){return function(n,r){n||r[0]&&r[0].error?((n=n||r[0]).docId=t,e(n)):e(null,r.length?r[0]:r)}}function eR(e,t){return e._id===t._id?(e._revisions?e._revisions.start:0)-(t._revisions?t._revisions.start:0):e._id<t._id?-1:1}function eN(e){return null===e||"object"!=typeof e||Array.isArray(e)}let eF=/^\d+-[^-]*$/;function eJ(e){return"string"==typeof e&&eF.test(e)}class ez extends g(){_setup(){this.post=$("post",function(e,t,n){if("function"==typeof t&&(n=t,t={}),eN(e))return n(W(U));this.bulkDocs({docs:[e]},t,eL(n,e._id))}).bind(this),this.put=$("put",function(e,t,n){var r,i,o,s;if("function"==typeof t&&(n=t,t={}),eN(e))return n(W(U));if(H(e._id),"_rev"in e&&!eJ(e._rev))return n(W(Q));if(eB(e._id)&&"function"==typeof this._putLocal)if(e._deleted)return this._removeLocal(e,n);else return this._putLocal(e,n);let a=n=>{"function"==typeof this._put&&!1!==t.new_edits?this._put(e,t,n):this.bulkDocs({docs:[e]},t,eL(n,e._id))};t.force&&e._rev?(i=(r=e._rev.split("-"))[1],e._revisions={start:o=parseInt(r[0],10)+1,ids:[s=eb(),i]},e._rev=o+"-"+s,t.new_edits=!1,a(function(t){var r=t?null:{ok:!0,id:e._id,rev:e._rev};n(t,r)})):a(n)}).bind(this),this.putAttachment=$("putAttachment",function(e,t,n,r,i){var o=this;function s(e){var n="_rev"in e?parseInt(e._rev,10):0;return e._attachments=e._attachments||{},e._attachments[t]={content_type:i,data:r,revpos:++n},o.put(e)}return"function"==typeof i&&(i=r,r=n,n=null),void 0===i&&(i=r,r=n,n=null),i||E("warn","Attachment",t,"on document",e,"is missing content_type"),o.get(e).then(function(e){if(e._rev!==n)throw W(T);return s(e)},function(t){if(t.reason===B.message)return s({_id:e});throw t})}).bind(this),this.removeAttachment=$("removeAttachment",function(e,t,n,r){this.get(e,(e,i)=>e?void r(e):i._rev!==n?void r(W(T)):i._attachments?void(delete i._attachments[t],0===Object.keys(i._attachments).length&&delete i._attachments,this.put(i,r)):r())}).bind(this),this.remove=$("remove",function(e,t,n,r){"string"==typeof t?(i={_id:e,_rev:t},"function"==typeof n&&(r=n,n={})):(i=e,"function"==typeof t?(r=t,n={}):(r=n,n=t)),(n=n||{}).was_delete=!0;var i,o={_id:i._id,_rev:i._rev||n.rev};if(o._deleted=!0,eB(o._id)&&"function"==typeof this._removeLocal)return this._removeLocal(i,r);this.bulkDocs({docs:[o]},n,eL(r,o._id))}).bind(this),this.revsDiff=$("revsDiff",function(e,t,n){"function"==typeof t&&(n=t,t={});var r=Object.keys(e);if(!r.length)return n(null,{});var i=0,o=new Map;function s(e,t){o.has(e)||o.set(e,{missing:[]}),o.get(e).missing.push(t)}r.forEach(function(t){this._getRevisionTree(t,function(a,c){if(a&&404===a.status&&"missing"===a.message)o.set(t,{missing:e[t]});else{var u;if(a)return n(a);u=e[t].slice(0),ek(c,function(e,n,r,i,o){var a=n+"-"+r,c=u.indexOf(a);-1!==c&&(u.splice(c,1),"available"!==o.status&&s(t,a))}),u.forEach(function(e){s(t,e)})}if(++i===r.length){var l={};return o.forEach(function(e,t){l[t]=e}),n(null,l)}})},this)}).bind(this),this.bulkGet=$("bulkGet",function(e,t){x(this,e,t)}).bind(this),this.compactDocument=$("compactDocument",function(e,t,n){this._getRevisionTree(e,(r,i)=>{if(r)return n(r);var o,s,a=(o={},s=[],ek(i,function(e,t,n,r){var i=t+"-"+n;return e&&(o[i]=0),void 0!==r&&s.push({from:r,to:i}),i}),s.reverse(),s.forEach(function(e){void 0===o[e.from]?o[e.from]=1+o[e.to]:o[e.from]=Math.min(o[e.from],1+o[e.to])}),o),c=[],u=[];Object.keys(a).forEach(function(e){a[e]>t&&c.push(e)}),ek(i,function(e,t,n,r,i){var o=t+"-"+n;"available"===i.status&&-1!==c.indexOf(o)&&u.push(o)}),this._doCompaction(e,u,n)})}).bind(this),this.compact=$("compact",function(e,t){"function"==typeof e&&(t=e,e={}),e=e||{},this._compactionQueue=this._compactionQueue||[],this._compactionQueue.push({opts:e,callback:t}),1===this._compactionQueue.length&&function e(t){var n=t._compactionQueue[0],r=n.opts,i=n.callback;t.get("_local/compaction").catch(function(){return!1}).then(function(n){n&&n.last_seq&&(r.last_seq=n.last_seq),t._compact(r,function(n,r){n?i(n):i(null,r),S(function(){t._compactionQueue.shift(),t._compactionQueue.length&&e(t)})})})}(this)}).bind(this),this.get=$("get",function(e,t,n){if("function"==typeof t&&(n=t,t={}),t=t||{},"string"!=typeof e)return n(W(M));if(eB(e)&&"function"==typeof this._getLocal)return this._getLocal(e,n);var r=[];let i=()=>{var i=[],o=r.length;if(!o)return n(null,i);r.forEach(r=>{this.get(e,{rev:r,revs:t.revs,latest:t.latest,attachments:t.attachments,binary:t.binary},function(e,t){if(e)i.push({missing:r});else{for(var s,a=0,c=i.length;a<c;a++)if(i[a].ok&&i[a].ok._rev===t._rev){s=!0;break}s||i.push({ok:t})}--o||n(null,i)})})};if(t.open_revs){if("all"===t.open_revs)this._getRevisionTree(e,function(e,t){if(e)return n(e);r=ej(t).map(function(e){return e.rev}),i()});else{if(!Array.isArray(t.open_revs))return n(W(N,"function_clause"));r=t.open_revs;for(var o=0;o<r.length;o++)if(!eJ(r[o]))return n(W(Q));i()}return}return this._get(e,t,(r,i)=>{if(r)return r.docId=e,n(r);var o=i.doc,s=i.metadata,a=i.ctx;if(t.conflicts){var c=eO(s);c.length&&(o._conflicts=c)}if(eI(s,o._rev)&&(o._deleted=!0),t.revs||t.revs_info){for(var u=o._rev.split("-"),l=parseInt(u[0],10),f=u[1],d=eq(s.rev_tree),h=null,p=0;p<d.length;p++){var v=d[p];let e=v.ids.findIndex(e=>e.id===f);e!==l-1&&(h||-1===e)||(h=v)}if(!h)return(r=Error("invalid rev tree")).docId=e,n(r);let i=o._rev.split("-")[1],a=h.ids.findIndex(e=>e.id===i)+1;var y=h.ids.length-a;if(h.ids.splice(a,y),h.ids.reverse(),t.revs&&(o._revisions={start:h.pos+h.ids.length-1,ids:h.ids.map(function(e){return e.id})}),t.revs_info){var g=h.pos+h.ids.length;o._revs_info=h.ids.map(function(e){return{rev:--g+"-"+e.id,status:e.opts.status}})}}if(t.attachments&&o._attachments){var _=o._attachments,m=Object.keys(_).length;if(0===m)return n(null,o);Object.keys(_).forEach(e=>{this._getAttachment(o._id,e,_[e],{binary:t.binary,metadata:s,ctx:a},function(t,r){var i=o._attachments[e];i.data=r,delete i.stub,delete i.length,--m||n(null,o)})})}else{if(o._attachments)for(var b in o._attachments)Object.prototype.hasOwnProperty.call(o._attachments,b)&&(o._attachments[b].stub=!0);n(null,o)}})}).bind(this),this.getAttachment=$("getAttachment",function(e,t,n,r){n instanceof Function&&(r=n,n={}),this._get(e,n,(i,o)=>i?r(i):o.doc._attachments&&o.doc._attachments[t]?void(n.ctx=o.ctx,n.binary=!0,n.metadata=o.metadata,this._getAttachment(e,t,o.doc._attachments[t],n,r)):r(W(B)))}).bind(this),this.allDocs=$("allDocs",function(e,t){if("function"==typeof e&&(t=e,e={}),e.skip=void 0!==e.skip?e.skip:0,e.start_key&&(e.startkey=e.start_key),e.end_key&&(e.endkey=e.end_key),"keys"in e){if(!Array.isArray(e.keys))return t(TypeError("options.keys must be an array"));var n,r,i=["startkey","endkey","key"].filter(function(t){return t in e})[0];if(i)return void t(W(J,"Query parameter `"+i+"` is not compatible with multi-get"));if(!Z(this)&&(r="limit"in(n=e)?n.keys.slice(n.skip,n.limit+n.skip):n.skip>0?n.keys.slice(n.skip):n.keys,n.keys=r,n.skip=0,delete n.limit,n.descending&&(r.reverse(),n.descending=!1),0===e.keys.length))return this._allDocs({limit:0},t)}return this._allDocs(e,t)}).bind(this),this.close=$("close",function(e){return this._closed=!0,this.emit("closed"),this._close(e)}).bind(this),this.info=$("info",function(e){this._info((t,n)=>{if(t)return e(t);n.db_name=n.db_name||this.name,n.auto_compaction=!!(this.auto_compaction&&!Z(this)),n.adapter=this.adapter,e(null,n)})}).bind(this),this.id=$("id",function(e){return this._id(e)}).bind(this),this.bulkDocs=$("bulkDocs",function(e,t,n){if("function"==typeof t&&(n=t,t={}),t=t||{},Array.isArray(e)&&(e={docs:e}),!e||!e.docs||!Array.isArray(e.docs))return n(W(I));for(var r,i=0;i<e.docs.length;++i){let t=e.docs[i];if(eN(t))return n(W(U));if("_rev"in t&&!eJ(t._rev))return n(W(Q))}if(e.docs.forEach(function(e){e._attachments&&Object.keys(e._attachments).forEach(function(t){r=r||"_"===t.charAt(0)&&t+" is not a valid attachment name, attachment names cannot start with '_'",e._attachments[t].content_type||E("warn","Attachment",t,"on document",e._id,"is missing content_type")})}),r)return n(W(K,r));"new_edits"in t||("new_edits"in e?t.new_edits=e.new_edits:t.new_edits=!0);var o=this;t.new_edits||Z(o)||e.docs.sort(eR),function(e){for(var t=0;t<e.length;t++){var n=e[t];if(n._deleted)delete n._attachments;else if(n._attachments)for(var r=Object.keys(n._attachments),i=0;i<r.length;i++){var o=r[i];n._attachments[o]=j(n._attachments[o],["data","digest","content_type","length","revpos","stub"])}}}(e.docs);var s=e.docs.map(function(e){return e._id});this._bulkDocs(e,t,function(e,r){if(e)return n(e);if(t.new_edits||(r=r.filter(function(e){return e.error})),!Z(o))for(var i=0,a=r.length;i<a;i++)r[i].id=r[i].id||s[i];n(null,r)})}).bind(this),this.registerDependentDatabase=$("registerDependentDatabase",function(e,t){var n=b(this.__opts);this.__opts.view_adapter&&(n.adapter=this.__opts.view_adapter);var r=new this.constructor(e,n);ec(this,"_local/_pouch_dependentDbs",function(t){return t.dependentDbs=t.dependentDbs||{},!t.dependentDbs[e]&&(t.dependentDbs[e]=!0,t)}).then(function(){t(null,{db:r})}).catch(t)}).bind(this),this.destroy=$("destroy",function(e,t){"function"==typeof e&&(t=e,e={});var n=!("use_prefix"in this)||this.use_prefix;let r=()=>{this._destroy(e,(e,n)=>{if(e)return t(e);this._destroyed=!0,this.emit("destroyed"),t(null,n||{ok:!0})})};if(Z(this))return r();this.get("_local/_pouch_dependentDbs",(e,i)=>{if(e)if(404!==e.status)return t(e);else return r();var o=i.dependentDbs,s=this.constructor;Promise.all(Object.keys(o).map(e=>{var t=n?e.replace(RegExp("^"+s.prefix),""):e;return new s(t,this.__opts).destroy()})).then(r,t)})}).bind(this)}_compact(e,t){var n,r={return_docs:!1,last_seq:e.last_seq||0,since:e.last_seq||0},i=[],o=0;let s=e=>{this.activeTasks.update(n,{completed_items:++o}),i.push(this.compactDocument(e.id,0))},a=e=>{this.activeTasks.remove(n,e),t(e)},c=e=>{var r=e.last_seq;Promise.all(i).then(()=>ec(this,"_local/compaction",e=>(!e.last_seq||e.last_seq<r)&&(e.last_seq=r,e))).then(()=>{this.activeTasks.remove(n),t(null,{ok:!0})}).catch(a)};this.info().then(e=>{n=this.activeTasks.add({name:"database_compaction",total_items:e.update_seq-r.last_seq}),this.changes(r).on("change",s).on("complete",c).on("error",a)})}changes(e,t){return"function"==typeof e&&(t=e,e={}),(e=e||{}).return_docs="return_docs"in e?e.return_docs:!e.live,new eM(this,e,t)}type(){return"function"==typeof this._type?this._type():this.adapter}}ez.prototype.purge=$("_purge",function(e,t,n){if(void 0===this._purge)return n(W(N,"Purge is not implemented in the "+this.adapter+" adapter."));var r=this;r._getRevisionTree(e,(i,o)=>{let s;if(i)return n(i);if(!o)return n(W(B));try{s=function(e,t){let n,r=[],i=e.slice();for(;n=i.pop();){let{pos:e,ids:o}=n,s=`${e}-${o[0]}`,a=o[2];if(r.push(s),s===t){if(0!==a.length)throw Error("The requested revision is not a leaf");return r.reverse()}(0===a.length||a.length>1)&&(r=[]);for(let t=0,n=a.length;t<n;t++)i.push({pos:e+1,ids:a[t]})}if(0===r.length)throw Error("The requested revision does not exist");return r.reverse()}(o,t)}catch(e){return n(e.message||e)}r._purge(e,s,(i,o)=>{if(i)return n(i);r.get("_local/purges").then(function(n){let r=n.purgeSeq+1;return n.purges.push({docId:e,rev:t,purgeSeq:r}),n.purges.length>self.purged_infos_limit&&n.purges.splice(0,n.purges.length-self.purged_infos_limit),n.purgeSeq=r,n}).catch(function(n){if(404!==n.status)throw n;return{_id:"_local/purges",purges:[{docId:e,rev:t,purgeSeq:0}],purgeSeq:0}}).then(function(e){return r.put(e)}).then(function(){return n(null,o)})})})});class eK{constructor(){this.isReady=!1,this.failed=!1,this.queue=[]}execute(){var e;if(this.failed)for(;e=this.queue.shift();)e(this.failed);else for(;e=this.queue.shift();)e()}fail(e){this.failed=e,this.execute()}ready(e){this.isReady=!0,this.db=e,this.execute()}addTask(e){this.queue.push(e),this.failed&&this.execute()}}function eU(e,t){let n=function(...e){if(!(this instanceof n))return new n(...e);t.apply(this,e)};return n.prototype=Object.create(e.prototype,{constructor:{value:n}}),n}class eV extends ez{constructor(e,t){super(),this._setup(e,t)}_setup(e,t){if(super._setup(),t=t||{},e&&"object"==typeof e&&(e=(t=e).name,delete t.name),void 0===t.deterministic_revs&&(t.deterministic_revs=!0),this.__opts=t=b(t),this.auto_compaction=t.auto_compaction,this.purged_infos_limit=t.purged_infos_limit||1e3,this.prefix=eQ.prefix,"string"!=typeof e)throw Error("Missing/invalid DB name");var n=function(e,t){var n=e.match(/([a-z-]*):\/\/(.*)/);if(n)return{name:/https?/.test(n[1])?n[1]+"://"+n[2]:n[2],adapter:n[1]};var r=eQ.adapters,i=eQ.preferredAdapters,s=eQ.prefix,a=t.adapter;if(!a)for(var c=0;c<i.length;++c){if("idb"===(a=i[c])&&"websql"in r&&o&&localStorage["_pouch__websqldb_"+s+e]){E("log",'PouchDB is downgrading "'+e+'" to WebSQL to avoid data loss, because it was already opened with WebSQL.');continue}break}var u=r[a];return{name:u&&"use_prefix"in u&&!u.use_prefix?e:s+e,adapter:a}}((t.prefix||"")+e,t);if(t.name=n.name,t.adapter=t.adapter||n.adapter,this.name=e,this._adapter=t.adapter,eQ.emit("debug",["adapter","Picked adapter: ",t.adapter]),!eQ.adapters[t.adapter]||!eQ.adapters[t.adapter].valid())throw Error("Invalid Adapter: "+t.adapter);if(t.view_adapter&&(!eQ.adapters[t.view_adapter]||!eQ.adapters[t.view_adapter].valid()))throw Error("Invalid View Adapter: "+t.view_adapter);this.taskqueue=new eK,this.adapter=t.adapter,eQ.adapters[t.adapter].call(this,t,e=>{if(e)return this.taskqueue.fail(e);var t=this;function n(e){t.removeListener("closed",r),e||t.constructor.emit("destroyed",t.name)}function r(){t.removeListener("destroyed",n),t.constructor.emit("unref",t)}t.once("destroyed",n),t.once("closed",r),t.constructor.emit("ref",t),this.emit("created",this),eQ.emit("created",this.name),this.taskqueue.ready(this)})}}let eQ=eU(eV,function(e,t){eV.prototype._setup.call(this,e,t)});var eG=fetch,eW=Headers;eQ.adapters={},eQ.preferredAdapters=[],eQ.prefix="_pouch_";var eX=new(g());function eY(e,t){for(var n=e,r=0,i=t.length;r<i&&(n=n[t[r]]);r++);return n}function eH(e){for(var t=[],n="",r=0,i=e.length;r<i;r++){var o=e[r];r>0&&"\\"===e[r-1]&&("$"===o||"."===o)?n=n.substring(0,n.length-1)+o:"."===o?(t.push(n),n=""):n+=o}return t.push(n),t}Object.keys(g().prototype).forEach(function(e){"function"==typeof g().prototype[e]&&(eQ[e]=eX[e].bind(eX))}),r=eQ._destructionListeners=new Map,eQ.on("ref",function(e){r.has(e.name)||r.set(e.name,[]),r.get(e.name).push(e)}),eQ.on("unref",function(e){if(r.has(e.name)){var t=r.get(e.name),n=t.indexOf(e);n<0||(t.splice(n,1),t.length>1?r.set(e.name,t):r.delete(e.name))}}),eQ.on("destroyed",function(e){if(r.has(e)){var t=r.get(e);r.delete(e),t.forEach(function(e){e.emit("destroyed",!0)})}}),eQ.adapter=function(e,t,n){t.valid()&&(eQ.adapters[e]=t,n&&eQ.preferredAdapters.push(e))},eQ.plugin=function(e){if("function"==typeof e)e(eQ);else if("object"!=typeof e||0===Object.keys(e).length)throw Error('Invalid plugin: got "'+e+'", expected an object or a function');else Object.keys(e).forEach(function(t){eQ.prototype[t]=e[t]});return this.__defaults&&(eQ.__defaults=Object.assign({},this.__defaults)),eQ},eQ.defaults=function(e){let t=eU(eQ,function(e,n){n=n||{},e&&"object"==typeof e&&(e=(n=e).name,delete n.name),n=Object.assign({},t.__defaults,n),eQ.call(this,e,n)});return t.preferredAdapters=eQ.preferredAdapters.slice(),Object.keys(eQ).forEach(function(e){e in t||(t[e]=eQ[e])}),t.__defaults=Object.assign({},this.__defaults,e),t},eQ.fetch=function(e,t){return eG(e,t)},eQ.prototype.activeTasks=eQ.activeTasks=new class{constructor(){this.tasks={}}list(){return Object.values(this.tasks)}add(e){let t=p();return this.tasks[t]={id:t,name:e.name,total_items:e.total_items,created_at:new Date().toJSON()},t}get(e){return this.tasks[e]}remove(e,t){return delete this.tasks[e],this.tasks}update(e,t){let n=this.tasks[e];if(void 0!==n){let r={id:n.id,name:n.name,created_at:n.created_at,total_items:t.total_items||n.total_items,completed_items:t.completed_items||n.completed_items,updated_at:new Date().toJSON()};this.tasks[e]=r}return this.tasks}};var eZ=["$or","$nor","$not"];function e0(e){return eZ.indexOf(e)>-1}function e1(e){return Object.keys(e)[0]}function e4(e){var t={},n={$or:!0,$nor:!0};return e.forEach(function(e){Object.keys(e).forEach(function(r){var i=e[r];if("object"!=typeof i&&(i={$eq:i}),e0(r))if(i instanceof Array){if(n[r]){n[r]=!1,t[r]=i;return}var o=[];t[r].forEach(function(e){Object.keys(i).forEach(function(t){var n=i[t],r=Math.max(Object.keys(e).length,Object.keys(n).length),s=e4([e,n]);Object.keys(s).length<=r||o.push(s)})}),t[r]=o}else t[r]=e4([i]);else{var s=t[r]=t[r]||{};Object.keys(i).forEach(function(e){var t,n,r,o,a,c,u,l,f,d,h,p,v=i[e];if("$gt"===e||"$gte"===e){return t=e,n=v,void(void 0===(r=s).$eq&&(void 0!==r.$gte?"$gte"===t?n>r.$gte&&(r.$gte=n):n>=r.$gte&&(delete r.$gte,r.$gt=n):void 0!==r.$gt?"$gte"===t?n>r.$gt&&(delete r.$gt,r.$gte=n):n>r.$gt&&(r.$gt=n):r[t]=n))}if("$lt"===e||"$lte"===e){return o=e,a=v,void(void 0===(c=s).$eq&&(void 0!==c.$lte?"$lte"===o?a<c.$lte&&(c.$lte=a):a<=c.$lte&&(delete c.$lte,c.$lt=a):void 0!==c.$lt?"$lte"===o?a<c.$lt&&(delete c.$lt,c.$lte=a):a<c.$lt&&(c.$lt=a):c[o]=a))}if("$ne"===e){return u=v,void("$ne"in(l=s)?l.$ne.push(u):l.$ne=[u])}if("$eq"===e){return f=v,d=s,void(delete d.$gt,delete d.$gte,delete d.$lt,delete d.$lte,delete d.$ne,d.$eq=f)}if("$regex"===e){return h=v,void("$regex"in(p=s)?p.$regex.push(h):p.$regex=[h])}s[e]=v})}})}),t}function e2(e,t){if(e===t)return 0;e=e3(e),t=e3(t);var n,r,i=e6(e),o=e6(t);if(i-o!=0)return i-o;switch(typeof e){case"number":return e-t;case"boolean":return e<t?-1:1;case"string":return(n=e)===(r=t)?0:n>r?1:-1}return Array.isArray(e)?function(e,t){for(var n=Math.min(e.length,t.length),r=0;r<n;r++){var i=e2(e[r],t[r]);if(0!==i)return i}return e.length===t.length?0:e.length>t.length?1:-1}(e,t):function(e,t){for(var n=Object.keys(e),r=Object.keys(t),i=Math.min(n.length,r.length),o=0;o<i;o++){var s=e2(n[o],r[o]);if(0!==s||0!==(s=e2(e[n[o]],t[r[o]])))return s}return n.length===r.length?0:n.length>r.length?1:-1}(e,t)}function e3(e){switch(typeof e){case"undefined":return null;case"number":if(e===1/0||e===-1/0||isNaN(e))return null;break;case"object":var t=e;if(Array.isArray(e)){var n=e.length;e=Array(n);for(var r=0;r<n;r++)e[r]=e3(t[r])}else if(e instanceof Date)return e.toJSON();else if(null!==e){for(var i in e={},t)if(Object.prototype.hasOwnProperty.call(t,i)){var o=t[i];void 0!==o&&(e[i]=e3(o))}}}return e}function e5(e){return e6(e=e3(e))+""+function(e){if(null!==e)switch(typeof e){case"boolean":return+!!e;case"number":return function(e){if(0===e)return"1";var t,n=e.toExponential().split(/e\+?/),r=parseInt(n[1],10),i=e<0,o=i?"0":"2";o+=""+(t=((i?-r:r)- -324).toString(),function(e,t,n){for(var r="",i=3-e.length;r.length<i;)r+="0";return r}(t,"0",3)+t);var s=Math.abs(parseFloat(n[0]));i&&(s=10-s);var a=s.toFixed(20);return o+(""+(a=a.replace(/\.?0+$/,"")))}(e);case"string":return e.replace(/\u0002/g,"\x02\x02").replace(/\u0001/g,"\x01\x02").replace(/\u0000/g,"\x01\x01");case"object":var t=Array.isArray(e),n=t?e:Object.keys(e),r=-1,i=n.length,o="";if(t)for(;++r<i;)o+=e5(n[r]);else for(;++r<i;){var s=n[r];o+=e5(s)+e5(e[s])}return o}return""}(e)+"\0"}function e6(e){var t=["boolean","number","string","object"].indexOf(typeof e);return~t?null===e?1:Array.isArray(e)?5:t<3?t+2:t+3:Array.isArray(e)?5:void 0}function e8(e,t,n){return n.every(function(n){var r,i,o,s=t[n],a=eH(n),c=eY(e,a);return e0(n)?(r=n,i=s,o=e,"$or"===r?i.some(function(e){return e8(o,e,Object.keys(e))}):"$not"===r?!e8(o,i,Object.keys(i)):!i.find(function(e){return e8(o,e,Object.keys(e))})):e9(s,e,a,c)})}function e9(e,t,n,r){return!e||("object"==typeof e?Object.keys(e).every(function(i){var o=e[i];if(0===i.indexOf("$"))return e7(i,t,o,n,r);var s=eH(i);if(void 0===r&&"object"!=typeof o&&s.length>0)return!1;var a=eY(r,s);return"object"==typeof o?e9(o,t,n,a):e7("$eq",t,o,s,a)}):e===r)}function e7(e,t,n,r,i){if(!tr[e])throw Error('unknown operator "'+e+'" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, $nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');return tr[e](t,n,r,i)}function te(e){return null!=e}function tt(e){return void 0!==e}function tn(e,t){return t.some(function(t){return e instanceof Array?e.some(function(e){return 0===e2(t,e)}):0===e2(t,e)})}var tr={$elemMatch:function(e,t,n,r){return!!Array.isArray(r)&&0!==r.length&&("object"==typeof r[0]&&null!==r[0]?r.some(function(e){return e8(e,t,Object.keys(t))}):r.some(function(r){return e9(t,e,n,r)}))},$allMatch:function(e,t,n,r){return!!Array.isArray(r)&&0!==r.length&&("object"==typeof r[0]&&null!==r[0]?r.every(function(e){return e8(e,t,Object.keys(t))}):r.every(function(r){return e9(t,e,n,r)}))},$eq:function(e,t,n,r){return tt(r)&&0===e2(r,t)},$gte:function(e,t,n,r){return tt(r)&&e2(r,t)>=0},$gt:function(e,t,n,r){return tt(r)&&e2(r,t)>0},$lte:function(e,t,n,r){return tt(r)&&0>=e2(r,t)},$lt:function(e,t,n,r){return tt(r)&&0>e2(r,t)},$exists:function(e,t,n,r){return t?tt(r):!tt(r)},$mod:function(e,t,n,r){return te(r)&&"number"==typeof r&&parseInt(r,10)===r&&r%t[0]===t[1]},$ne:function(e,t,n,r){return t.every(function(e){return 0!==e2(r,e)})},$in:function(e,t,n,r){return te(r)&&tn(r,t)},$nin:function(e,t,n,r){return te(r)&&!tn(r,t)},$size:function(e,t,n,r){return te(r)&&Array.isArray(r)&&r.length===t},$all:function(e,t,n,r){return Array.isArray(r)&&t.every(function(e){return r.some(function(t){return 0===e2(e,t)})})},$regex:function(e,t,n,r){return te(r)&&"string"==typeof r&&t.every(function(e){return new RegExp(e).test(r)})},$type:function(e,t,n,r){switch(t){case"null":return null===r;case"boolean":return"boolean"==typeof r;case"number":return"number"==typeof r;case"string":return"string"==typeof r;case"array":return r instanceof Array;case"object":return"[object Object]"===({}).toString.call(r)}}};function ti(e,t){if(e.selector&&e.filter&&"_selector"!==e.filter)return t(Error('selector invalid for filter "'+("string"==typeof e.filter?e.filter:"function")+'"'));t()}function to(e){e.view&&!e.filter&&(e.filter="_view"),e.selector&&!e.filter&&(e.filter="_selector"),e.filter&&"string"==typeof e.filter&&("_view"===e.filter?e.view=et(e.view):e.filter=et(e.filter))}function ts(e,t){return t.filter&&"string"==typeof t.filter&&!t.doc_ids&&!Z(e.db)}function ta(e,t){var n=t.complete;if("_view"===t.filter){if(!t.view||"string"!=typeof t.view)return n(W(K,"`view` filter parameter not found or invalid."));var r=ee(t.view);e.db.get("_design/"+r[0],function(i,o){if(e.isCancelled)return n(null,{status:"cancelled"});if(i)return n(X(i));var s=o&&o.views&&o.views[r[1]]&&o.views[r[1]].map;if(!s)return n(W(B,o.views?"missing json key: "+r[1]:"missing json key: views"));t.filter=ea(["return function(doc) {",'  "use strict";',"  var emitted = false;","  var emit = function (a, b) {","    emitted = true;","  };","  var view = "+s+";","  view(doc);","  if (emitted) {","    return true;","  }","};"].join("\n"),{}),e.doChanges(t)})}else if(t.selector)t.filter=function(e){var n=t.selector;if("object"!=typeof n)throw Error("Selector error: expected a JSON object");var r=function(e,t,n){if(e=e.filter(function(e){return e8(e.doc,t.selector,n)}),t.sort){var r,i=function(e){function t(t){return e.map(function(e){return eY(t,eH(e1(e)))})}return function(e,n){var r,i,o=e2(t(e.doc),t(n.doc));return 0!==o?o:(r=e.doc._id)<(i=n.doc._id)?-1:+(r>i)}}(t.sort);e=e.sort(i),"string"!=typeof t.sort[0]&&"desc"===(r=t.sort[0])[e1(r)]&&(e=e.reverse())}if("limit"in t||"skip"in t){var o=t.skip||0,s=("limit"in t?t.limit:e.length)+o;e=e.slice(o,s)}return e}([{doc:e}],{selector:n=function(e){var t=b(e);(function e(t,n){for(var r in t){"$and"===r&&(n=!0);var i=t[r];"object"==typeof i&&(n=e(i,n))}return n})(t,!1)&&"$and"in(t=function e(t){for(var n in t){if(Array.isArray(t))for(var r in t)t[r].$and&&(t[r]=e4(t[r].$and));var i=t[n];"object"==typeof i&&e(i)}return t}(t))&&(t=e4(t.$and)),["$or","$nor"].forEach(function(e){e in t&&t[e].forEach(function(e){for(var t=Object.keys(e),n=0;n<t.length;n++){var r=t[n],i=e[r];("object"!=typeof i||null===i)&&(e[r]={$eq:i})}})}),"$not"in t&&(t.$not=e4([t.$not]));for(var n=Object.keys(t),r=0;r<n.length;r++){var i=n[r],o=t[i];("object"!=typeof o||null===o)&&(o={$eq:o}),t[i]=o}return function e(t){Object.keys(t).forEach(function(n){var r=t[n];Array.isArray(r)?r.forEach(function(t){t&&"object"==typeof t&&e(t)}):"$ne"===n?t.$ne=[r]:"$regex"===n?t.$regex=[r]:r&&"object"==typeof r&&e(r)})}(t),t}(n)},Object.keys(n));return r&&1===r.length},e.doChanges(t);else{var i=ee(t.filter);e.db.get("_design/"+i[0],function(r,o){if(e.isCancelled)return n(null,{status:"cancelled"});if(r)return n(X(r));var s=o&&o.filters&&o.filters[i[1]];if(!s)return n(W(B,o&&o.filters?"missing json key: "+i[1]:"missing json key: filters"));t.filter=ea('"use strict";\nreturn '+s+";",{}),e.doChanges(t)})}}function tc(e){return e.reduce(function(e,t){return e[t]=!0,e},{})}eQ.plugin(function(e){e._changesFilterPlugin={validate:ti,normalize:to,shouldFilter:ts,filter:ta}}),eQ.version="9.0.0";var tu=tc(["_id","_rev","_access","_attachments","_deleted","_revisions","_revs_info","_conflicts","_deleted_conflicts","_local_seq","_rev_tree","_replication_id","_replication_state","_replication_state_time","_replication_state_reason","_replication_stats","_removed"]),tl=tc(["_access","_attachments","_replication_id","_replication_state","_replication_state_time","_replication_state_reason","_replication_stats"]);function tf(e){if(!/^\d+-/.test(e))return W(Q);var t=e.indexOf("-"),n=e.substring(0,t),r=e.substring(t+1);return{prefix:parseInt(n,10),id:r}}function td(e,t,n){n||(n={deterministic_revs:!0});var r,i,o,s={status:"available"};if(e._deleted&&(s.deleted=!0),t)if(e._id||(e._id=p()),i=eb(e,n.deterministic_revs),e._rev){if((o=tf(e._rev)).error)return o;e._rev_tree=[{pos:o.prefix,ids:[o.id,{status:"missing"},[[i,s,[]]]]}],r=o.prefix+1}else e._rev_tree=[{pos:1,ids:[i,s,[]]}],r=1;else if(e._revisions&&(e._rev_tree=function(e,t){for(var n=e.start-e.ids.length+1,r=e.ids,i=[r[0],t,[]],o=1,s=r.length;o<s;o++)i=[r[o],{status:"missing"},[i]];return[{pos:n,ids:i}]}(e._revisions,s),r=e._revisions.start,i=e._revisions.ids[0]),!e._rev_tree){if((o=tf(e._rev)).error)return o;e._rev_tree=[{pos:r=o.prefix,ids:[i=o.id,s,[]]}]}H(e._id),e._rev=r+"-"+i;var a={metadata:{},data:{}};for(var c in e)if(Object.prototype.hasOwnProperty.call(e,c)){var u="_"===c[0];if(u&&!tu[c]){var l=W(z,c);throw l.message=z.message+": "+c,l}u&&!tl[c]?a.metadata[c.slice(1)]=e[c]:a.data[c]=e[c]}return a}var th="document-store",tp="by-sequence",tv="attach-store",ty="attach-seq-store",tg="meta-store",t_="local-store",tm="detect-blob-support";function tb(e){return function(t){var n="unknown_error";t.target&&t.target.error&&(n=t.target.error.name||t.target.error.message),e(W(V,n,t.type))}}function tw(e,t,n){return{data:function(e){try{return JSON.stringify(e)}catch(t){return v.stringify(e)}}(e),winningRev:t,deletedOrLocal:n?"1":"0",seq:e.seq,id:e.id}}function tk(e){if(!e)return null;var t=function(e){try{return JSON.parse(e)}catch(t){return v.parse(e)}}(e.data);return t.winningRev=e.winningRev,t.deleted="1"===e.deletedOrLocal,t.seq=e.seq,t}function t$(e){if(!e)return e;var t=e._doc_id_rev.lastIndexOf(":");return e._id=e._doc_id_rev.substring(0,t-1),e._rev=e._doc_id_rev.substring(t+1),delete e._doc_id_rev,e}function tj(e,t,n,r){if(n)if(e)if("string"!=typeof e)r(e);else r(ed(eu(e),t));else r(ef([""],{type:t}));else e?"string"!=typeof e?eh(e,function(e){r(el(e))}):r(e):r("")}function tO(e,t,n,r){var i=Object.keys(e._attachments||{});if(!i.length)return r&&r();var o=0;function s(){++o===i.length&&r&&r()}i.forEach(function(r){if(t.attachments&&t.include_docs){var i,o;o=(i=e._attachments[r]).digest,n.objectStore(tv).get(o).onsuccess=function(e){i.body=e.target.result.body,s()}}else e._attachments[r].stub=!0,s()})}function tq(e,t){return Promise.all(e.map(function(e){if(e.doc&&e.doc._attachments)return Promise.all(Object.keys(e.doc._attachments).map(function(n){var r=e.doc._attachments[n];if("body"in r){var i=r.body,o=r.content_type;return new Promise(function(s){tj(i,o,t,function(t){e.doc._attachments[n]=Object.assign(j(r,["digest","content_type"]),{data:t}),s()})})}}))}))}function tx(e,t,n){var r=[],i=n.objectStore(tp),o=n.objectStore(tv),s=n.objectStore(ty),a=e.length;function c(){r.length&&r.forEach(function(e){s.index("digestSeq").count(IDBKeyRange.bound(e+"::",e+"::￿",!1,!1)).onsuccess=function(t){t.target.result||o.delete(e)}})}e.forEach(function(e){i.index("_doc_id_rev").getKey(t+"::"+e).onsuccess=function(e){var t=e.target.result;if("number"!=typeof t)return void(!--a&&c());i.delete(t),s.index("seq").openCursor(IDBKeyRange.only(t)).onsuccess=function(e){var t=e.target.result;if(t){var n=t.value.digestSeq.split("::")[0];r.push(n),s.delete(t.primaryKey),t.continue()}else--a||c()}}})}function tS(e,t,n){try{return{txn:e.transaction(t,n)}}catch(e){return{error:e}}}var tA=new A;function tE(e,t,n,r,i){var o,s,a;function c(e){s=e.target.result,o&&i(o,s,a)}function u(e){o=e.target.result,s&&i(o,s,a)}function l(e){var t=e.target.result;if(!t)return i();i([t.key],[t.value],t)}-1===r&&(r=1e3),"function"==typeof e.getAll&&"function"==typeof e.getAllKeys&&r>1&&!n?(a={continue:function(){if(!o.length)return i();var n,a=o[o.length-1];if(t&&t.upper)try{n=IDBKeyRange.bound(a,t.upper,!0,t.upperOpen)}catch(e){if("DataError"===e.name&&0===e.code)return i()}else n=IDBKeyRange.lowerBound(a,!0);t=n,o=null,s=null,e.getAll(t,r).onsuccess=c,e.getAllKeys(t,r).onsuccess=u}},e.getAll(t,r).onsuccess=c,e.getAllKeys(t,r).onsuccess=u):n?e.openCursor(t,"prev").onsuccess=l:e.openCursor(t).onsuccess=l}var tP=!1,tD=[];function tC(){!tP&&tD.length&&(tP=!0,tD.shift()())}var tI=new Map,tB=new Map;function tT(e,t){var n,r,i=this;n=function(t){!function(e,t,n){var r=t.name,i=null,a=null;function c(e){return function(t,n){t&&t instanceof Error&&!t.reason&&a&&(t.reason=a),e(t,n)}}function u(e,t){var n=e.objectStore(th);n.createIndex("deletedOrLocal","deletedOrLocal",{unique:!1}),n.openCursor().onsuccess=function(e){var r=e.target.result;if(r){var i=r.value,o=eI(i);i.deletedOrLocal=o?"1":"0",n.put(i),r.continue()}else t()}}function l(e,t){var n=e.objectStore(t_),r=e.objectStore(th),i=e.objectStore(tp);r.openCursor().onsuccess=function(e){var o=e.target.result;if(o){var s=o.value,a=s.id,c=eB(a),u=ew(s);if(c){var l=a+"::"+u,f=i.index("_doc_id_rev"),d=IDBKeyRange.bound(a+"::",a+"::~",!1,!1),h=f.openCursor(d);h.onsuccess=function(e){if(h=e.target.result){var t=h.value;t._doc_id_rev===l&&n.put(t),i.delete(h.primaryKey),h.continue()}else r.delete(o.primaryKey),o.continue()}}else o.continue()}else t&&t()}}function f(e,t){var n=e.objectStore(tp),r=e.objectStore(tv),i=e.objectStore(ty);r.count().onsuccess=function(e){if(!e.target.result)return t();n.openCursor().onsuccess=function(e){var n=e.target.result;if(!n)return t();for(var r=n.value,o=n.primaryKey,s=Object.keys(r._attachments||{}),a={},c=0;c<s.length;c++)a[r._attachments[s[c]].digest]=!0;var u=Object.keys(a);for(c=0;c<u.length;c++){var l=u[c];i.put({seq:o,digestSeq:l+"::"+o})}n.continue()}}}function d(e){var t=e.objectStore(tp),n=e.objectStore(th);n.openCursor().onsuccess=function(e){var r=e.target.result;if(r){var i,o,s,a,c,u=(c=r.value).data?tk(c):(c.deleted="1"===c.deletedOrLocal,c);if(u.winningRev=u.winningRev||ew(u),u.seq)return l();i=u.id+"::",o=u.id+"::￿",s=t.index("_doc_id_rev").openCursor(IDBKeyRange.bound(i,o)),a=0,s.onsuccess=function(e){var t=e.target.result;if(!t)return u.seq=a,l();var n=t.primaryKey;n>a&&(a=n),t.continue()}}function l(){var e=tw(u,u.winningRev,u.deleted);n.put(e).onsuccess=function(){r.continue()}}}}e._meta=null,e._remote=!1,e.type=function(){return"idb"},e._id=k(function(t){t(null,e._meta.instanceId)}),e._bulkDocs=function(n,r,o){!function(e,t,n,r,i,o){for(var s,a,c,u,l,f,d,h,p=t.docs,v=0,y=p.length;v<y;v++){var g=p[v];g._id&&eB(g._id)||(g=p[v]=td(g,n.new_edits,e)).error&&!d&&(d=g)}if(d)return o(d);var _=!1,m=0,b=Array(p.length),w=new Map,k=!1;function $(){_=!0,j()}function j(){h&&_&&(h.docCount+=m,f.put(h))}function O(){k||(tA.notify(r._meta.name),o(null,b))}function q(e,t,n,r,i,o,s,a){e.metadata.winningRev=t,e.metadata.deleted=n;var c=e.data;if(c._id=e.metadata.id,c._rev=e.metadata.rev,r&&(c._deleted=!0),c._attachments&&Object.keys(c._attachments).length)return function(e,t,n,r,i,o){var s=e.data,a=0,c=Object.keys(s._attachments);function l(){a===c.length&&x(e,t,n,r,i,o)}function f(){a++,l()}c.forEach(function(n){var r=e.data._attachments[n];if(r.stub)a++,l();else{var i,o,s,c=r.data;delete r.data,r.revpos=parseInt(t,10),i=r.digest,o=c,s=f,u.count(i).onsuccess=function(e){if(e.target.result)return s();u.put({digest:i,body:o}).onsuccess=s}}})}(e,t,n,i,s,a);m+=o,j(),x(e,t,n,i,s,a)}function x(e,t,n,i,o,u){var f=e.data,d=e.metadata;function h(o){var c,u,l=e.stemmedRevs||[];i&&r.auto_compaction&&(l=l.concat((c=e.metadata,u=[],ek(c.rev_tree,function(e,t,n,r,i){"available"!==i.status||e||(u.push(t+"-"+n),i.status="missing")}),u))),l&&l.length&&tx(l,e.metadata.id,s),d.seq=o.target.result;var f=tw(d,t,n);a.put(f).onsuccess=p}function p(){b[o]={ok:!0,id:d.id,rev:d.rev},w.set(e.metadata.id,e.metadata),function(e,t,n){var r=0,i=Object.keys(e.data._attachments||{});if(!i.length)return n();function o(){++r===i.length&&n()}for(var s=0;s<i.length;s++)!function(n){var r=e.data._attachments[n].digest,i=l.put({seq:t,digestSeq:r+"::"+t});i.onsuccess=o,i.onerror=function(e){e.preventDefault(),e.stopPropagation(),o()}}(i[s])}(e,d.seq,u)}f._doc_id_rev=d.id+"::"+d.rev,delete f._id,delete f._rev;var v=c.put(f);v.onsuccess=h,v.onerror=function(e){e.preventDefault(),e.stopPropagation(),c.index("_doc_id_rev").getKey(f._doc_id_rev).onsuccess=function(e){c.put(f,e.target.result).onsuccess=h}}}!function(e,t,n){if(!e.length)return n();var r,i=0;function o(){i++,e.length===i&&(r?n(r):n())}e.forEach(function(e){var n=e.data&&e.data._attachments?Object.keys(e.data._attachments):[],i=0;if(!n.length)return o();function s(e){r=e,++i===n.length&&o()}for(var a in e.data._attachments)Object.prototype.hasOwnProperty.call(e.data._attachments,a)&&function(e,t,n){if(e.stub)return n();if("string"==typeof e.data)!function(e,t,n){var r=function(e){try{return eu(e)}catch(e){return{error:W(F,"Attachment is not a valid base64 string")}}}(e.data);if(r.error)return n(r.error);e.length=r.length,"blob"===t?e.data=ed(r,e.content_type):"base64"===t?e.data=el(r):e.data=r,em(r,function(t){e.digest="md5-"+t,n()})}(e,t,n);else em(e.data,function(r){e.digest="md5-"+r,e.length=e.data.size||e.data.length||0,"binary"===t?ep(e.data,function(t){e.data=t,n()}):"base64"===t?ev(e.data,function(t){e.data=t,n()}):n()})}(e.data._attachments[a],t,s)})}(p,r._meta.blobSupport?"blob":"base64",function(t){if(t)return o(t);!function(){var t=tS(i,[th,tp,tv,t_,ty,tg],"readwrite");if(t.error)return o(t.error);(s=t.txn).onabort=tb(o),s.ontimeout=tb(o),s.oncomplete=O,a=s.objectStore(th),c=s.objectStore(tp),u=s.objectStore(tv),l=s.objectStore(ty),(f=s.objectStore(tg)).get(tg).onsuccess=function(e){h=e.target.result,j()},function(e){var t,n=[];if(p.forEach(function(e){e.data&&e.data._attachments&&Object.keys(e.data._attachments).forEach(function(t){var r=e.data._attachments[t];r.stub&&n.push(r.digest)})}),!n.length)return e();var r=0;n.forEach(function(i){var o;o=function(i){i&&!t&&(t=i),++r===n.length&&e(t)},u.get(i).onsuccess=function(e){if(e.target.result)o();else{var t=W(G,"unknown stub attachment with digest "+i);t.status=412,o(t)}}})}(function(t){if(t)return k=!0,o(t);!function(){if(p.length)for(var t=0,i=0,o=p.length;i<o;i++){var c=p[i];if(c._id&&eB(c._id)){u();continue}a.get(c.metadata.id).onsuccess=l}function u(){++t===p.length&&function(e,t,n,r,i,o,s,a,c){e=e||1e3;var u=a.new_edits,l=new Map,f=0,d=t.length;function h(){++f===d&&c&&c()}t.forEach(function(e,t){if(e._id&&eB(e._id))return void n[e._deleted?"_removeLocal":"_putLocal"](e,{ctx:i},function(e,n){o[t]=e||n,h()});var r=e.metadata.id;l.has(r)?(d--,l.get(r).push([e,t])):l.set(r,[[e,t]])}),l.forEach(function(t,n){var i=0;function c(){++i<t.length?l():h()}function l(){var l=t[i],f=l[0],d=l[1];if(r.has(n))!function(e,t,n,r,i,o,s,a){if(function(e,t){for(var n,r=e.slice(),i=t.split("-"),o=parseInt(i[0],10),s=i[1];n=r.pop();){if(n.pos===o&&n.ids[0]===s)return!0;for(var a=n.ids[2],c=0,u=a.length;c<u;c++)r.push({pos:n.pos+1,ids:a[c]})}return!1}(t.rev_tree,n.metadata.rev)&&!a)return r[i]=n,o();var c,u=t.winningRev||ew(t),l="deleted"in t?t.deleted:eI(t,u),f="deleted"in n.metadata?n.metadata.deleted:eI(n.metadata),d=/^1-/.test(n.metadata.rev);if(l&&!f&&a&&d){var h=n.data;h._rev=u,h._id=n.metadata.id,n=td(h,a)}var p=eD(t.rev_tree,n.metadata.rev_tree[0],e);if(a&&(l&&f&&"new_leaf"!==p.conflicts||!l&&"new_leaf"!==p.conflicts||l&&!f&&"new_branch"===p.conflicts)){var v=W(T);return r[i]=v,o()}var y=n.metadata.rev;n.metadata.rev_tree=p.tree,n.stemmedRevs=p.stemmedRevs||[],t.rev_map&&(n.metadata.rev_map=t.rev_map);var g=ew(n.metadata),_=eI(n.metadata,g),m=l===_?0:l<_?-1:1;c=y===g?_:eI(n.metadata,y),s(n,g,_,c,!0,m,i,o)}(e,r.get(n),f,o,d,c,s,u);else{var h=eD([],f.metadata.rev_tree[0],e);f.metadata.rev_tree=h.tree,f.stemmedRevs=h.stemmedRevs||[],function(e,t,n){var r=ew(e.metadata),i=eI(e.metadata,r);if("was_delete"in a&&i)return o[t]=W(B,"deleted"),n();if(u&&"missing"===e.metadata.rev_tree[0].ids[1].status){var c=W(T);return o[t]=c,n()}var l=+!i;s(e,r,i,i,!1,l,t,n)}(f,d,c)}}l()})}(e.revs_limit,p,r,w,s,b,q,n,$)}function l(e){var t=tk(e.target.result);t&&w.set(t.id,t),u()}}()})}()})}(t,n,r,e,i,c(o))},e._get=function(e,t,n){var r,o,s,a=t.ctx;if(!a){var c=tS(i,[th,tp,tv],"readonly");if(c.error)return n(c.error);a=c.txn}function u(){n(s,{doc:r,metadata:o,ctx:a})}a.objectStore(th).get(e).onsuccess=function(e){if(!(o=tk(e.target.result)))return s=W(B,"missing"),u();if(t.rev)n=t.latest?function(e,t){for(var n,r=t.rev_tree.slice();n=r.pop();){var i=n.pos,o=n.ids,s=o[0],a=o[1],c=o[2],u=0===c.length,l=n.history?n.history.slice():[];if(l.push({id:s,pos:i,opts:a}),u)for(var f=0,d=l.length;f<d;f++){var h=l[f];if(h.pos+"-"+h.id===e)return i+"-"+s}for(var p=0,v=c.length;p<v;p++)r.push({pos:i+1,ids:c[p],history:l})}throw Error("Unable to resolve latest revision for id "+t.id+", rev "+e)}(t.rev,o):t.rev;else{var n=o.winningRev;if(eI(o))return s=W(B,"deleted"),u()}var i=a.objectStore(tp),c=o.id+"::"+n;i.index("_doc_id_rev").get(c).onsuccess=function(e){if((r=e.target.result)&&(r=t$(r)),!r)return s=W(B,"missing"),u();u()}}},e._getAttachment=function(e,t,n,r,o){if(r.ctx)s=r.ctx;else{var s,a=tS(i,[th,tp,tv],"readonly");if(a.error)return o(a.error);s=a.txn}var c=n.digest,u=n.content_type;s.objectStore(tv).get(c).onsuccess=function(e){tj(e.target.result.body,u,r.binary,function(e){o(null,e)})}},e._info=function(t){var n,r,o=tS(i,[tg,tp],"readonly");if(o.error)return t(o.error);var s=o.txn;s.objectStore(tg).get(tg).onsuccess=function(e){r=e.target.result.docCount},s.objectStore(tp).openKeyCursor(null,"prev").onsuccess=function(e){var t=e.target.result;n=t?t.key:0},s.oncomplete=function(){t(null,{doc_count:r,update_seq:n,idb_attachment_format:e._meta.blobSupport?"binary":"base64"})}},e._allDocs=function(e,t){!function(e,t,n){var r,i,o,s,a,c,u="startkey"in e&&e.startkey,l="endkey"in e&&e.endkey,f="key"in e&&e.key,d="keys"in e&&e.keys,h=e.skip||0,p="number"==typeof e.limit?e.limit:-1,v=!1!==e.inclusive_end;if(!d&&(s=(o=function(e,t,n,r,i){try{if(e&&t)if(i)return IDBKeyRange.bound(t,e,!n,!1);else return IDBKeyRange.bound(e,t,!1,!n);if(e)if(i)return IDBKeyRange.upperBound(e);else return IDBKeyRange.lowerBound(e);if(t)if(i)return IDBKeyRange.lowerBound(t,!n);else return IDBKeyRange.upperBound(t,!n);else if(r)return IDBKeyRange.only(r)}catch(e){return{error:e}}return null}(u,l,v,f,e.descending))&&o.error)&&("DataError"!==s.name||0!==s.code))return n(W(V,s.name,s.message));var y=[th,tp,tg];e.attachments&&y.push(tv);var g=tS(t,y,"readonly");if(g.error)return n(g.error);var _=g.txn;_.oncomplete=function(){e.attachments?tq($,e.binary).then(q):q()},_.onabort=tb(n);var m=_.objectStore(th),b=_.objectStore(tp),w=_.objectStore(tg),k=b.index("_doc_id_rev"),$=[];function j(t){for(var n=0,r=t.length;n<r&&$.length!==p;n++){var i=t[n];if(i.error&&d){$.push(i);continue}var o=tk(i);!function(t,n){var r,i={id:n.id,key:n.id,value:{rev:t}};n.deleted?d&&($.push(i),i.value.deleted=!0,i.doc=null):h--<=0&&($.push(i),e.include_docs&&(r=n.id+"::"+t,k.get(r).onsuccess=function(t){if(i.doc=t$(t.target.result)||{},e.conflicts){var r=eO(n);r.length&&(i.doc._conflicts=r)}tO(i.doc,e,_)}))}(o.winningRev,o)}}function O(e,t,n){n&&(j(t),$.length<p&&n.continue())}function q(){var t={total_rows:a,offset:e.skip,rows:$};e.update_seq&&void 0!==c&&(t.update_seq=c),n(null,t)}if(w.get(tg).onsuccess=function(e){a=e.target.result.docCount},e.update_seq&&(b.openKeyCursor(null,"prev").onsuccess=e=>{var t=e.target.result;t&&t.key&&(c=t.key)}),!s&&0!==p){if(d)return r=Array(d.length),i=0,d.forEach(function(e,t){m.get(e).onsuccess=function(n){n.target.result?r[t]=n.target.result:r[t]={key:e,error:"not_found"},++i===d.length&&O(d,r,{})}});if(-1===p){var x=o,S=function(t){var n=t.target.result;e.descending&&(n=n.reverse()),j(n)};if("function"==typeof m.getAll){m.getAll(x).onsuccess=S;return}var A=[];m.openCursor(x).onsuccess=function(e){var t=e.target.result;t?(A.push(t.value),t.continue()):S({target:{result:A}})};return}tE(m,o,e.descending,p+h,O)}}(e,i,c(t))},e._changes=function(t){return function(e,t,n,r){if((e=b(e)).continuous){var i,o,s,a,c=n+":"+p();return tA.addListener(n,c,t,e),tA.notify(n),{cancel:function(){tA.removeListener(n,c)}}}var u=e.doc_ids&&new Set(e.doc_ids);e.since=e.since||0;var l=e.since,f="limit"in e?e.limit:-1;0===f&&(f=1);var d=[],h=0,v=Y(e),y=new Map;function g(e,t,n,r){if(n.seq!==t)return r();if(n.winningRev===e._rev)return r(n,e);var i=e._id+"::"+n.winningRev;a.get(i).onsuccess=function(e){r(n,t$(e.target.result))}}function _(){e.complete(null,{results:d,last_seq:l})}var m=[th,tp];e.attachments&&m.push(tv);var w=tS(r,m,"readonly");if(w.error)return e.complete(w.error);(i=w.txn).onabort=tb(e.complete),i.oncomplete=function(){!e.continuous&&e.attachments?tq(d).then(_):_()},o=i.objectStore(tp),s=i.objectStore(th),a=o.index("_doc_id_rev"),tE(o,e.since&&!e.descending?IDBKeyRange.lowerBound(e.since,!0):null,e.descending,f,function(t,n,r){if(r&&t.length){var o=Array(t.length),a=Array(t.length),c=0;n.forEach(function(n,p){!function(e,t,n){if(u&&!u.has(e._id))return n();var r=y.get(e._id);if(r)return g(e,t,r,n);s.get(e._id).onsuccess=function(i){r=tk(i.target.result),y.set(e._id,r),g(e,t,r,n)}}(t$(n),t[p],function(n,s){a[p]=n,o[p]=s,++c===t.length&&function(){for(var t=[],n=0,s=o.length;n<s&&h!==f;n++){var c=o[n];if(c){var u=a[n];t.push(function(t,n){var r=e.processChange(n,t,e);l=r.seq=t.seq;var o=v(r);return"object"==typeof o?Promise.reject(o):o?(h++,e.return_docs&&d.push(r),e.attachments&&e.include_docs)?new Promise(function(t){tO(n,e,i,function(){tq([r],e.binary).then(function(){t(r)})})}):Promise.resolve(r):Promise.resolve()}(u,c))}}Promise.all(t).then(function(t){for(var n=0,r=t.length;n<r;n++)t[n]&&e.onChange(t[n])}).catch(e.complete),h!==f&&r.continue()}()})})}})}(t,e,r,i)},e._close=function(e){i.close(),tI.delete(r),e()},e._getRevisionTree=function(e,t){var n=tS(i,[th],"readonly");if(n.error)return t(n.error);n.txn.objectStore(th).get(e).onsuccess=function(e){var n=tk(e.target.result);n?t(null,n.rev_tree):t(W(B))}},e._doCompaction=function(e,t,n){var r=tS(i,[th,tp,tv,ty],"readwrite");if(r.error)return n(r.error);var o=r.txn;o.objectStore(th).get(e).onsuccess=function(n){var r=tk(n.target.result);ek(r.rev_tree,function(e,n,r,i,o){-1!==t.indexOf(n+"-"+r)&&(o.status="missing")}),tx(t,e,o);var i=r.winningRev,s=r.deleted;o.objectStore(th).put(tw(r,i,s))},o.onabort=tb(n),o.oncomplete=function(){n()}},e._getLocal=function(e,t){var n=tS(i,[t_],"readonly");if(n.error)return t(n.error);var r=n.txn.objectStore(t_).get(e);r.onerror=tb(t),r.onsuccess=function(e){var n=e.target.result;n?(delete n._doc_id_rev,t(null,n)):t(W(B))}},e._putLocal=function(e,t,n){"function"==typeof t&&(n=t,t={}),delete e._revisions;var r,o,s=e._rev,a=e._id;s?e._rev="0-"+(parseInt(s.split("-")[1],10)+1):e._rev="0-1";var c=t.ctx;if(!c){var u=tS(i,[t_],"readwrite");if(u.error)return n(u.error);(c=u.txn).onerror=tb(n),c.oncomplete=function(){r&&n(null,r)}}var l=c.objectStore(t_);s?(o=l.get(a)).onsuccess=function(i){var o=i.target.result;o&&o._rev===s?l.put(e).onsuccess=function(){r={ok:!0,id:e._id,rev:e._rev},t.ctx&&n(null,r)}:n(W(T))}:((o=l.add(e)).onerror=function(e){n(W(T)),e.preventDefault(),e.stopPropagation()},o.onsuccess=function(){r={ok:!0,id:e._id,rev:e._rev},t.ctx&&n(null,r)})},e._removeLocal=function(e,t,n){"function"==typeof t&&(n=t,t={});var r,o=t.ctx;if(!o){var s=tS(i,[t_],"readwrite");if(s.error)return n(s.error);(o=s.txn).oncomplete=function(){r&&n(null,r)}}var a=e._id,c=o.objectStore(t_),u=c.get(a);u.onerror=tb(n),u.onsuccess=function(i){var o=i.target.result;o&&o._rev===e._rev?(c.delete(a),r={ok:!0,id:a,rev:"0-0"},t.ctx&&n(null,r)):n(W(B))}},e._destroy=function(e,t){tA.removeAllListeners(r);var n=tB.get(r);n&&n.result&&(n.result.close(),tI.delete(r));var i=indexedDB.deleteDatabase(r);i.onsuccess=function(){tB.delete(r),o&&r in localStorage&&delete localStorage[r],t(null,{ok:!0})},i.onerror=tb(t)};var h=tI.get(r);if(h)return i=h.idb,e._meta=h.global,S(function(){n(null,e)});var v=indexedDB.open(r,5);tB.set(r,v),v.onupgradeneeded=function(e){var t,n,r,i=e.target.result;if(e.oldVersion<1)return t=i.createObjectStore(th,{keyPath:"id"}),i.createObjectStore(tp,{autoIncrement:!0}).createIndex("_doc_id_rev","_doc_id_rev",{unique:!0}),i.createObjectStore(tv,{keyPath:"digest"}),i.createObjectStore(tg,{keyPath:"id",autoIncrement:!1}),i.createObjectStore(tm),t.createIndex("deletedOrLocal","deletedOrLocal",{unique:!1}),i.createObjectStore(t_,{keyPath:"_id"}),void((n=i.createObjectStore(ty,{autoIncrement:!0})).createIndex("seq","seq"),n.createIndex("digestSeq","digestSeq",{unique:!0}));var o=e.currentTarget.transaction;e.oldVersion<3&&i.createObjectStore(t_,{keyPath:"_id"}).createIndex("_doc_id_rev","_doc_id_rev",{unique:!0}),e.oldVersion<4&&((r=i.createObjectStore(ty,{autoIncrement:!0})).createIndex("seq","seq"),r.createIndex("digestSeq","digestSeq",{unique:!0}));var s=[u,l,f,d],a=e.oldVersion;!function e(){var t=s[a-1];a++,t&&t(o,e)}()},v.onsuccess=function(t){(i=t.target.result).onversionchange=function(){i.close(),tI.delete(r)},i.onabort=function(e){E("error","Database has a global failure",e.target.error),a=e.target.error,i.close(),tI.delete(r)};var o,c,u,l,f,d=i.transaction([tg,tm,th],"readwrite"),h=!1;function v(){void 0!==l&&h&&(e._meta={name:r,instanceId:f,blobSupport:l},tI.set(r,{idb:i,global:e._meta}),n(null,e))}function y(){if(void 0!==u&&void 0!==c){var e=r+"_id";e in c?f=c[e]:c[e]=f=p(),c.docCount=u,d.objectStore(tg).put(c)}}d.objectStore(tg).get(tg).onsuccess=function(e){c=e.target.result||{id:tg},y()},o=function(e){u=e,y()},d.objectStore(th).index("deletedOrLocal").count(IDBKeyRange.only("0")).onsuccess=function(e){o(e.target.result)},s||(s=new Promise(function(e){let t;var n=ef([""]);(t=d.objectStore(tm).put(n,"key")).onsuccess=function(){var t=navigator.userAgent.match(/Chrome\/(\d+)/);e(navigator.userAgent.match(/Edge\//)||!t||parseInt(t[1],10)>=43)},t.onerror=d.onabort=function(t){t.preventDefault(),t.stopPropagation(),e(!1)}}).catch(function(){return!1})),s.then(function(e){l=e,v()}),d.oncomplete=function(){h=!0,v()},d.onabort=tb(n)},v.onerror=function(e){var t=e.target.error&&e.target.error.message;t?-1!==t.indexOf("stored database is a higher version")&&(t=Error('This DB was created with the newer "indexeddb" adapter, but you are trying to open it with the older "idb" adapter')):t="Failed to open indexedDB, are you in private browsing mode?",E("error",t),n(W(V,t))}}(i,e,t)},r=i.constructor,tD.push(function(){n(function(e,n){try{t(e,n)}catch(e){r.emit("error",e)}tP=!1,S(function(){tC(r)})})}),tC()}tT.valid=function(){try{return"undefined"!=typeof indexedDB&&"undefined"!=typeof IDBKeyRange}catch(e){return!1}};let tM={};function tL(e){let t=e.doc||e.ok,n=t&&t._attachments;n&&Object.keys(n).forEach(function(e){var t,r;let i=n[e];t=i.data,r=i.content_type,i.data=ed(eu(t),r)})}function tR(e){return/^_design/.test(e)?"_design/"+encodeURIComponent(e.slice(8)):e.startsWith("_local/")?"_local/"+encodeURIComponent(e.slice(7)):encodeURIComponent(e)}function tN(e){return e._attachments&&Object.keys(e._attachments)?Promise.all(Object.keys(e._attachments).map(function(t){let n=e._attachments[t];if(n.data&&"string"!=typeof n.data)return new Promise(function(e){ev(n.data,e)}).then(function(e){n.data=e})})):Promise.resolve()}function tF(e,t){return tJ(e,e.db+"/"+t)}function tJ(e,t){let n=e.path?"/":"";return e.protocol+"://"+e.host+(e.port?":"+e.port:"")+"/"+e.path+n+t}function tz(e){let t=Object.keys(e);return 0===t.length?"":"?"+t.map(t=>t+"="+encodeURIComponent(e[t])).join("&")}function tK(e,t){let n,r=this,i=function(e,t){if(function(e){if(!e.prefix)return!1;let t=es(e.prefix).protocol;return"http"===t||"https"===t}(t)){let n=t.name.substr(t.prefix.length);e=t.prefix.replace(/\/?$/,"/")+encodeURIComponent(n)}let n=es(e);(n.user||n.password)&&(n.auth={username:n.user,password:n.password});let r=n.path.replace(/(^\/|\/$)/g,"").split("/");return n.db=r.pop(),-1===n.db.indexOf("%")&&(n.db=encodeURIComponent(n.db)),n.path=r.join("/"),n}(e.name,e),o=tF(i,"");e=b(e);let s=async function(t,n){if((n=n||{}).headers=n.headers||new eW,n.credentials="include",e.auth||i.auth){let t=e.auth||i.auth,r=el(unescape(encodeURIComponent(t.username+":"+t.password)));n.headers.set("Authorization","Basic "+r)}let r=e.headers||{};Object.keys(r).forEach(function(e){n.headers.append(e,r[e])}),function(e){let t="undefined"!=typeof navigator&&navigator.userAgent?navigator.userAgent.toLowerCase():"",n=-1!==t.indexOf("msie"),r=-1!==t.indexOf("trident"),i=-1!==t.indexOf("edge"),o=!("method"in e)||"GET"===e.method;return(n||r||i)&&o}(n)&&(t+=(-1===t.indexOf("?")?"?":"&")+"_nonce="+Date.now());let o=e.fetch||eG;return await o(t,n)};function a(e,t){return $(e,function(...e){u().then(function(){return t.apply(this,e)}).catch(function(t){e.pop()(t)})}).bind(r)}async function c(e,t){let n={};(t=t||{}).headers=t.headers||new eW,t.headers.get("Content-Type")||t.headers.set("Content-Type","application/json"),t.headers.get("Accept")||t.headers.set("Accept","application/json");let r=await s(e,t);if(n.ok=r.ok,n.status=r.status,n.data=await r.json(),!n.ok)throw n.data.status=n.status,X(n.data);return Array.isArray(n.data)&&(n.data=n.data.map(function(e){return e.error||e.missing?X(e):e})),n}async function u(){return e.skip_setup?Promise.resolve():(n||(n=c(o).catch(function(e){return e&&e.status&&404===e.status?(D(404,"PouchDB is just detecting if the remote exists."),c(o,{method:"PUT"})):Promise.reject(e)}).catch(function(e){return!!e&&!!e.status&&412===e.status||Promise.reject(e)})).catch(function(){n=null}),n)}function l(e){return e.split("/").map(encodeURIComponent).join("/")}S(function(){t(null,r)}),r._remote=!0,r.type=function(){return"http"},r.id=a("id",async function(e){let t;try{let e=await s(tJ(i,""));t=await e.json()}catch(e){t={}}e(null,t&&t.uuid?t.uuid+i.db:tF(i,""))}),r.compact=a("compact",async function(e,t){"function"==typeof e&&(t=e,e={}),e=b(e),await c(tF(i,"_compact"),{method:"POST"}),!function n(){r.info(function(r,i){i&&!i.compact_running?t(null,{ok:!0}):setTimeout(n,e.interval||200)})}()}),r.bulkGet=$("bulkGet",function(e,t){let n=this;async function r(t){let n={};e.revs&&(n.revs=!0),e.attachments&&(n.attachments=!0),e.latest&&(n.latest=!0);try{let r=await c(tF(i,"_bulk_get"+tz(n)),{method:"POST",body:JSON.stringify({docs:e.docs})});e.attachments&&e.binary&&r.data.results.forEach(function(e){e.docs.forEach(tL)}),t(null,r.data)}catch(e){t(e)}}function o(){let r=Math.ceil(e.docs.length/50),i=0,o=Array(r);for(let s=0;s<r;s++){let a=j(e,["revs","attachments","binary","latest"]);a.docs=e.docs.slice(50*s,Math.min(e.docs.length,(s+1)*50)),x(n,a,function(e){return function(n,s){o[e]=s.results,++i===r&&t(null,{results:o.flat()})}}(s))}}let s=tJ(i,""),a=tM[s];"boolean"!=typeof a?r(function(e,n){e?(tM[s]=!1,D(e.status,"PouchDB is just detecting if the remote supports the _bulk_get API."),o()):(tM[s]=!0,t(null,n))}):a?r(t):o()}),r._info=async function(e){try{await u();let t=await s(tF(i,"")),n=await t.json();n.host=tF(i,""),e(null,n)}catch(t){e(t)}},r.fetch=async function(e,t){return await u(),s("/"===e.substring(0,1)?tJ(i,e.substring(1)):tF(i,e),t)},r.get=a("get",async function(e,t,n){"function"==typeof t&&(n=t,t={});let r={};function o(e){var n;let r=e._attachments,o=r&&Object.keys(r);if(r&&o.length)return n=o.map(function(e){return function(){return a(e)}}),new Promise(function(e,t){var r,i=0,o=0,s=0,a=n.length;function c(){++s===a?r?t(r):e():f()}function u(){i--,c()}function l(e){i--,r=r||e,c()}function f(){for(;i<5&&o<a;)i++,n[o++]().then(u,l)}f()});async function a(n){let o,a,c=r[n],u=tR(e._id)+"/"+l(n)+"?rev="+e._rev,f=await s(tF(i,u));if(o="buffer"in f?await f.buffer():await f.blob(),t.binary){let e=Object.getOwnPropertyDescriptor(o.__proto__,"type");(!e||e.set)&&(o.type=c.content_type),a=o}else a=await new Promise(function(e){ev(o,e)});delete c.stub,delete c.length,c.data=a}}(t=b(t)).revs&&(r.revs=!0),t.revs_info&&(r.revs_info=!0),t.latest&&(r.latest=!0),t.open_revs&&("all"!==t.open_revs&&(t.open_revs=JSON.stringify(t.open_revs)),r.open_revs=t.open_revs),t.rev&&(r.rev=t.rev),t.conflicts&&(r.conflicts=t.conflicts),t.update_seq&&(r.update_seq=t.update_seq);let a=tF(i,(e=tR(e))+tz(r));try{var u;let e=await c(a);t.attachments&&await (u=e.data,Array.isArray(u)?Promise.all(u.map(function(e){if(e.ok)return o(e.ok)})):o(u)),n(null,e.data)}catch(t){t.docId=e,n(t)}}),r.remove=a("remove",async function(e,t,n,r){let o;"string"==typeof t?(o={_id:e,_rev:t},"function"==typeof n&&(r=n,n={})):(o=e,"function"==typeof t?(r=t,n={}):(r=n,n=t));let s=o._rev||n.rev,a=tF(i,tR(o._id))+"?rev="+s;try{let e=await c(a,{method:"DELETE"});r(null,e.data)}catch(e){r(e)}}),r.getAttachment=a("getAttachment",async function(e,t,n,r){let o;"function"==typeof n&&(r=n,n={});let a=n.rev?"?rev="+n.rev:"",c=tF(i,tR(e))+"/"+l(t)+a;try{let e,t=await s(c,{method:"GET"});if(!t.ok)throw t;if(o=t.headers.get("content-type"),e="undefined"==typeof process||process.browser||"function"!=typeof t.buffer?await t.blob():await t.buffer(),"undefined"!=typeof process&&!process.browser){let t=Object.getOwnPropertyDescriptor(e.__proto__,"type");(!t||t.set)&&(e.type=o)}r(null,e)}catch(e){r(e)}}),r.removeAttachment=a("removeAttachment",async function(e,t,n,r){let o=tF(i,tR(e)+"/"+l(t))+"?rev="+n;try{let e=await c(o,{method:"DELETE"});r(null,e.data)}catch(e){r(e)}}),r.putAttachment=a("putAttachment",async function(e,t,n,r,o,s){"function"==typeof o&&(s=o,o=r,r=n,n=null);let a=tF(i,tR(e)+"/"+l(t));if(n&&(a+="?rev="+n),"string"==typeof r){let e;try{e=eu(r)}catch(e){return s(W(F,"Attachment is not a valid base64 string"))}r=e?ed(e,o):""}try{let e=await c(a,{headers:new eW({"Content-Type":o}),method:"PUT",body:r});s(null,e.data)}catch(e){s(e)}}),r._bulkDocs=async function(e,t,n){e.new_edits=t.new_edits;try{await u(),await Promise.all(e.docs.map(tN));let t=await c(tF(i,"_bulk_docs"),{method:"POST",body:JSON.stringify(e)});n(null,t.data)}catch(e){n(e)}},r._put=async function(e,t,n){try{await u(),await tN(e);let t=await c(tF(i,tR(e._id)),{method:"PUT",body:JSON.stringify(e)});n(null,t.data)}catch(t){t.docId=e&&e._id,n(t)}},r.allDocs=a("allDocs",async function(e,t){let n;"function"==typeof e&&(t=e,e={});let r={},o="GET";(e=b(e)).conflicts&&(r.conflicts=!0),e.update_seq&&(r.update_seq=!0),e.descending&&(r.descending=!0),e.include_docs&&(r.include_docs=!0),e.attachments&&(r.attachments=!0),e.key&&(r.key=JSON.stringify(e.key)),e.start_key&&(e.startkey=e.start_key),e.startkey&&(r.startkey=JSON.stringify(e.startkey)),e.end_key&&(e.endkey=e.end_key),e.endkey&&(r.endkey=JSON.stringify(e.endkey)),void 0!==e.inclusive_end&&(r.inclusive_end=!!e.inclusive_end),void 0!==e.limit&&(r.limit=e.limit),void 0!==e.skip&&(r.skip=e.skip);let s=tz(r);void 0!==e.keys&&(o="POST",n={keys:e.keys});try{let r=await c(tF(i,"_all_docs"+s),{method:o,body:JSON.stringify(n)});e.include_docs&&e.attachments&&e.binary&&r.data.rows.forEach(tL),t(null,r.data)}catch(e){t(e)}}),r._changes=function(e){let t,n,r="batch_size"in e?e.batch_size:25;!(e=b(e)).continuous||"heartbeat"in e||(e.heartbeat=1e4);let o="timeout"in e?e.timeout:3e4;"timeout"in e&&e.timeout&&o-e.timeout<5e3&&(o=e.timeout+5e3),"heartbeat"in e&&e.heartbeat&&o-e.heartbeat<5e3&&(o=e.heartbeat+5e3);let s={};"timeout"in e&&e.timeout&&(s.timeout=e.timeout);let a=void 0!==e.limit&&e.limit,l=a;if(e.style&&(s.style=e.style),(e.include_docs||e.filter&&"function"==typeof e.filter)&&(s.include_docs=!0),e.attachments&&(s.attachments=!0),e.continuous&&(s.feed="longpoll"),e.seq_interval&&(s.seq_interval=e.seq_interval),e.conflicts&&(s.conflicts=!0),e.descending&&(s.descending=!0),e.update_seq&&(s.update_seq=!0),"heartbeat"in e&&e.heartbeat&&(s.heartbeat=e.heartbeat),e.filter&&"string"==typeof e.filter&&(s.filter=e.filter),e.view&&"string"==typeof e.view&&(s.filter="_view",s.view=e.view),e.query_params&&"object"==typeof e.query_params)for(let t in e.query_params)Object.prototype.hasOwnProperty.call(e.query_params,t)&&(s[t]=e.query_params[t]);let f="GET";e.doc_ids?(s.filter="_doc_ids",f="POST",t={doc_ids:e.doc_ids}):e.selector&&(s.filter="_selector",f="POST",t={selector:e.selector});let d=new AbortController,h=async function(o,h){if(e.aborted)return;s.since=o,"object"==typeof s.since&&(s.since=JSON.stringify(s.since)),e.descending?a&&(s.limit=l):s.limit=!a||l>r?r:l;let p=tF(i,"_changes"+tz(s)),v={signal:d.signal,method:f,body:JSON.stringify(t)};if(n=o,!e.aborted)try{await u();let e=await c(p,v);h(null,e.data)}catch(e){h(e)}},p={results:[]},v=function(t,i){if(e.aborted)return;let o=0;if(i&&i.results){o=i.results.length,p.last_seq=i.last_seq;let t=null,n=null;"number"==typeof i.pending&&(t=i.pending),("string"==typeof p.last_seq||"number"==typeof p.last_seq)&&(n=p.last_seq),e.query_params,i.results=i.results.filter(function(r){l--;let i=Y(e)(r);return i&&(e.include_docs&&e.attachments&&e.binary&&tL(r),e.return_docs&&p.results.push(r),e.onChange(r,t,n)),i})}else if(t){e.aborted=!0,e.complete(t);return}i&&i.last_seq&&(n=i.last_seq);let s=a&&l<=0||i&&o<r||e.descending;(!e.continuous||a&&l<=0)&&s?e.complete(null,p):S(function(){h(n,v)})};return h(e.since||0,v),{cancel:function(){e.aborted=!0,d.abort()}}},r.revsDiff=a("revsDiff",async function(e,t,n){"function"==typeof t&&(n=t,t={});try{let t=await c(tF(i,"_revs_diff"),{method:"POST",body:JSON.stringify(e)});n(null,t.data)}catch(e){n(e)}}),r._close=function(e){e()},r._destroy=async function(e,t){try{let e=await c(tF(i,""),{method:"DELETE"});t(null,e)}catch(e){404===e.status?t(null,{ok:!0}):t(e)}}}tK.valid=function(){return!0};class tU extends Error{constructor(e){super(),this.status=400,this.name="query_parse_error",this.message=e,this.error=!0;try{Error.captureStackTrace(this,tU)}catch(e){}}}class tV extends Error{constructor(e){super(),this.status=404,this.name="not_found",this.message=e,this.error=!0;try{Error.captureStackTrace(this,tV)}catch(e){}}}class tQ extends Error{constructor(e){super(),this.status=500,this.name="invalid_value",this.message=e,this.error=!0;try{Error.captureStackTrace(this,tQ)}catch(e){}}}function tG(e,t){return t&&e.then(function(e){S(function(){t(null,e)})},function(e){S(function(){t(e)})}),e}function tW(e,t){return function(){var n=arguments,r=this;return e.add(function(){return t.apply(r,n)})}}function tX(e){var t=new Set(e),n=Array(t.size),r=-1;return t.forEach(function(e){n[++r]=e}),n}function tY(e){var t=Array(e.size),n=-1;return e.forEach(function(e,r){t[++n]=r}),t}function tH(e){return new tQ("builtin "+e+" function requires map values to be numbers or number arrays")}function tZ(e){for(var t=0,n=0,r=e.length;n<r;n++){var i=e[n];if("number"!=typeof i)if(Array.isArray(i)){t="number"==typeof t?[t]:t;for(var o=0,s=i.length;o<s;o++){var a=i[o];if("number"!=typeof a)throw tH("_sum");void 0===t[o]?t.push(a):t[o]+=a}}else throw tH("_sum");else"number"==typeof t?t+=i:t[0]+=i}return t}var t0=E.bind(null,"log"),t1=Array.isArray,t4=JSON.parse;function t2(e,t){return ea("return ("+e.replace(/;\s*$/,"")+");",{emit:t,sum:tZ,log:t0,isArray:t1,toJSON:t4})}class t3{constructor(){this.promise=Promise.resolve()}add(e){return this.promise=this.promise.catch(()=>{}).then(()=>e()),this.promise}finish(){return this.promise}}function t5(e){if(!e)return"undefined";switch(typeof e){case"function":case"string":return e.toString();default:return JSON.stringify(e)}}async function t6(e,t,n,r,i,o){let s,a=t5(n)+t5(r)+"undefined";if(!i&&(s=e._cachedViews=e._cachedViews||{})[a])return s[a];let u=e.info().then(async function(u){let l,f=u.db_name+"-mrview-"+(i?"temp":c().hash(a));await ec(e,"_local/"+o,function(e){e.views=e.views||{};let n=t;-1===n.indexOf("/")&&(n=t+"/"+t);let r=e.views[n]=e.views[n]||{};if(!r[f])return r[f]=!0,e});let d=(await e.registerDependentDatabase(f)).db;d.auto_compaction=!0;let h={name:f,db:d,sourceDB:e,adapter:e.adapter,mapFun:n,reduceFun:r};try{l=await h.db.get("_local/lastSeq")}catch(e){if(404!==e.status)throw e}return h.seq=l?l.seq:0,s&&h.db.once("destroyed",function(){delete s[a]}),h});return s&&(s[a]=u),u}let t8={},t9=new t3;function t7(e){return -1===e.indexOf("/")?[e,e]:e.split("/")}function ne(e,t,n){try{e.emit("error",t)}catch(e){E("error","The user's map/reduce function threw an uncaught error.\nYou can debug this error by doing:\nmyDatabase.on('error', function (err) { debugger; });\nPlease double-check your map/reduce function."),E("error",t,n)}}var nt={_sum:function(e,t){return tZ(t)},_count:function(e,t){return t.length},_stats:function(e,t){return{sum:tZ(t),min:Math.min.apply(null,t),max:Math.max.apply(null,t),count:t.length,sumsqr:function(e){for(var t=0,n=0,r=e.length;n<r;n++){var i=e[n];t+=i*i}return t}(t)}}},nn=function(e,t,n,r){var i;function o(e,t){let n=e2(e.key,t.key);return 0!==n?n:e2(e.value,t.value)}function s(e,t,n){return(n=n||0,"number"==typeof t)?e.slice(n,t+n):n>0?e.slice(n):e}function a(e){let t=e.value;return t&&"object"==typeof t&&t._id||e.id}function c(e){return function(t){return e.include_docs&&e.attachments&&e.binary&&function(e){for(let r of e.rows){let e=r.doc&&r.doc._attachments;if(e)for(let r of Object.keys(e)){var t,n;let i=e[r];e[r].data=(t=i.data,n=i.content_type,ed(eu(t),n))}}}(t),t}}function u(e,t,n,r){let i=t[e];void 0!==i&&(r&&(i=encodeURIComponent(JSON.stringify(i))),n.push(e+"="+i))}function l(e){if(void 0!==e){let t=Number(e);return isNaN(t)||t!==parseInt(e,10)?e:t}}function f(e,t){let n=e.descending?"endkey":"startkey",r=e.descending?"startkey":"endkey";if(void 0!==e[n]&&void 0!==e[r]&&e2(e[n],e[r])>0)throw new tU("No rows can match your key range, reverse your start_key and end_key or set {descending : true}");if(t.reduce&&!1!==e.reduce){if(e.include_docs)throw new tU("{include_docs:true} is invalid for reduce");else if(e.keys&&e.keys.length>1&&!e.group&&!e.group_level)throw new tU("Multi-key fetches for reduce views must use {group: true}")}for(let t of["group_level","limit","skip"]){let n=function(e){if(e){if("number"!=typeof e)return new tU(`Invalid value for integer: "${e}"`);if(e<0)return new tU(`Invalid value for positive integer: "${e}"`)}}(e[t]);if(n)throw n}}async function d(e,t,n){let r,i,o=[],s="GET";if(u("reduce",n,o),u("include_docs",n,o),u("attachments",n,o),u("limit",n,o),u("descending",n,o),u("group",n,o),u("group_level",n,o),u("skip",n,o),u("stale",n,o),u("conflicts",n,o),u("startkey",n,o,!0),u("start_key",n,o,!0),u("endkey",n,o,!0),u("end_key",n,o,!0),u("inclusive_end",n,o),u("key",n,o,!0),u("update_seq",n,o),o=""===(o=o.join("&"))?"":"?"+o,void 0!==n.keys){let e=`keys=${encodeURIComponent(JSON.stringify(n.keys))}`;e.length+o.length+1<=2e3?o+=("?"===o[0]?"&":"?")+e:(s="POST","string"==typeof t?r={keys:n.keys}:t.keys=n.keys)}if("string"==typeof t){let a=t7(t),u=await e.fetch("_design/"+a[0]+"/_view/"+a[1]+o,{headers:new eW({"Content-Type":"application/json"}),method:s,body:JSON.stringify(r)});i=u.ok;let l=await u.json();if(!i)throw l.status=u.status,X(l);for(let e of l.rows)if(e.value&&e.value.error&&"builtin_reduce_error"===e.value.error)throw Error(e.reason);return new Promise(function(e){e(l)}).then(c(n))}for(let e of(r=r||{},Object.keys(t)))Array.isArray(t[e])?r[e]=t[e]:r[e]=t[e].toString();let a=await e.fetch("_temp_view"+o,{headers:new eW({"Content-Type":"application/json"}),method:"POST",body:JSON.stringify(r)});i=a.ok;let l=await a.json();if(!i)throw l.status=a.status,X(l);return new Promise(function(e){e(l)}).then(c(n))}function h(e){return function(t){if(404===t.status)return e;throw t}}async function p(e,t,n){let r="_local/doc_"+e,i={_id:r,keys:[]},o=n.get(e),s=o[0],a=o[1],c=await (1===a.length&&/^1-/.test(a[0].rev)?Promise.resolve(i):t.db.get(r).catch(h(i))),u=await (!c.keys.length?Promise.resolve({rows:[]}):t.db.allDocs({keys:c.keys,include_docs:!0}));return function(e,t){let n=[],r=new Set;for(let e of t.rows){let t=e.doc;if(t&&(n.push(t),r.add(t._id),t._deleted=!s.has(t._id),!t._deleted)){let e=s.get(t._id);"value"in e&&(t.value=e.value)}}let i=tY(s);for(let e of i)if(!r.has(e)){let t={_id:e},r=s.get(e);"value"in r&&(t.value=r.value),n.push(t)}return e.keys=tX(i.concat(e.keys)),n.push(e),n}(c,u)}function v(e){let t="string"==typeof e?e:e.name,n=t8[t];return n||(n=t8[t]=new t3),n}async function y(e,t){return tW(v(e),function(){return g(e,t)})()}async function g(e,n){let r,i,s,a=t(e.mapFun,function(e,t){let n={id:i._id,key:e3(e)};null!=t&&(n.value=e3(t)),r.push(n)}),c=e.seq||0,u=0,l={view:e.name,indexed_docs:u};e.sourceDB.emit("indexing",l);let f=new t3;async function d(){return function(t,l){var v;let y=t.results;if(!y.length&&!l.length)return;for(let e of l)if(0>y.findIndex(function(t){return t.id===e.docId})){let t={_id:e.docId,doc:{_id:e.docId,_deleted:1},changes:[]};e.doc&&(t.doc=e.doc,t.changes.push({rev:e.doc._rev})),y.push(t)}let g=function(t){let n=new Map;for(let s of t){if("_"!==s.doc._id[0]){r=[],(i=s.doc)._deleted||function(e,t,n){try{t(n)}catch(r){ne(e,r,{fun:t,doc:n})}}(e.sourceDB,a,i),r.sort(o);let t=function(e){let t,n=new Map;for(let r=0,i=e.length;r<i;r++){let i=e[r],o=[i.key,i.id];r>0&&0===e2(i.key,t)&&o.push(r),n.set(e5(o),i),t=i.key}return n}(r);n.set(s.doc._id,[t,s.changes])}c=s.seq}return n}(y);f.add((v=c,function(){var t;return t="_local/lastSeq",e.db.get(t).catch(h({_id:t,seq:0})).then(function(t){return Promise.all(tY(g).map(function(t){return p(t,e,g)})).then(function(n){var r=n.flat();return t.seq=v,r.push(t),e.db.bulkDocs({docs:r})}).then(()=>e.sourceDB.get("_local/purges").then(function(t){let n=t.purgeSeq;return e.db.get("_local/purgeSeq").then(function(e){return e._rev}).catch(h(void 0)).then(function(t){return e.db.put({_id:"_local/purgeSeq",_rev:t,purgeSeq:n})})}).catch(function(e){if(404!==e.status)throw e}))})})),u+=y.length;let _={view:e.name,last_seq:t.last_seq,results_count:y.length,indexed_docs:u};if(e.sourceDB.emit("indexing",_),e.sourceDB.activeTasks.update(s,{completed_items:u}),!(y.length<n.changes_batch_size))return d()}(await e.sourceDB.changes({return_docs:!0,conflicts:!0,include_docs:!0,style:"all_docs",since:c,limit:n.changes_batch_size}),await e.db.get("_local/purgeSeq").then(function(e){return e.purgeSeq}).catch(h(-1)).then(function(t){return e.sourceDB.get("_local/purges").then(function(n){let r=n.purges.filter(function(e,n){return n>t}).map(e=>e.docId);return Promise.all(r.filter(function(e,t){return r.indexOf(e)===t}).map(function(t){return e.sourceDB.get(t).then(function(e){return{docId:t,doc:e}}).catch(h({docId:t}))}))}).catch(h([]))}))}try{await e.sourceDB.info().then(function(t){s=e.sourceDB.activeTasks.add({name:"view_indexing",total_items:t.update_seq-c})}),await d(),await f.finish(),e.seq=c,e.sourceDB.activeTasks.remove(s)}catch(t){e.sourceDB.activeTasks.remove(s,t)}}function _(e,t){return tW(v(e),function(){return m(e,t)})()}async function m(e,t){let r,i=e.reduceFun&&!1!==t.reduce,o=t.skip||0;async function c(t){t.include_docs=!0;let n=await e.db.allDocs(t);return r=n.total_rows,n.rows.map(function(e){if("value"in e.doc&&"object"==typeof e.doc.value&&null!==e.doc.value){let t=Object.keys(e.doc.value).sort(),n=["id","key","value"];if(!(t<n||t>n))return e.doc.value}let t=function(e){for(var t=[],n=[],r=0;;){var i=e[r++];if("\0"===i)if(1===t.length)return t.pop();else{!function(e,t){var n=e.pop();if(t.length){var r=t[t.length-1];n===r.element&&(t.pop(),r=t[t.length-1]);var i=r.element,o=r.index;Array.isArray(i)?i.push(n):o===e.length-2?i[e.pop()]=n:e.push(n)}}(t,n);continue}switch(i){case"1":t.push(null);break;case"2":t.push("1"===e[r]),r++;break;case"3":var o=function(e,t){var n,r=t;if("1"===e[t])n=0,t++;else{var i="0"===e[t];t++;var o="",s=parseInt(e.substring(t,t+3),10)+-324;for(i&&(s=-s),t+=3;;){var a=e[t];if("\0"===a)break;o+=a,t++}n=1===(o=o.split(".")).length?parseInt(o,10):parseFloat(o[0]+"."+o[1]),i&&(n-=10),0!==s&&(n=parseFloat(n+"e"+s))}return{num:n,length:t-r}}(e,r);t.push(o.num),r+=o.length;break;case"4":for(var s="";;){var a=e[r];if("\0"===a)break;s+=a,r++}s=s.replace(/\u0001\u0001/g,"\0").replace(/\u0001\u0002/g,"\x01").replace(/\u0002\u0002/g,"\x02"),t.push(s);break;case"5":var c={element:[],index:t.length};t.push(c.element),n.push(c);break;case"6":var u={element:{},index:t.length};t.push(u.element),n.push(u);break;default:throw Error("bad collationIndex or unexpectedly reached end of input: "+i)}}}(e.doc._id);return{key:t[0],id:t[1],value:"value"in e.doc?e.doc.value:null}})}async function u(c){let u;if(u=i?function(e,t,r){0===r.group_level&&delete r.group_level;let i=r.group||r.group_level,o=n(e.reduceFun),a=[],c=isNaN(r.group_level)?Number.POSITIVE_INFINITY:r.group_level;for(let e of t){let t=a[a.length-1],n=i?e.key:null;if(i&&Array.isArray(n)&&(n=n.slice(0,c)),t&&0===e2(t.groupKey,n)){t.keys.push([e.key,e.id]),t.values.push(e.value);continue}a.push({keys:[[e.key,e.id]],values:[e.value],groupKey:n})}for(let n of(t=[],a)){let r=function(e,t,n,r,i){try{return{output:t(n,r,i)}}catch(o){return ne(e,o,{fun:t,keys:n,values:r,rereduce:i}),{error:o}}}(e.sourceDB,o,n.keys,n.values,!1);if(r.error&&r.error instanceof tQ)throw r.error;t.push({value:r.error?null:r.output,key:n.groupKey})}return{rows:s(t,r.limit,r.skip)}}(e,c,t):void 0===t.keys?{total_rows:r,offset:o,rows:c}:{total_rows:r,offset:o,rows:s(c,t.limit,t.skip)},t.update_seq&&(u.update_seq=e.seq),t.include_docs){let n=tX(c.map(a)),r=await e.sourceDB.allDocs({keys:n,include_docs:!0,conflicts:t.conflicts,attachments:t.attachments,binary:t.binary}),i=new Map;for(let e of r.rows)i.set(e.id,e.doc);for(let e of c){let t=a(e),n=i.get(t);n&&(e.doc=n)}}return u}if(void 0===t.keys||t.keys.length||(t.limit=0,delete t.keys),void 0!==t.keys){let e=t.keys.map(function(e){let n={startkey:e5([e]),endkey:e5([e,{}])};return t.update_seq&&(n.update_seq=!0),c(n)});return u((await Promise.all(e)).flat())}{let e,n,r={descending:t.descending};if(t.update_seq&&(r.update_seq=!0),"start_key"in t&&(e=t.start_key),"startkey"in t&&(e=t.startkey),"end_key"in t&&(n=t.end_key),"endkey"in t&&(n=t.endkey),void 0!==e&&(r.startkey=t.descending?e5([e,{}]):e5([e])),void 0!==n){let e=!1!==t.inclusive_end;t.descending&&(e=!e),r.endkey=e5(e?[n,{}]:[n])}if(void 0!==t.key){let e=e5([t.key]),n=e5([t.key,{}]);r.descending?(r.endkey=e,r.startkey=n):(r.startkey=e,r.endkey=n)}return i||("number"==typeof t.limit&&(r.limit=t.limit),r.skip=o),u(await c(r))}}async function b(e){return(await e.fetch("_view_cleanup",{headers:new eW({"Content-Type":"application/json"}),method:"POST"})).json()}async function w(t){try{let n=await t.get("_local/"+e),r=new Map;for(let e of Object.keys(n.views)){let t=t7(e),n="_design/"+t[0],i=t[1],o=r.get(n);o||(o=new Set,r.set(n,o)),o.add(i)}let i={keys:tY(r),include_docs:!0},o=await t.allDocs(i),s={};for(let e of o.rows){let t=e.key.substring(8);for(let i of r.get(e.key)){let r=t+"/"+i;n.views[r]||(r=i);let o=Object.keys(n.views[r]),a=e.doc&&e.doc.views&&e.doc.views[i];for(let e of o)s[e]=s[e]||a}}let a=Object.keys(s).filter(function(e){return!s[e]}).map(function(e){return tW(v(e),function(){return new t.constructor(e,t.__opts).destroy()})()});return Promise.all(a).then(function(){return{ok:!0}})}catch(e){if(404===e.status)return{ok:!0};throw e}}async function k(t,n,i){if("function"==typeof t._query){var o;return o=n,new Promise(function(e,n){t._query(o,i,function(t,r){if(t)return n(t);e(r)})})}if(Z(t))return d(t,n,i);let s={changes_batch_size:t.__opts.view_update_changes_batch_size||50};if("string"!=typeof n)return f(i,n),t9.add(async function(){var r,o;let a=await t6(t,"temp_view/temp_view",n.map,n.reduce,!0,e);return r=y(a,s).then(function(){return _(a,i)}),o=function(){return a.db.destroy()},r.then(function(e){return o().then(function(){return e})},function(e){return o().then(function(){throw e})})}),t9.finish();{let o=n,a=t7(o),c=a[0],u=a[1],l=await t.get("_design/"+c);if(!(n=l.views&&l.views[u]))throw new tV(`ddoc ${l._id} has no view named ${u}`);r(l,u),f(i,n);let d=await t6(t,o,n.map,n.reduce,!1,e);return"ok"===i.stale||"update_after"===i.stale?"update_after"===i.stale&&S(function(){y(d,s)}):await y(d,s),_(d,i)}}return{query:function(e,t,n){var r;let i=this;"function"==typeof t&&(n=t,t={}),t=t?((r=t).group_level=l(r.group_level),r.limit=l(r.limit),r.skip=l(r.skip),r):{},"function"==typeof e&&(e={map:e});let o=Promise.resolve().then(function(){return k(i,e,t)});return tG(o,n),o},viewCleanup:(i=function(){if("function"==typeof this._viewCleanup){var e;return e=this,new Promise(function(t,n){e._viewCleanup(function(e,r){if(e)return n(e);t(r)})})}return Z(this)?b(this):w(this)},function(...e){var t=e.pop(),n=i.apply(this,e);return"function"==typeof t&&tG(n,t),n})}}("mrviews",function(e,t){return"function"==typeof e&&2===e.length?function(n){return e(n,t)}:t2(e.toString(),t)},function(e){var t=e.toString(),n=function(e){if(/^_sum/.test(e))return nt._sum;if(/^_count/.test(e))return nt._count;if(/^_stats/.test(e))return nt._stats;if(/^_/.test(e))throw Error(e+" is not a supported reduce function.")}(t);return n||t2(t)},function(e,t){var n=e.views&&e.views[t];if("string"!=typeof n.map)throw new tV("ddoc "+e._id+" has no string view named "+t+", instead found object of type: "+typeof n.map)});function nr(e,t){return Promise.all(Object.keys(t._attachments).map(function(n){return e.getAttachment(t._id,n,{rev:t._rev})}))}var ni="pouchdb";function no(e,t,n,r,i){return e.get(t).catch(function(n){if(404===n.status)return("http"===e.adapter||"https"===e.adapter)&&D(404,"PouchDB is just checking if a remote checkpoint exists."),{session_id:r,_id:t,history:[],replicator:ni,version:1};throw n}).then(function(o){if(!i.cancelled&&o.last_seq!==n)return o.history=(o.history||[]).filter(function(e){return e.session_id!==r}),o.history.unshift({last_seq:n,session_id:r}),o.history=o.history.slice(0,5),o.version=1,o.replicator=ni,o.session_id=r,o.last_seq=n,e.put(o).catch(function(o){if(409===o.status)return no(e,t,n,r,i);throw o})})}class ns{constructor(e,t,n,r,i={writeSourceCheckpoint:!0,writeTargetCheckpoint:!0}){this.src=e,this.target=t,this.id=n,this.returnValue=r,this.opts=i,void 0===i.writeSourceCheckpoint&&(i.writeSourceCheckpoint=!0),void 0===i.writeTargetCheckpoint&&(i.writeTargetCheckpoint=!0)}writeCheckpoint(e,t){var n=this;return this.updateTarget(e,t).then(function(){return n.updateSource(e,t)})}updateTarget(e,t){return this.opts.writeTargetCheckpoint?no(this.target,this.id,e,t,this.returnValue):Promise.resolve(!0)}updateSource(e,t){if(!this.opts.writeSourceCheckpoint)return Promise.resolve(!0);var n=this;return no(this.src,this.id,e,t,this.returnValue).catch(function(e){if(nu(e))return n.opts.writeSourceCheckpoint=!1,!0;throw e})}getCheckpoint(){var e=this;return e.opts.writeSourceCheckpoint||e.opts.writeTargetCheckpoint?e.opts&&e.opts.writeSourceCheckpoint&&!e.opts.writeTargetCheckpoint?e.src.get(e.id).then(function(e){return e.last_seq||0}).catch(function(e){if(404!==e.status)throw e;return 0}):e.target.get(e.id).then(function(t){return e.opts&&e.opts.writeTargetCheckpoint&&!e.opts.writeSourceCheckpoint?t.last_seq||0:e.src.get(e.id).then(function(e){var n;return t.version!==e.version?0:(n=t.version?t.version.toString():"undefined")in na?na[n](t,e):0},function(n){if(404===n.status&&t.last_seq)return e.src.put({_id:e.id,last_seq:0}).then(function(){return 0},function(n){return nu(n)?(e.opts.writeSourceCheckpoint=!1,t.last_seq):0});throw n})}).catch(function(e){if(404!==e.status)throw e;return 0}):Promise.resolve(0)}}var na={undefined:function(e,t){return 0===e2(e.last_seq,t.last_seq)?t.last_seq:0},1:function(e,t){var n,r;return(n=t,r=e,n.session_id===r.session_id?{last_seq:n.last_seq,history:n.history}:function e(t,n){var r=t[0],i=t.slice(1),o=n[0],s=n.slice(1);return r&&0!==n.length?nc(r.session_id,n)?{last_seq:r.last_seq,history:t}:nc(o.session_id,i)?{last_seq:o.last_seq,history:s}:e(i,s):{last_seq:0,history:[]}}(n.history,r.history)).last_seq}};function nc(e,t){var n=t[0],r=t.slice(1);return!!e&&0!==t.length&&(e===n.session_id||nc(e,r))}function nu(e){return"number"==typeof e.status&&4===Math.floor(e.status/100)}function nl(e,t,n,r,i){return this instanceof ns?nl:new ns(e,t,n,r,i)}class nf extends g(){constructor(){super(),this.cancelled=!1,this.state="pending";let e=new Promise((e,t)=>{this.once("complete",e),this.once("error",t)});this.then=function(t,n){return e.then(t,n)},this.catch=function(t){return e.catch(t)},this.catch(function(){})}cancel(){this.cancelled=!0,this.state="cancelled",this.emit("cancel")}ready(e,t){if(this._readyCalled)return;this._readyCalled=!0;let n=()=>{this.cancel()};function r(){e.removeListener("destroyed",n),t.removeListener("destroyed",n)}e.once("destroyed",n),t.once("destroyed",n),this.once("complete",r),this.once("error",r)}}function nd(e,t){var n=t.PouchConstructor;return"string"==typeof e?new n(e,t):e}function nh(e,t,n,r){if("function"==typeof n&&(r=n,n={}),void 0===n&&(n={}),n.doc_ids&&!Array.isArray(n.doc_ids))throw W(K,"`doc_ids` filter parameter is not a list.");n.complete=r,(n=b(n)).continuous=n.continuous||n.live,n.retry="retry"in n&&n.retry,n.PouchConstructor=n.PouchConstructor||this;var i=new nf(n);return!function e(t,n,r,i,o){var s,a,c,u=[],l={seq:0,changes:[],docs:[]},f=!1,d=!1,h=!1,v=0,y=0,g=r.continuous||r.live||!1,_=r.batch_size||100,m=r.batches_limit||10,w=r.style||"all_docs",k=!1,$=r.doc_ids,j=r.selector,O=[],q=p();o=o||{ok:!0,start_time:new Date().toISOString(),docs_read:0,docs_written:0,doc_write_failures:0,errors:[]};var x={};function A(){var e,o,s,c,u,l;return a?Promise.resolve():(e=r.doc_ids?r.doc_ids.sort(e2):"",o=r.filter?r.filter.toString():"",s="",c="",u="",r.selector&&(u=JSON.stringify(r.selector)),r.filter&&r.query_params&&(s=JSON.stringify(Object.keys(l=r.query_params).sort(e2).reduce(function(e,t){return e[t]=l[t],e},{}))),r.filter&&"_view"===r.filter&&(c=r.view.toString()),Promise.all([t.id(),n.id()]).then(function(t){var n=t[0]+t[1]+o+c+s+e+u;return new Promise(function(e){em(n,e)})}).then(function(e){return"_local/"+(e=e.replace(/\//g,".").replace(/\+/g,"_"))})).then(function(e){var o={};a=new nl(t,n,e,i,!1===r.checkpoint?{writeSourceCheckpoint:!1,writeTargetCheckpoint:!1}:"source"===r.checkpoint?{writeSourceCheckpoint:!0,writeTargetCheckpoint:!1}:"target"===r.checkpoint?{writeSourceCheckpoint:!1,writeTargetCheckpoint:!0}:{writeSourceCheckpoint:!0,writeTargetCheckpoint:!0})})}function E(){if(O=[],0!==s.docs.length){var e=s.docs,t={timeout:r.timeout};return n.bulkDocs({docs:e,new_edits:!1},t).then(function(t){if(i.cancelled)throw T(),Error("cancelled");var n=Object.create(null);t.forEach(function(e){e.error&&(n[e.id]=e)});var r=Object.keys(n).length;o.doc_write_failures+=r,o.docs_written+=e.length-r,e.forEach(function(e){var t=n[e._id];if(t){o.errors.push(t);var r=(t.name||"").toLowerCase();if("unauthorized"===r||"forbidden"===r)i.emit("denied",b(t));else throw t}else O.push(e)})},function(t){throw o.doc_write_failures+=e.length,t})}}function D(){if(s.error)throw Error("There was a problem getting docs.");o.last_seq=y=s.seq;var e=b(o);return O.length&&(e.docs=O,"number"==typeof s.pending&&(e.pending=s.pending,delete s.pending),i.emit("change",e)),f=!0,t.info().then(function(e){var n=t.activeTasks.get(c);if(s&&n){var r=n.completed_items||0,i=parseInt(e.update_seq,10)-parseInt(v,10);t.activeTasks.update(c,{completed_items:r+s.changes.length,total_items:i})}}),a.writeCheckpoint(s.seq,q).then(function(){if(i.emit("checkpoint",{checkpoint:s.seq}),f=!1,i.cancelled)throw T(),Error("cancelled");s=void 0,N()}).catch(function(e){throw z(e),e})}function C(){var e,r,a;return(e=s.diffs,e=b(e),r=[],a=!0,Promise.resolve().then(function(){var o,s,c=(s=[],Object.keys(o=e).forEach(function(e){o[e].missing.forEach(function(t){s.push({id:e,rev:t})})}),{docs:s,revs:!0,latest:!0});if(c.docs.length)return t.bulkGet(c).then(function(e){if(i.cancelled)throw Error("cancelled");return Promise.all(e.results.map(function(e){return Promise.all(e.docs.map(function(e){var r,i,o=e.ok;return(e.error&&(a=!1),o&&o._attachments)?(r=Z(t)&&!Z(n),i=Object.keys(o._attachments),!r?nr(t,o):n.get(o._id).then(function(e){return Promise.all(i.map(function(r){return e._attachments&&e._attachments[r]&&e._attachments[r].digest===o._attachments[r].digest?n.getAttachment(e._id,r):t.getAttachment(o._id,r)}))}).catch(function(e){if(404!==e.status)throw e;return nr(t,o)})).then(e=>{var t=Object.keys(o._attachments);return e.forEach(function(e,n){var r=o._attachments[t[n]];delete r.stub,delete r.length,r.data=e}),o}):o}))})).then(function(e){r=r.concat(e.flat().filter(Boolean))})})}).then(function(){return{ok:a,docs:r}})).then(function(e){s.error=!e.ok,e.docs.forEach(function(e){delete s.diffs[e._id],o.docs_read++,s.docs.push(e)})})}function I(e){if(0===l.changes.length){0===u.length&&!s&&((g&&x.live||d)&&(i.state="pending",i.emit("paused")),d&&T());return}(e||d||l.changes.length>=_)&&(u.push(l),l={seq:0,changes:[],docs:[]},("pending"===i.state||"stopped"===i.state)&&(i.state="active",i.emit("active")),function e(){if(!i.cancelled&&!s){var t;if(0===u.length)return void I(!0);s=u.shift(),i.emit("checkpoint",{start_next_batch:s.seq}),(t={},s.changes.forEach(function(e){i.emit("checkpoint",{revs_diff:e}),"_user/"!==e.id&&(t[e.id]=e.changes.map(function(e){return e.rev}))}),n.revsDiff(t).then(function(e){if(i.cancelled)throw T(),Error("cancelled");s.diffs=e})).then(C).then(E).then(D).then(e).catch(function(e){B("batch processing terminated with error",e)})}}())}function B(e,t){h||(t.message||(t.message=e),o.ok=!1,o.status="aborting",u=[],l={seq:0,changes:[],docs:[]},T(t))}function T(s){if(!h&&(!i.cancelled||(o.status="cancelled",!f)))if(o.status=o.status||"complete",o.end_time=new Date().toISOString(),o.last_seq=y,h=!0,t.activeTasks.remove(c,s),s){(s=W(s)).result=o;var a=(s.name||"").toLowerCase();"unauthorized"===a||"forbidden"===a?(i.emit("error",s),i.removeAllListeners()):function(e,t,n,r){if(!1===e.retry){t.emit("error",n),t.removeAllListeners();return}if("function"!=typeof e.back_off_function&&(e.back_off_function=P),t.emit("requestError",n),"active"===t.state||"pending"===t.state){t.emit("paused",n),t.state="stopped";var i=function(){e.current_back_off=0};t.once("paused",function(){t.removeListener("active",i)}),t.once("active",i)}e.current_back_off=e.current_back_off||0,e.current_back_off=e.back_off_function(e.current_back_off),setTimeout(r,e.current_back_off)}(r,i,s,function(){e(t,n,r,i)})}else i.emit("complete",o),i.removeAllListeners()}function M(e,n,o){if(i.cancelled)return T();if("number"==typeof n&&(l.pending=n),!Y(r)(e)){var s=t.activeTasks.get(c);if(s){var a=s.completed_items||0;t.activeTasks.update(c,{completed_items:++a})}return}l.seq=e.seq||o,l.changes.push(e),i.emit("checkpoint",{pending_batch:l.seq}),S(function(){I(0===u.length&&x.live)})}function L(e){if(k=!1,i.cancelled)return T();if(e.results.length>0)x.since=e.results[e.results.length-1].seq,N(),I(!0);else{var t=function(){g?(x.live=!0,N()):d=!0,I(!0)};s||0!==e.results.length?t():(f=!0,a.writeCheckpoint(e.last_seq,q).then(function(){if(f=!1,o.last_seq=y=e.last_seq,i.cancelled)throw T(),Error("cancelled");t()}).catch(z))}}function R(e){if(k=!1,i.cancelled)return T();B("changes rejected",e)}function N(){if(!k&&!d&&u.length<m){k=!0,i._changes&&(i.removeListener("cancel",i._abortChanges),i._changes.cancel()),i.once("cancel",n);var e=t.changes(x).on("change",M);e.then(o,o),e.then(L).catch(R),r.retry&&(i._changes=e,i._abortChanges=n)}function n(){e.cancel()}function o(){i.removeListener("cancel",n)}}function F(e){return t.info().then(function(n){var i=void 0===r.since?parseInt(n.update_seq,10)-parseInt(e,10):parseInt(n.update_seq,10);return c=t.activeTasks.add({name:`${g?"continuous ":""}replication from ${n.db_name}`,total_items:i}),e})}function J(){A().then(function(){return i.cancelled?void T():a.getCheckpoint().then(F).then(function(e){y=e,v=e,x={since:y,limit:_,batch_size:_,style:w,doc_ids:$,selector:j,return_docs:!0},r.filter&&("string"!=typeof r.filter?x.include_docs=!0:x.filter=r.filter),"heartbeat"in r&&(x.heartbeat=r.heartbeat),"timeout"in r&&(x.timeout=r.timeout),r.query_params&&(x.query_params=r.query_params),r.view&&(x.view=r.view),N()})}).catch(function(e){B("getCheckpoint rejected with ",e)})}function z(e){f=!1,B("writeCheckpoint completed with error",e)}if(i.ready(t,n),i.cancelled)return void T();i._addedListeners||(i.once("cancel",T),"function"==typeof r.complete&&(i.once("error",r.complete),i.once("complete",function(e){r.complete(null,e)})),i._addedListeners=!0),void 0===r.since?J():A().then(function(){return f=!0,a.writeCheckpoint(r.since,q)}).then(function(){if(f=!1,i.cancelled)return void T();y=r.since,J()}).catch(z)}(nd(e,n),nd(t,n),n,i),i}function np(e,t,n,r){return"function"==typeof n&&(r=n,n={}),void 0===n&&(n={}),(n=b(n)).PouchConstructor=n.PouchConstructor||this,new nv(e=nd(e,n),t=nd(t,n),n,r)}class nv extends g(){constructor(e,t,n,r){super(),this.canceled=!1;let i=n.push?Object.assign({},n,n.push):n,o=n.pull?Object.assign({},n,n.pull):n;this.push=nh(e,t,i),this.pull=nh(t,e,o),this.pushPaused=!0,this.pullPaused=!0;let s=e=>{this.emit("change",{direction:"pull",change:e})},a=e=>{this.emit("change",{direction:"push",change:e})},c=e=>{this.emit("denied",{direction:"push",doc:e})},u=e=>{this.emit("denied",{direction:"pull",doc:e})},l=()=>{this.pushPaused=!0,this.pullPaused&&this.emit("paused")},f=()=>{this.pullPaused=!0,this.pushPaused&&this.emit("paused")},d=()=>{this.pushPaused=!1,this.pullPaused&&this.emit("active",{direction:"push"})},h=()=>{this.pullPaused=!1,this.pushPaused&&this.emit("active",{direction:"pull"})},p={},v=e=>(t,n)=>{let r="change"===t&&(n===s||n===a),i="denied"===t&&(n===u||n===c),o="paused"===t&&(n===f||n===l),v="active"===t&&(n===h||n===d);(r||i||o||v)&&(t in p||(p[t]={}),p[t][e]=!0,2===Object.keys(p[t]).length&&this.removeAllListeners(t))};function y(e,t,n){-1==e.listeners(t).indexOf(n)&&e.on(t,n)}n.live&&(this.push.on("complete",this.pull.cancel.bind(this.pull)),this.pull.on("complete",this.push.cancel.bind(this.push))),this.on("newListener",function(e){"change"===e?(y(this.pull,"change",s),y(this.push,"change",a)):"denied"===e?(y(this.pull,"denied",u),y(this.push,"denied",c)):"active"===e?(y(this.pull,"active",h),y(this.push,"active",d)):"paused"===e&&(y(this.pull,"paused",f),y(this.push,"paused",l))}),this.on("removeListener",function(e){"change"===e?(this.pull.removeListener("change",s),this.push.removeListener("change",a)):"denied"===e?(this.pull.removeListener("denied",u),this.push.removeListener("denied",c)):"active"===e?(this.pull.removeListener("active",h),this.push.removeListener("active",d)):"paused"===e&&(this.pull.removeListener("paused",f),this.push.removeListener("paused",l))}),this.pull.on("removeListener",v("pull")),this.push.on("removeListener",v("push"));let g=Promise.all([this.push,this.pull]).then(e=>{let t={push:e[0],pull:e[1]};return this.emit("complete",t),r&&r(null,t),this.removeAllListeners(),t},e=>{if(this.cancel(),r?r(e):this.emit("error",e),this.removeAllListeners(),r)throw e});this.then=function(e,t){return g.then(e,t)},this.catch=function(e){return g.catch(e)}}cancel(){this.canceled||(this.canceled=!0,this.push.cancel(),this.pull.cancel())}}eQ.plugin(function(e){e.adapter("idb",tT,!0)}).plugin(function(e){e.adapter("http",tK,!1),e.adapter("https",tK,!1)}).plugin({query:function(e,t,n){return nn.query.call(this,e,t,n)},viewCleanup:function(e){return nn.viewCleanup.call(this,e)}}).plugin(function(e){e.replicate=nh,e.sync=np,Object.defineProperty(e.prototype,"replicate",{get:function(){var e=this;return void 0===this.replicateMethods&&(this.replicateMethods={from:function(t,n,r){return e.constructor.replicate(t,e,n,r)},to:function(t,n,r){return e.constructor.replicate(e,t,n,r)}}),this.replicateMethods}}),e.prototype.sync=function(e,t,n){return this.constructor.sync(this,e,t,n)}});let ny=eQ},49807:function(e,t,n){n.d(t,{Z:()=>eT});class r extends Error{constructor(e,t,n){super(),this.status=e,this.name=t,this.message=n,this.error=!0}toString(){return JSON.stringify({status:this.status,name:this.name,message:this.message,reason:this.reason})}}new r(401,"unauthorized","Name or password is incorrect."),new r(400,"bad_request","Missing JSON list of 'docs'"),new r(404,"not_found","missing"),new r(409,"conflict","Document update conflict"),new r(400,"bad_request","_id field must contain a string"),new r(412,"missing_id","_id is required for puts"),new r(400,"bad_request","Only reserved document ids may start with underscore."),new r(412,"precondition_failed","Database not open");var i,o=new r(500,"unknown_error","Database encountered an unknown error");function s(e){if("object"!=typeof e){var t=e;(e=o).data=t}return"error"in e&&"conflict"===e.error&&(e.name="conflict",e.status=409),"name"in e||(e.name=e.error||"unknown"),"status"in e||(e.status=500),"message"in e||(e.message=e.message||e.reason),"stack"in e||(e.stack=Error().stack),e}new r(500,"badarg","Some query argument is invalid"),new r(400,"invalid_request","Request was invalid"),new r(400,"query_parse_error","Some query parameter is invalid"),new r(500,"doc_validation","Bad special document member"),new r(400,"bad_request","Something wrong with the request"),new r(400,"bad_request","Document must be a JSON object"),new r(404,"not_found","Database not found"),new r(500,"indexed_db_went_bad","unknown"),new r(500,"web_sql_went_bad","unknown"),new r(500,"levelDB_went_went_bad","unknown"),new r(403,"forbidden","Forbidden by design doc validate_doc_update function"),new r(400,"bad_request","Invalid rev format"),new r(412,"file_exists","The database could not be created, the file already exists."),new r(412,"missing_stub","A pre-existing attachment stub wasn't found"),new r(413,"invalid_url","Provided URL is invalid");var a=Headers;function c(e,t){if(e===t)return 0;e=u(e),t=u(t);var n,r,i=f(e),o=f(t);if(i-o!=0)return i-o;switch(typeof e){case"number":return e-t;case"boolean":return e<t?-1:1;case"string":return(n=e)===(r=t)?0:n>r?1:-1}return Array.isArray(e)?function(e,t){for(var n=Math.min(e.length,t.length),r=0;r<n;r++){var i=c(e[r],t[r]);if(0!==i)return i}return e.length===t.length?0:e.length>t.length?1:-1}(e,t):function(e,t){for(var n=Object.keys(e),r=Object.keys(t),i=Math.min(n.length,r.length),o=0;o<i;o++){var s=c(n[o],r[o]);if(0!==s||0!==(s=c(e[n[o]],t[r[o]])))return s}return n.length===r.length?0:n.length>r.length?1:-1}(e,t)}function u(e){switch(typeof e){case"undefined":return null;case"number":if(e===1/0||e===-1/0||isNaN(e))return null;break;case"object":var t=e;if(Array.isArray(e)){var n=e.length;e=Array(n);for(var r=0;r<n;r++)e[r]=u(t[r])}else if(e instanceof Date)return e.toJSON();else if(null!==e){for(var i in e={},t)if(Object.prototype.hasOwnProperty.call(t,i)){var o=t[i];void 0!==o&&(e[i]=u(o))}}}return e}function l(e){return f(e=u(e))+""+function(e){if(null!==e)switch(typeof e){case"boolean":return+!!e;case"number":return function(e){if(0===e)return"1";var t,n=e.toExponential().split(/e\+?/),r=parseInt(n[1],10),i=e<0,o=i?"0":"2";o+=""+(t=((i?-r:r)- -324).toString(),function(e,t,n){for(var r="",i=3-e.length;r.length<i;)r+="0";return r}(t,"0",3)+t);var s=Math.abs(parseFloat(n[0]));i&&(s=10-s);var a=s.toFixed(20);return o+(""+(a=a.replace(/\.?0+$/,"")))}(e);case"string":return e.replace(/\u0002/g,"\x02\x02").replace(/\u0001/g,"\x01\x02").replace(/\u0000/g,"\x01\x01");case"object":var t=Array.isArray(e),n=t?e:Object.keys(e),r=-1,i=n.length,o="";if(t)for(;++r<i;)o+=l(n[r]);else for(;++r<i;){var s=n[r];o+=l(s)+l(e[s])}return o}return""}(e)+"\0"}function f(e){var t=["boolean","number","string","object"].indexOf(typeof e);return~t?null===e?1:Array.isArray(e)?5:t<3?t+2:t+3:Array.isArray(e)?5:void 0}var d=n(17187),h=n.n(d),p=n(8322),v=n.n(p);self.setImmediate||self.setTimeout;var y=Function.prototype.toString,g=y.call(Object);function _(e){if(!e||"object"!=typeof e)return e;if(Array.isArray(e)){for(n=0,t=[],r=e.length;n<r;n++)t[n]=_(e[n]);return t}if(e instanceof Date&&isFinite(e))return e.toISOString();if("undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer||"undefined"!=typeof Blob&&e instanceof Blob)return e instanceof ArrayBuffer?e.slice(0):e.slice(0,e.size,e.type);if(!function(e){var t=Object.getPrototypeOf(e);if(null===t)return!0;var n=t.constructor;return"function"==typeof n&&n instanceof n&&y.call(n)==g}(e))return e;for(n in t={},e)if(Object.prototype.hasOwnProperty.call(e,n)){var t,n,r,i=_(e[n]);void 0!==i&&(t[n]=i)}return t}try{localStorage.setItem("_pouch_check_localstorage",1),i=!!localStorage.getItem("_pouch_check_localstorage")}catch(e){i=!1}let m="function"==typeof queueMicrotask?queueMicrotask:function(e){Promise.resolve().then(e)};function b(e){if("undefined"!=typeof console&&"function"==typeof console[e]){var t=Array.prototype.slice.call(arguments,1);console[e].apply(console,t)}}function w(e){return"boolean"==typeof e._remote?e._remote:"function"==typeof e.type&&(b("warn","db.type() is deprecated and will be removed in a future version of PouchDB"),"http"===e.type())}function k(e,t,n){return e.get(t).catch(function(e){if(404!==e.status)throw e;return{}}).then(function(r){var i,o,s,a=r._rev,c=n(r);return c?(c._id=t,c._rev=a,i=e,o=c,s=n,i.put(o).then(function(e){return{updated:!0,rev:e.rev}},function(e){if(409!==e.status)throw e;return k(i,o._id,s)})):{updated:!1,rev:a}})}h();class $ extends Error{constructor(e){super(),this.status=400,this.name="query_parse_error",this.message=e,this.error=!0;try{Error.captureStackTrace(this,$)}catch(e){}}}class j extends Error{constructor(e){super(),this.status=404,this.name="not_found",this.message=e,this.error=!0;try{Error.captureStackTrace(this,j)}catch(e){}}}class O extends Error{constructor(e){super(),this.status=500,this.name="invalid_value",this.message=e,this.error=!0;try{Error.captureStackTrace(this,O)}catch(e){}}}function q(e,t){return t&&e.then(function(e){m(function(){t(null,e)})},function(e){m(function(){t(e)})}),e}function x(e,t){return function(){var n=arguments,r=this;return e.add(function(){return t.apply(r,n)})}}function S(e){var t=new Set(e),n=Array(t.size),r=-1;return t.forEach(function(e){n[++r]=e}),n}function A(e){var t=Array(e.size),n=-1;return e.forEach(function(e,r){t[++n]=r}),t}class E{constructor(){this.promise=Promise.resolve()}add(e){return this.promise=this.promise.catch(()=>{}).then(()=>e()),this.promise}finish(){return this.promise}}function P(e){if(!e)return"undefined";switch(typeof e){case"function":case"string":return e.toString();default:return JSON.stringify(e)}}async function D(e,t,n,r,i,o){let s,a=P(n)+P(r)+"undefined";if(!i&&(s=e._cachedViews=e._cachedViews||{})[a])return s[a];let c=e.info().then(async function(c){let u,l=c.db_name+"-mrview-"+(i?"temp":v().hash(a));await k(e,"_local/"+o,function(e){e.views=e.views||{};let n=t;-1===n.indexOf("/")&&(n=t+"/"+t);let r=e.views[n]=e.views[n]||{};if(!r[l])return r[l]=!0,e});let f=(await e.registerDependentDatabase(l)).db;f.auto_compaction=!0;let d={name:l,db:f,sourceDB:e,adapter:e.adapter,mapFun:n,reduceFun:r};try{u=await d.db.get("_local/lastSeq")}catch(e){if(404!==e.status)throw e}return d.seq=u?u.seq:0,s&&d.db.once("destroyed",function(){delete s[a]}),d});return s&&(s[a]=c),c}let C={},I=new E;function B(e){return -1===e.indexOf("/")?[e,e]:e.split("/")}function T(e,t,n){try{e.emit("error",t)}catch(e){b("error","The user's map/reduce function threw an uncaught error.\nYou can debug this error by doing:\nmyDatabase.on('error', function (err) { debugger; });\nPlease double-check your map/reduce function."),b("error",t,n)}}function M(e,t){for(var n=e,r=0,i=t.length;r<i&&(n=n[t[r]]);r++);return n}function L(e,t){return e<t?-1:+(e>t)}function R(e){for(var t=[],n="",r=0,i=e.length;r<i;r++){var o=e[r];r>0&&"\\"===e[r-1]&&("$"===o||"."===o)?n=n.substring(0,n.length-1)+o:"."===o?(t.push(n),n=""):n+=o}return t.push(n),t}var N=["$or","$nor","$not"];function F(e){return N.indexOf(e)>-1}function J(e){return Object.keys(e)[0]}function z(e){var t={},n={$or:!0,$nor:!0};return e.forEach(function(e){Object.keys(e).forEach(function(r){var i=e[r];if("object"!=typeof i&&(i={$eq:i}),F(r))if(i instanceof Array){if(n[r]){n[r]=!1,t[r]=i;return}var o=[];t[r].forEach(function(e){Object.keys(i).forEach(function(t){var n=i[t],r=Math.max(Object.keys(e).length,Object.keys(n).length),s=z([e,n]);Object.keys(s).length<=r||o.push(s)})}),t[r]=o}else t[r]=z([i]);else{var s=t[r]=t[r]||{};Object.keys(i).forEach(function(e){var t,n,r,o,a,c,u,l,f,d,h,p,v=i[e];if("$gt"===e||"$gte"===e){return t=e,n=v,void(void 0===(r=s).$eq&&(void 0!==r.$gte?"$gte"===t?n>r.$gte&&(r.$gte=n):n>=r.$gte&&(delete r.$gte,r.$gt=n):void 0!==r.$gt?"$gte"===t?n>r.$gt&&(delete r.$gt,r.$gte=n):n>r.$gt&&(r.$gt=n):r[t]=n))}if("$lt"===e||"$lte"===e){return o=e,a=v,void(void 0===(c=s).$eq&&(void 0!==c.$lte?"$lte"===o?a<c.$lte&&(c.$lte=a):a<=c.$lte&&(delete c.$lte,c.$lt=a):void 0!==c.$lt?"$lte"===o?a<c.$lt&&(delete c.$lt,c.$lte=a):a<c.$lt&&(c.$lt=a):c[o]=a))}if("$ne"===e){return u=v,void("$ne"in(l=s)?l.$ne.push(u):l.$ne=[u])}if("$eq"===e){return f=v,d=s,void(delete d.$gt,delete d.$gte,delete d.$lt,delete d.$lte,delete d.$ne,d.$eq=f)}if("$regex"===e){return h=v,void("$regex"in(p=s)?p.$regex.push(h):p.$regex=[h])}s[e]=v})}})}),t}function K(e){var t=_(e);(function e(t,n){for(var r in t){"$and"===r&&(n=!0);var i=t[r];"object"==typeof i&&(n=e(i,n))}return n})(t,!1)&&"$and"in(t=function e(t){for(var n in t){if(Array.isArray(t))for(var r in t)t[r].$and&&(t[r]=z(t[r].$and));var i=t[n];"object"==typeof i&&e(i)}return t}(t))&&(t=z(t.$and)),["$or","$nor"].forEach(function(e){e in t&&t[e].forEach(function(e){for(var t=Object.keys(e),n=0;n<t.length;n++){var r=t[n],i=e[r];("object"!=typeof i||null===i)&&(e[r]={$eq:i})}})}),"$not"in t&&(t.$not=z([t.$not]));for(var n=Object.keys(t),r=0;r<n.length;r++){var i=n[r],o=t[i];("object"!=typeof o||null===o)&&(o={$eq:o}),t[i]=o}return function e(t){Object.keys(t).forEach(function(n){var r=t[n];Array.isArray(r)?r.forEach(function(t){t&&"object"==typeof t&&e(t)}):"$ne"===n?t.$ne=[r]:"$regex"===n?t.$regex=[r]:r&&"object"==typeof r&&e(r)})}(t),t}function U(e,t,n){if(e=e.filter(function(e){return V(e.doc,t.selector,n)}),t.sort){var r,i=function(e){function t(t){return e.map(function(e){return M(t,R(J(e)))})}return function(e,n){var r,i,o=c(t(e.doc),t(n.doc));return 0!==o?o:(r=e.doc._id,r<(i=n.doc._id)?-1:+(r>i))}}(t.sort);e=e.sort(i),"string"!=typeof t.sort[0]&&"desc"===(r=t.sort[0])[J(r)]&&(e=e.reverse())}if("limit"in t||"skip"in t){var o=t.skip||0,s=("limit"in t?t.limit:e.length)+o;e=e.slice(o,s)}return e}function V(e,t,n){return n.every(function(n){var r,i,o,s=t[n],a=R(n),c=M(e,a);return F(n)?(r=n,i=s,o=e,"$or"===r?i.some(function(e){return V(o,e,Object.keys(e))}):"$not"===r?!V(o,i,Object.keys(i)):!i.find(function(e){return V(o,e,Object.keys(e))})):Q(s,e,a,c)})}function Q(e,t,n,r){return!e||("object"==typeof e?Object.keys(e).every(function(i){var o=e[i];if(0===i.indexOf("$"))return G(i,t,o,n,r);var s=R(i);if(void 0===r&&"object"!=typeof o&&s.length>0)return!1;var a=M(r,s);return"object"==typeof o?Q(o,t,n,a):G("$eq",t,o,s,a)}):e===r)}function G(e,t,n,r,i){if(!H[e])throw Error('unknown operator "'+e+'" - should be one of $eq, $lte, $lt, $gt, $gte, $exists, $ne, $in, $nin, $size, $mod, $regex, $elemMatch, $type, $allMatch or $all');return H[e](t,n,r,i)}function W(e){return null!=e}function X(e){return void 0!==e}function Y(e,t){return t.some(function(t){return e instanceof Array?e.some(function(e){return 0===c(t,e)}):0===c(t,e)})}var H={$elemMatch:function(e,t,n,r){return!!Array.isArray(r)&&0!==r.length&&("object"==typeof r[0]&&null!==r[0]?r.some(function(e){return V(e,t,Object.keys(t))}):r.some(function(r){return Q(t,e,n,r)}))},$allMatch:function(e,t,n,r){return!!Array.isArray(r)&&0!==r.length&&("object"==typeof r[0]&&null!==r[0]?r.every(function(e){return V(e,t,Object.keys(t))}):r.every(function(r){return Q(t,e,n,r)}))},$eq:function(e,t,n,r){return X(r)&&0===c(r,t)},$gte:function(e,t,n,r){return X(r)&&c(r,t)>=0},$gt:function(e,t,n,r){return X(r)&&c(r,t)>0},$lte:function(e,t,n,r){return X(r)&&0>=c(r,t)},$lt:function(e,t,n,r){return X(r)&&0>c(r,t)},$exists:function(e,t,n,r){return t?X(r):!X(r)},$mod:function(e,t,n,r){return W(r)&&"number"==typeof r&&parseInt(r,10)===r&&r%t[0]===t[1]},$ne:function(e,t,n,r){return t.every(function(e){return 0!==c(r,e)})},$in:function(e,t,n,r){return W(r)&&Y(r,t)},$nin:function(e,t,n,r){return W(r)&&!Y(r,t)},$size:function(e,t,n,r){return W(r)&&Array.isArray(r)&&r.length===t},$all:function(e,t,n,r){return Array.isArray(r)&&t.every(function(e){return r.some(function(t){return 0===c(e,t)})})},$regex:function(e,t,n,r){return W(r)&&"string"==typeof r&&t.every(function(e){return new RegExp(e).test(r)})},$type:function(e,t,n,r){switch(t){case"null":return null===r;case"boolean":return"boolean"==typeof r;case"number":return"number"==typeof r;case"string":return"string"==typeof r;case"array":return r instanceof Array;case"object":return"[object Object]"===({}).toString.call(r)}}};function Z(e,t){if("object"!=typeof t)throw Error("Selector error: expected a JSON object");var n=U([{doc:e}],{selector:t=K(t)},Object.keys(t));return n&&1===n.length}let ee=(...e)=>{let t=[];for(let n of e)Array.isArray(n)?t=t.concat(ee(...n)):t.push(n);return t},et="function"==typeof Array.prototype.flat?(...e)=>e.flat(1/0):ee;function en(e){let t={};for(let n of e)Object.assign(t,n);return t}function er(e,t){for(let n=0,r=Math.min(e.length,t.length);n<r;n++)if(e[n]!==t[n])return!1;return!0}function ei(e,t){if(e.length!==t.length)return!1;for(let n=0,r=e.length;n<r;n++)if(e[n]!==t[n])return!1;return!0}function eo(e){return function(...t){let n=t[t.length-1];if("function"!=typeof n)return e.apply(this,t);{let r=n.bind(null,null),i=n.bind(null);e.apply(this,t.slice(0,-1)).then(r,i)}}}function es(e){for(let t of((e=_(e)).index||(e.index={}),["type","name","ddoc"]))e.index[t]&&(e[t]=e.index[t],delete e.index[t]);return e.fields&&(e.index.fields=e.fields,delete e.fields),e.type||(e.type="json"),e}function ea(e){return"object"==typeof e&&null!==e}let ec=["$all","$allMatch","$and","$elemMatch","$exists","$in","$mod","$nin","$nor","$not","$or","$regex","$size","$type"],eu=["$in","$nin","$mod","$all"],el=["$eq","$gt","$gte","$lt","$lte"];function ef(e,t){if(Array.isArray(e))for(let n of e)ea(n)&&ef(n,t);else for(let[n,r]of Object.entries(e))-1!==ec.indexOf(n)&&function(e,t,n){let r="",i=t,o=!0;if(-1===["$in","$nin","$or","$and","$mod","$nor","$all"].indexOf(e)||Array.isArray(t)||(r="Query operator "+e+" must be an array."),-1===["$not","$elemMatch","$allMatch"].indexOf(e)||!Array.isArray(t)&&ea(t)||(r="Query operator "+e+" must be an object."),"$mod"===e&&Array.isArray(t))if(2!==t.length)r="Query operator $mod must be in the format [divisor, remainder], where divisor and remainder are both integers.";else{let e=t[0],n=t[1];0===e&&(r="Query operator $mod's divisor cannot be 0, cannot divide by zero.",o=!1),("number"!=typeof e||parseInt(e,10)!==e)&&(r="Query operator $mod's divisor is not an integer.",i=e),parseInt(n,10)!==n&&(r="Query operator $mod's remainder is not an integer.",i=n)}if("$exists"===e&&"boolean"!=typeof t&&(r="Query operator $exists must be a boolean."),"$type"===e){let e=["null","boolean","number","string","array","object"],n='"'+e.slice(0,e.length-1).join('", "')+'", or "'+e[e.length-1]+'"';"string"!=typeof t?r="Query operator $type must be a string. Supported values: "+n+".":-1==e.indexOf(t)&&(r="Query operator $type must be a string. Supported values: "+n+".")}if("$size"===e&&parseInt(t,10)!==t&&(r="Query operator $size must be a integer."),"$regex"===e&&"string"!=typeof t&&(n?r="Query operator $regex must be a string.":t instanceof RegExp||(r="Query operator $regex must be a string or an instance of a javascript regular expression.")),r)throw o&&(r+=" Received"+(null===i?" ":Array.isArray(i)?" array":" "+typeof i)+": "+(ea(i)?JSON.stringify(i,null,"	"):i)),Error(r)}(n,r,t),-1===el.indexOf(n)&&-1===eu.indexOf(n)&&ea(r)&&ef(r,t)}async function ed(e,t,n){n.body&&(n.body=JSON.stringify(n.body),n.headers=new a({"Content-type":"application/json"}));let i=await e.fetch(t,n),o=await i.json();if(!i.ok)throw o.status=i.status,s(function(e,t){function n(t){for(var n=Object.getOwnPropertyNames(e),r=0,i=n.length;r<i;r++)"function"!=typeof e[n[r]]&&(this[n[r]]=e[n[r]]);void 0===this.stack&&(this.stack=Error().stack),void 0!==t&&(this.reason=t)}return n.prototype=r.prototype,new n(void 0)}(o));return o}async function eh(e,t){return await ed(e,"_index",{method:"POST",body:es(t)})}async function ep(e,t){return ef(t.selector,!0),await ed(e,"_find",{method:"POST",body:t})}async function ev(e,t){return await ed(e,"_explain",{method:"POST",body:t})}async function ey(e){return await ed(e,"_index",{method:"GET"})}async function eg(e,t){let n=t.ddoc,r=t.type||"json",i=t.name;if(!n)throw Error("you must provide an index's ddoc");if(!i)throw Error("you must provide an index's name");let o="_index/"+[n,r,i].map(encodeURIComponent).join("/");return await ed(e,o,{method:"DELETE"})}function e_(e,t){for(let n of t)if(void 0===(e=e[n]))return;return e}let em=function(e,t,n,r){var i;function o(e,t){let n=c(e.key,t.key);return 0!==n?n:c(e.value,t.value)}function f(e,t,n){return(n=n||0,"number"==typeof t)?e.slice(n,t+n):n>0?e.slice(n):e}function d(e){let t=e.value;return t&&"object"==typeof t&&t._id||e.id}function h(e){return function(t){return e.include_docs&&e.attachments&&e.binary&&function(e){for(let t of e.rows){let e=t.doc&&t.doc._attachments;if(e)for(let t of Object.keys(e)){let n=e[t];e[t].data=function(e,t){var n;return n=atob(e),function(e,t){e=e||[],t=t||{};try{return new Blob(e,t)}catch(i){if("TypeError"!==i.name)throw i;for(var n=new("undefined"!=typeof BlobBuilder?BlobBuilder:"undefined"!=typeof MSBlobBuilder?MSBlobBuilder:"undefined"!=typeof MozBlobBuilder?MozBlobBuilder:WebKitBlobBuilder),r=0;r<e.length;r+=1)n.append(e[r]);return n.getBlob(t.type)}}([function(e){for(var t=e.length,n=new ArrayBuffer(t),r=new Uint8Array(n),i=0;i<t;i++)r[i]=e.charCodeAt(i);return n}(n)],{type:t})}(n.data,n.content_type)}}}(t),t}}function p(e,t,n,r){let i=t[e];void 0!==i&&(r&&(i=encodeURIComponent(JSON.stringify(i))),n.push(e+"="+i))}function v(e){if(void 0!==e){let t=Number(e);return isNaN(t)||t!==parseInt(e,10)?e:t}}function y(e,t){let n=e.descending?"endkey":"startkey",r=e.descending?"startkey":"endkey";if(void 0!==e[n]&&void 0!==e[r]&&c(e[n],e[r])>0)throw new $("No rows can match your key range, reverse your start_key and end_key or set {descending : true}");if(t.reduce&&!1!==e.reduce){if(e.include_docs)throw new $("{include_docs:true} is invalid for reduce");else if(e.keys&&e.keys.length>1&&!e.group&&!e.group_level)throw new $("Multi-key fetches for reduce views must use {group: true}")}for(let t of["group_level","limit","skip"]){let n=function(e){if(e){if("number"!=typeof e)return new $(`Invalid value for integer: "${e}"`);if(e<0)return new $(`Invalid value for positive integer: "${e}"`)}}(e[t]);if(n)throw n}}async function g(e,t,n){let r,i,o=[],c="GET";if(p("reduce",n,o),p("include_docs",n,o),p("attachments",n,o),p("limit",n,o),p("descending",n,o),p("group",n,o),p("group_level",n,o),p("skip",n,o),p("stale",n,o),p("conflicts",n,o),p("startkey",n,o,!0),p("start_key",n,o,!0),p("endkey",n,o,!0),p("end_key",n,o,!0),p("inclusive_end",n,o),p("key",n,o,!0),p("update_seq",n,o),o=""===(o=o.join("&"))?"":"?"+o,void 0!==n.keys){let e=`keys=${encodeURIComponent(JSON.stringify(n.keys))}`;e.length+o.length+1<=2e3?o+=("?"===o[0]?"&":"?")+e:(c="POST","string"==typeof t?r={keys:n.keys}:t.keys=n.keys)}if("string"==typeof t){let u=B(t),l=await e.fetch("_design/"+u[0]+"/_view/"+u[1]+o,{headers:new a({"Content-Type":"application/json"}),method:c,body:JSON.stringify(r)});i=l.ok;let f=await l.json();if(!i)throw f.status=l.status,s(f);for(let e of f.rows)if(e.value&&e.value.error&&"builtin_reduce_error"===e.value.error)throw Error(e.reason);return new Promise(function(e){e(f)}).then(h(n))}for(let e of(r=r||{},Object.keys(t)))Array.isArray(t[e])?r[e]=t[e]:r[e]=t[e].toString();let u=await e.fetch("_temp_view"+o,{headers:new a({"Content-Type":"application/json"}),method:"POST",body:JSON.stringify(r)});i=u.ok;let l=await u.json();if(!i)throw l.status=u.status,s(l);return new Promise(function(e){e(l)}).then(h(n))}function _(e){return function(t){if(404===t.status)return e;throw t}}async function b(e,t,n){let r="_local/doc_"+e,i={_id:r,keys:[]},o=n.get(e),s=o[0],a=o[1],c=await (1===a.length&&/^1-/.test(a[0].rev)?Promise.resolve(i):t.db.get(r).catch(_(i))),u=await (!c.keys.length?Promise.resolve({rows:[]}):t.db.allDocs({keys:c.keys,include_docs:!0}));return function(e,t){let n=[],r=new Set;for(let e of t.rows){let t=e.doc;if(t&&(n.push(t),r.add(t._id),t._deleted=!s.has(t._id),!t._deleted)){let e=s.get(t._id);"value"in e&&(t.value=e.value)}}let i=A(s);for(let e of i)if(!r.has(e)){let t={_id:e},r=s.get(e);"value"in r&&(t.value=r.value),n.push(t)}return e.keys=S(i.concat(e.keys)),n.push(e),n}(c,u)}function k(e){let t="string"==typeof e?e:e.name,n=C[t];return n||(n=C[t]=new E),n}async function P(e,t){return x(k(e),function(){return M(e,t)})()}async function M(e,n){let r,i,s,a=t(e.mapFun,function(e,t){let n={id:i._id,key:u(e)};null!=t&&(n.value=u(t)),r.push(n)}),f=e.seq||0,d=0,h={view:e.name,indexed_docs:d};e.sourceDB.emit("indexing",h);let p=new E;async function v(){return function(t,u){var h;let y=t.results;if(!y.length&&!u.length)return;for(let e of u)if(0>y.findIndex(function(t){return t.id===e.docId})){let t={_id:e.docId,doc:{_id:e.docId,_deleted:1},changes:[]};e.doc&&(t.doc=e.doc,t.changes.push({rev:e.doc._rev})),y.push(t)}let g=function(t){let n=new Map;for(let s of t){if("_"!==s.doc._id[0]){r=[],(i=s.doc)._deleted||function(e,t,n){try{t(n)}catch(r){T(e,r,{fun:t,doc:n})}}(e.sourceDB,a,i),r.sort(o);let t=function(e){let t,n=new Map;for(let r=0,i=e.length;r<i;r++){let i=e[r],o=[i.key,i.id];r>0&&0===c(i.key,t)&&o.push(r),n.set(l(o),i),t=i.key}return n}(r);n.set(s.doc._id,[t,s.changes])}f=s.seq}return n}(y);p.add((h=f,function(){var t;return t="_local/lastSeq",e.db.get(t).catch(_({_id:t,seq:0})).then(function(t){return Promise.all(A(g).map(function(t){return b(t,e,g)})).then(function(n){var r=n.flat();return t.seq=h,r.push(t),e.db.bulkDocs({docs:r})}).then(()=>e.sourceDB.get("_local/purges").then(function(t){let n=t.purgeSeq;return e.db.get("_local/purgeSeq").then(function(e){return e._rev}).catch(_(void 0)).then(function(t){return e.db.put({_id:"_local/purgeSeq",_rev:t,purgeSeq:n})})}).catch(function(e){if(404!==e.status)throw e}))})})),d+=y.length;let m={view:e.name,last_seq:t.last_seq,results_count:y.length,indexed_docs:d};if(e.sourceDB.emit("indexing",m),e.sourceDB.activeTasks.update(s,{completed_items:d}),!(y.length<n.changes_batch_size))return v()}(await e.sourceDB.changes({return_docs:!0,conflicts:!0,include_docs:!0,style:"all_docs",since:f,limit:n.changes_batch_size}),await e.db.get("_local/purgeSeq").then(function(e){return e.purgeSeq}).catch(_(-1)).then(function(t){return e.sourceDB.get("_local/purges").then(function(n){let r=n.purges.filter(function(e,n){return n>t}).map(e=>e.docId);return Promise.all(r.filter(function(e,t){return r.indexOf(e)===t}).map(function(t){return e.sourceDB.get(t).then(function(e){return{docId:t,doc:e}}).catch(_({docId:t}))}))}).catch(_([]))}))}try{await e.sourceDB.info().then(function(t){s=e.sourceDB.activeTasks.add({name:"view_indexing",total_items:t.update_seq-f})}),await v(),await p.finish(),e.seq=f,e.sourceDB.activeTasks.remove(s)}catch(t){e.sourceDB.activeTasks.remove(s,t)}}function L(e,t){return x(k(e),function(){return R(e,t)})()}async function R(e,t){let r,i=e.reduceFun&&!1!==t.reduce,o=t.skip||0;async function s(t){t.include_docs=!0;let n=await e.db.allDocs(t);return r=n.total_rows,n.rows.map(function(e){if("value"in e.doc&&"object"==typeof e.doc.value&&null!==e.doc.value){let t=Object.keys(e.doc.value).sort(),n=["id","key","value"];if(!(t<n||t>n))return e.doc.value}let t=function(e){for(var t=[],n=[],r=0;;){var i=e[r++];if("\0"===i)if(1===t.length)return t.pop();else{!function(e,t){var n=e.pop();if(t.length){var r=t[t.length-1];n===r.element&&(t.pop(),r=t[t.length-1]);var i=r.element,o=r.index;Array.isArray(i)?i.push(n):o===e.length-2?i[e.pop()]=n:e.push(n)}}(t,n);continue}switch(i){case"1":t.push(null);break;case"2":t.push("1"===e[r]),r++;break;case"3":var o=function(e,t){var n,r=t;if("1"===e[t])n=0,t++;else{var i="0"===e[t];t++;var o="",s=parseInt(e.substring(t,t+3),10)+-324;for(i&&(s=-s),t+=3;;){var a=e[t];if("\0"===a)break;o+=a,t++}n=1===(o=o.split(".")).length?parseInt(o,10):parseFloat(o[0]+"."+o[1]),i&&(n-=10),0!==s&&(n=parseFloat(n+"e"+s))}return{num:n,length:t-r}}(e,r);t.push(o.num),r+=o.length;break;case"4":for(var s="";;){var a=e[r];if("\0"===a)break;s+=a,r++}s=s.replace(/\u0001\u0001/g,"\0").replace(/\u0001\u0002/g,"\x01").replace(/\u0002\u0002/g,"\x02"),t.push(s);break;case"5":var c={element:[],index:t.length};t.push(c.element),n.push(c);break;case"6":var u={element:{},index:t.length};t.push(u.element),n.push(u);break;default:throw Error("bad collationIndex or unexpectedly reached end of input: "+i)}}}(e.doc._id);return{key:t[0],id:t[1],value:"value"in e.doc?e.doc.value:null}})}async function a(s){let a;if(a=i?function(e,t,r){0===r.group_level&&delete r.group_level;let i=r.group||r.group_level,o=n(e.reduceFun),s=[],a=isNaN(r.group_level)?Number.POSITIVE_INFINITY:r.group_level;for(let e of t){let t=s[s.length-1],n=i?e.key:null;if(i&&Array.isArray(n)&&(n=n.slice(0,a)),t&&0===c(t.groupKey,n)){t.keys.push([e.key,e.id]),t.values.push(e.value);continue}s.push({keys:[[e.key,e.id]],values:[e.value],groupKey:n})}for(let n of(t=[],s)){let r=function(e,t,n,r,i){try{return{output:t(n,r,i)}}catch(o){return T(e,o,{fun:t,keys:n,values:r,rereduce:i}),{error:o}}}(e.sourceDB,o,n.keys,n.values,!1);if(r.error&&r.error instanceof O)throw r.error;t.push({value:r.error?null:r.output,key:n.groupKey})}return{rows:f(t,r.limit,r.skip)}}(e,s,t):void 0===t.keys?{total_rows:r,offset:o,rows:s}:{total_rows:r,offset:o,rows:f(s,t.limit,t.skip)},t.update_seq&&(a.update_seq=e.seq),t.include_docs){let n=S(s.map(d)),r=await e.sourceDB.allDocs({keys:n,include_docs:!0,conflicts:t.conflicts,attachments:t.attachments,binary:t.binary}),i=new Map;for(let e of r.rows)i.set(e.id,e.doc);for(let e of s){let t=d(e),n=i.get(t);n&&(e.doc=n)}}return a}if(void 0===t.keys||t.keys.length||(t.limit=0,delete t.keys),void 0!==t.keys){let e=t.keys.map(function(e){let n={startkey:l([e]),endkey:l([e,{}])};return t.update_seq&&(n.update_seq=!0),s(n)});return a((await Promise.all(e)).flat())}{let e,n,r={descending:t.descending};if(t.update_seq&&(r.update_seq=!0),"start_key"in t&&(e=t.start_key),"startkey"in t&&(e=t.startkey),"end_key"in t&&(n=t.end_key),"endkey"in t&&(n=t.endkey),void 0!==e&&(r.startkey=t.descending?l([e,{}]):l([e])),void 0!==n){let e=!1!==t.inclusive_end;t.descending&&(e=!e),r.endkey=l(e?[n,{}]:[n])}if(void 0!==t.key){let e=l([t.key]),n=l([t.key,{}]);r.descending?(r.endkey=e,r.startkey=n):(r.startkey=e,r.endkey=n)}return i||("number"==typeof t.limit&&(r.limit=t.limit),r.skip=o),a(await s(r))}}async function N(e){return(await e.fetch("_view_cleanup",{headers:new a({"Content-Type":"application/json"}),method:"POST"})).json()}async function F(t){try{let n=await t.get("_local/"+e),r=new Map;for(let e of Object.keys(n.views)){let t=B(e),n="_design/"+t[0],i=t[1],o=r.get(n);o||(o=new Set,r.set(n,o)),o.add(i)}let i={keys:A(r),include_docs:!0},o=await t.allDocs(i),s={};for(let e of o.rows){let t=e.key.substring(8);for(let i of r.get(e.key)){let r=t+"/"+i;n.views[r]||(r=i);let o=Object.keys(n.views[r]),a=e.doc&&e.doc.views&&e.doc.views[i];for(let e of o)s[e]=s[e]||a}}let a=Object.keys(s).filter(function(e){return!s[e]}).map(function(e){return x(k(e),function(){return new t.constructor(e,t.__opts).destroy()})()});return Promise.all(a).then(function(){return{ok:!0}})}catch(e){if(404===e.status)return{ok:!0};throw e}}async function J(t,n,i){if("function"==typeof t._query){var o;return o=n,new Promise(function(e,n){t._query(o,i,function(t,r){if(t)return n(t);e(r)})})}if(w(t))return g(t,n,i);let s={changes_batch_size:t.__opts.view_update_changes_batch_size||50};if("string"!=typeof n)return y(i,n),I.add(async function(){var r,o;let a=await D(t,"temp_view/temp_view",n.map,n.reduce,!0,e);return r=P(a,s).then(function(){return L(a,i)}),o=function(){return a.db.destroy()},r.then(function(e){return o().then(function(){return e})},function(e){return o().then(function(){throw e})})}),I.finish();{let o=n,a=B(o),c=a[0],u=a[1],l=await t.get("_design/"+c);if(!(n=l.views&&l.views[u]))throw new j(`ddoc ${l._id} has no view named ${u}`);r(l,u),y(i,n);let f=await D(t,o,n.map,n.reduce,!1,e);return"ok"===i.stale||"update_after"===i.stale?"update_after"===i.stale&&m(function(){P(f,s)}):await P(f,s),L(f,i)}}return{query:function(e,t,n){var r;let i=this;"function"==typeof t&&(n=t,t={}),t=t?((r=t).group_level=v(r.group_level),r.limit=v(r.limit),r.skip=v(r.skip),r):{},"function"==typeof e&&(e={map:e});let o=Promise.resolve().then(function(){return J(i,e,t)});return q(o,n),o},viewCleanup:(i=function(){if("function"==typeof this._viewCleanup){var e;return e=this,new Promise(function(t,n){e._viewCleanup(function(e,r){if(e)return n(e);t(r)})})}return w(this)?N(this):F(this)},function(...e){var t=e.pop(),n=i.apply(this,e);return"function"==typeof t&&q(n,t),n})}}("indexes",function(e,t){return function(e,t,n){let r=e.every(e=>-1===e.indexOf(".")),i=1===e.length;if(r)if(i)return o=e[0],function(e){(!n||Z(e,n))&&t(e[o])};else return function(r){(!n||Z(r,n))&&t(e.map(e=>r[e]))};if(i){var o;let r=R(e[0]);return function(e){if(n&&!Z(e,n))return;let i=e_(e,r);void 0!==i&&t(i)}}return function(r){if(n&&!Z(r,n))return;let i=[];for(let t of e){let e=e_(r,R(t));if(void 0===e)return;i.push(e)}t(i)}}(Object.keys(e.fields),t,e.partial_filter_selector)},function(){throw Error("reduce not supported")},function(e,t){let n=e.views[t];if(!n.map||!n.map.fields)throw Error("ddoc "+e._id+" with view "+t+" doesn't have map.fields defined. maybe it wasn't created by this plugin?")});function eb(e){return e._customFindAbstractMapper?{query:function(t,n){let r=em.query.bind(this);return e._customFindAbstractMapper.query.call(this,t,n,r)},viewCleanup:function(){let t=em.viewCleanup.bind(this);return e._customFindAbstractMapper.viewCleanup.call(this,t)}}:em}let ew=/^_design\//;function ek(e){return e.fields=e.fields.map(function(e){if("string"==typeof e){let t={};return t[e]="asc",t}return e}),e.partial_filter_selector&&(e.partial_filter_selector=K(e.partial_filter_selector)),e}async function e$(e,t){let n,r=_((t=es(t)).index);t.index=ek(t.index);var i=t.index;let o=i.fields.filter(function(e){return"asc"===e[J(e)]});if(0!==o.length&&o.length!==i.fields.length)throw Error("unsupported mixed sorting");function s(){var e;return n||(e=JSON.stringify(t),n=v().hash(e))}let a=t.name||"idx-"+s(),c=t.ddoc||"idx-"+s(),u="_design/"+c,l=!1,f=!1;if(e.constructor.emit("debug",["find","creating index",u]),await k(e,u,function(e){return e._rev&&"query"!==e.language&&(l=!0),e.language="query",e.views=e.views||{},!(f=!!e.views[a])&&(e.views[a]={map:{fields:en(t.index.fields),partial_filter_selector:t.index.partial_filter_selector},reduce:"_count",options:{def:r}},e)}),l)throw Error('invalid language for ddoc with id "'+u+'" (should be "query")');return await eb(e).query.call(e,c+"/"+a,{limit:0,reduce:!1}),{id:u,name:a,result:f?"exists":"created"}}async function ej(e){let t=await e.allDocs({startkey:"_design/",endkey:"_design/￿",include_docs:!0}),n={indexes:[{ddoc:null,name:"_all_docs",type:"special",def:{fields:[{_id:"asc"}]}}]};return n.indexes=et(n.indexes,t.rows.filter(function(e){return"query"===e.doc.language}).map(function(e){return(void 0!==e.doc.views?Object.keys(e.doc.views):[]).map(function(t){let n=e.doc.views[t];return{ddoc:e.id,name:t,type:"json",def:ek(n.options.def)}})})),n.indexes.sort(function(e,t){var n,r;return n=e.name,n<(r=t.name)?-1:+(n>r)}),n.total_rows=n.indexes.length,n}let eO={"￿":{}},eq={queryOpts:{limit:0,startkey:eO,endkey:null},inMemoryFields:[]};function ex(e,t){let n=t.def.fields.map(J);return e.slice().sort(function(e,t){var r,i;let o=n.indexOf(e),s=n.indexOf(t);return -1===o&&(o=Number.MAX_VALUE),-1===s&&(s=Number.MAX_VALUE),(r=o)<(i=s)?-1:+(r>i)})}let eS=["$eq","$gt","$gte","$lt","$lte"];function eA(e){return -1===eS.indexOf(e)}async function eE(e,t){let n=_(t);n.descending?("endkey"in n&&"string"!=typeof n.endkey&&(n.endkey=""),"startkey"in n&&"string"!=typeof n.startkey&&(n.limit=0)):("startkey"in n&&"string"!=typeof n.startkey&&(n.startkey=""),"endkey"in n&&"string"!=typeof n.endkey&&(n.limit=0)),"key"in n&&"string"!=typeof n.key&&(n.limit=0),n.limit>0&&n.indexes_count&&(n.original_limit=n.limit,n.limit+=n.indexes_count);let r=await e.allDocs(n);return r.rows=r.rows.filter(function(e){return!/^_design\//.test(e.id)}),n.original_limit&&(n.limit=n.original_limit),r.rows=r.rows.slice(0,n.limit),r}async function eP(e,t,n){return"_all_docs"===n.name?eE(e,t):eb(e).query.call(e,n.ddoc.substring(8)+"/"+n.name,t)}async function eD(e,t,n){let r;if(t.selector&&(ef(t.selector,!1),t.selector=K(t.selector)),t.sort&&(t.sort=function(e){if(!Array.isArray(e))throw Error("invalid sort json - should be an array");return e.map(function(e){if("string"!=typeof e)return e;{let t={};return t[e]="asc",t}})}(t.sort)),t.use_index&&(a=t.use_index,r=[],"string"==typeof a?r.push(a):r=a,t.use_index=r.map(function(e){return e.replace(ew,"")})),"limit"in t||(t.limit=25),"object"!=typeof t.selector)throw Error("you must provide a selector when you find()");let i=await ej(e);e.constructor.emit("debug",["find","planning query",t]);let o=function(e,t){var n,r;let i=e.selector,o=e.sort;if(Object.keys(i).map(function(e){return i[e]}).some(function(e){return"object"==typeof e&&0===Object.keys(e).length}))return Object.assign({},eq,{index:t[0]});let s=function(e,t){let n,r=Object.keys(e),i=t?t.map(J):[];return(n=r.length>=i.length?r:i,0===i.length)?{fields:n}:{fields:n=n.sort(function(e,t){let n=i.indexOf(e);-1===n&&(n=Number.MAX_VALUE);let r=i.indexOf(t);return -1===r&&(r=Number.MAX_VALUE),n<r?-1:+(n>r)}),sortOrder:t.map(J)}}(i,o),a=s.fields,c=function(e,t,n,r,i){let o=r.filter(function(r){let i=r.def.fields.map(J);if(!function(e,t,n){if(t){let r=!(t.length>e.length)&&er(t,e),i=er(n,e);return r&&i}var r=n;for(let t of(r=r.slice(),e)){if(!r.length)break;let e=r.indexOf(t);if(-1===e)return!1;r.splice(e,1)}return!0}(i,n,t))return!1;let o=e[i[0]];return void 0===o||1!==Object.keys(o).length||"$ne"!==J(o)});if(0===o.length){if(i)throw{error:"no_usable_index",message:"There is no index available for this selector."};let e=r[0];return e.defaultUsed=!0,e}if(1===o.length&&!i)return o[0];let s=function(e){let t={};for(let n of e)t[n]=!0;return t}(t);if(i){let e="_design/"+i[0],t=2===i.length&&i[1],n=o.find(function(n){return!!t&&n.ddoc===e&&t===n.name||n.ddoc===e});if(!n)throw{error:"unknown_error",message:"Could not find that index or could not use that index for the query"};return n}return function(e,t){let n=null,r=-1;for(let i of e){let e=t(i);e>r&&(r=e,n=i)}return n}(o,function(e){let t=e.def.fields.map(J),n=0;for(let e of t)s[e]&&n++;return n})}(i,a,s.sortOrder,t,e.use_index),u=(n=i,(r=c).defaultUsed?{queryOpts:{startkey:null},inMemoryFields:[Object.keys(n)]}:1===r.def.fields.length?function(e,t){let n,r=J(t.def.fields[0]),i=e[r]||{},o=[];for(let e of Object.keys(i)){eA(e)&&o.push(r);let t=i[e],s=function(e,t){switch(e){case"$eq":return{key:t};case"$lte":return{endkey:t};case"$gte":return{startkey:t};case"$lt":return{endkey:t,inclusive_end:!1};case"$gt":return{startkey:t,inclusive_start:!1}}return{startkey:null}}(e,t);n=n?en([n,s]):s}return{queryOpts:n,inMemoryFields:o}}(n,r):function(e,t){let n,r,i=t.def.fields.map(J),o=[],s=[],a=[];function c(e){!1!==n&&s.push(null),!1!==r&&a.push(eO),o=i.slice(e)}for(let t=0,o=i.length;t<o;t++){let o=e[i[t]];if(o&&Object.keys(o).length){if(Object.keys(o).some(eA)){c(t);break}else if(t>0){let n="$gt"in o||"$gte"in o||"$lt"in o||"$lte"in o,r=Object.keys(e[i[t-1]]),s=ei(r,["$eq"]),a=ei(r,Object.keys(o));if(n&&!s&&!a){c(t);break}}}else{c(t);break}let u=Object.keys(o),l=null;for(let e of u){let t=o[e],n=function(e,t){switch(e){case"$eq":return{startkey:t,endkey:t};case"$lte":return{endkey:t};case"$gte":return{startkey:t};case"$lt":return{endkey:t,inclusive_end:!1};case"$gt":return{startkey:t,inclusive_start:!1}}}(e,t);l=l?en([l,n]):n}s.push("startkey"in l?l.startkey:null),a.push("endkey"in l?l.endkey:eO),"inclusive_start"in l&&(n=l.inclusive_start),"inclusive_end"in l&&(r=l.inclusive_end)}let u={startkey:s,endkey:a};return void 0!==n&&(u.inclusive_start=n),void 0!==r&&(u.inclusive_end=r),{queryOpts:u,inMemoryFields:o}}(n,r)),l=u.queryOpts,f=function(e,t,n,r){let i=et(e,function(e,t,n){n=ex(n,e);let r=!1;for(let i=0,o=n.length;i<o;i++){let s=n[i];if(r||!function(e,t){return e.def.fields.some(e=>J(e)===t)}(e,s))return n.slice(i);i<o-1&&"$eq"!==J(t[s])&&(r=!0)}return[]}(t,n,r),function(e){let t=[];for(let[n,r]of Object.entries(e))for(let e of Object.keys(r))"$ne"===e&&t.push(n);return t}(n));return ex(Array.from(new Set(i)),t)}(u.inMemoryFields,c,i,a);return{queryOpts:l,index:c,inMemoryFields:f}}(t,i.indexes);e.constructor.emit("debug",["find","query plan",o]);let s=o.index;var a,u,l=t,f=s;if(f.defaultUsed&&l.sort){let e=l.sort.filter(function(e){return"_id"!==Object.keys(e)[0]}).map(function(e){return Object.keys(e)[0]});if(e.length>0)throw Error('Cannot sort on field(s) "'+e.join(",")+'" when using the default index')}f.defaultUsed;let d=Object.assign({include_docs:!0,reduce:!1,indexes_count:i.total_rows},o.queryOpts);if("startkey"in d&&"endkey"in d&&c(d.startkey,d.endkey)>0)return{docs:[]};if(t.sort&&"string"!=typeof t.sort[0]&&"desc"===(u=t.sort[0])[J(u)]&&(d.descending=!0,d=function(e){let t=_(e);return delete t.startkey,delete t.endkey,delete t.inclusive_start,delete t.inclusive_end,"endkey"in e&&(t.startkey=e.endkey),"startkey"in e&&(t.endkey=e.startkey),"inclusive_start"in e&&(t.inclusive_end=e.inclusive_start),"inclusive_end"in e&&(t.inclusive_start=e.inclusive_end),t}(d)),!o.inMemoryFields.length&&(d.limit=t.limit,"skip"in t&&(d.skip=t.skip)),n)return Promise.resolve(o,d);let h=await eP(e,d,s);!1===d.inclusive_start&&(h.rows=function(e,t,n){let r=n.def.fields,i=0;for(let o of e){let e=function(e,t){return t.def.fields.map(t=>M(e,R(J(t))))}(o.doc,n);if(1===r.length)e=e[0];else for(;e.length>t.length;)e.pop();if(Math.abs(c(e,t))>0)break;++i}return i>0?e.slice(i):e}(h.rows,d.startkey,s)),o.inMemoryFields.length&&(h.rows=U(h.rows,t,o.inMemoryFields));let p={docs:h.rows.map(function(e){let n=e.doc;return t.fields?function(e,t){let n={};for(let r of t){let t=R(r),i=M(e,t);void 0!==i&&function(e,t,n){for(var r=0,i=t.length;r<i-1;r++){var o=t[r];e=e[o]=e[o]||{}}e[t[i-1]]=n}(n,t,i)}return n}(n,t.fields):n})};return s.defaultUsed&&(p.warning="No matching index found, create an index to optimize query time."),p}async function eC(e,t){let n=await eD(e,t,!0);return{dbname:e.name,index:n.index,selector:t.selector,range:{start_key:n.queryOpts.startkey,end_key:n.queryOpts.endkey},opts:{use_index:t.use_index||[],bookmark:"nil",limit:t.limit,skip:t.skip,sort:t.sort||{},fields:t.fields,conflicts:!1,r:[49]},limit:t.limit,skip:t.skip||0,fields:t.fields}}async function eI(e,t){if(!t.ddoc)throw Error("you must supply an index.ddoc when deleting");if(!t.name)throw Error("you must supply an index.name when deleting");let n=t.ddoc,r=t.name;return await k(e,n,function(e){return 1===Object.keys(e.views).length&&e.views[r]?{_id:n,_deleted:!0}:(delete e.views[r],e)}),await eb(e).viewCleanup.apply(e),{ok:!0}}let eB={};eB.createIndex=eo(async function(e){if("object"!=typeof e)throw Error("you must provide an index to create");return(w(this)?eh:e$)(this,e)}),eB.find=eo(async function(e){if("object"!=typeof e)throw Error("you must provide search parameters to find()");return(w(this)?ep:eD)(this,e)}),eB.explain=eo(async function(e){if("object"!=typeof e)throw Error("you must provide search parameters to explain()");return(w(this)?ev:eC)(this,e)}),eB.getIndexes=eo(async function(){return(w(this)?ey:ej)(this)}),eB.deleteIndex=eo(async function(e){if("object"!=typeof e)throw Error("you must provide an index to delete");return(w(this)?eg:eI)(this,e)});let eT=eB}}]);