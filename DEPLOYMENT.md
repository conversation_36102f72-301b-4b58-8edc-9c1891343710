# Deployment Guide

## Cloudflare Pages Deployment

This application is configured to deploy to Cloudflare Pages and connect to the database proxy worker.

### Prerequisites

1. Ensure you have wrangler installed:
```bash
npm install -g wrangler
```

2. Login to <PERSON>flare:
```bash
wrangler login
```

### Setting up Secrets

Before deploying, you need to set up the following secrets using wrangler:

```bash
# Set the database password
wrangler secret put ITHINC_DB_PASSWORD

# Set the Cloudflare Access credentials
wrangler secret put ITHINC_DB_CF_CLIENT_ID
wrangler secret put ITHINC_DB_CF_CLIENT_SECRET
```

### Deployment

The deployment is configured to happen automatically when you run:

```bash
npm run build
```

This will:
1. Build the React application
2. Deploy to Cloudflare Pages using wrangler

### Environment Variables

The following environment variables are configured in wrangler.toml:

- `ITHINC_DB_REMOTE_URL`: Points to the database proxy worker
- `ITHINC_DB_LOCAL_NAME`: Local database name
- `ITHINC_DB_REMOTE_NAME`: Remote database name
- `ITHINC_DB_USERNAME`: Database username

Secrets (must be set manually):
- `ITHINC_DB_PASSWORD`: Database password
- `ITHINC_DB_CF_CLIENT_ID`: Cloudflare Access client ID
- `ITHINC_DB_CF_CLIENT_SECRET`: Cloudflare Access client secret

### Troubleshooting

If you encounter deployment issues:

1. Ensure all secrets are properly set using:
```bash
wrangler secret list
```

2. Verify the environment variables in wrangler.toml match your database proxy configuration

3. Check the Cloudflare Pages deployment logs for any errors