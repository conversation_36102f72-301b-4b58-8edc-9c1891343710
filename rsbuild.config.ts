import { defineConfig } from '@rsbuild/core';
import { pluginReact } from '@rsbuild/plugin-react';
import { pluginSvgr } from '@rsbuild/plugin-svgr';
import { pluginTypeCheck } from '@rsbuild/plugin-type-check';

const ANALYZE = process.env.ANALYZE === 'true';

export default defineConfig({
  plugins: [
    pluginReact(),
    // Ensure SVGs are handled correctly, similar to @svgr/webpack
    // `svgrOptions` can be added if specific SVGR config is needed
    pluginSvgr({ svgrOptions: { /* ... */ } }),
    // Enable TypeScript type checking during build
    pluginTypeCheck(),
  ],
  source: {
    // Define entry point
    entry: {
      index: './src/index.tsx',
    },
    // Replicate resolve.fallback behavior by not providing polyfills
    // Rsbuild generally avoids injecting Node polyfills by default.
    define: {
      // Define process.env as an object containing NODE_ENV
      // This helps libraries that check for `process.env` before accessing properties,
      // especially since `resolve.fallback: { process: false }` is set.
      'process.env': JSON.stringify({
        NODE_ENV: process.env.NODE_ENV || 'development',
        PUBLIC_URL: '',
      }),
      // Define %PUBLIC_URL% as a global variable to replace placeholders in HTML
      '%PUBLIC_URL%': JSON.stringify(''),
    },
  },
  output: {
    // Configure CSS Modules: `auto: true` enables *.module.css and uses camelCase convention by default.
    cssModules: {
      auto: true,
    },
    // Enable asset fallback for handling files not explicitly matched
    assetPrefix: '/', // Adjust if using CDN or different base path
  },
  tools: {
    // Configure Rsbuild's bundler (Rspack)
    rspack: (config, { env }) => {
      // --- Handle .md files as raw text ---
      config.module ??= {};
      config.module.rules ??= [];
      config.module.rules.push({
        test: /\.md$/,
        type: 'asset/source', // Load as raw string
      });

      // --- Replicate resolve.fallback ---
      // Rsbuild/Rspack aims to avoid Node polyfills. Setting resolve.fallback to false
      // is the default behavior for many modules. If specific errors arise,
      // we might need `resolve.alias` or `provide` for specific shims, but
      // let's start without explicit fallbacks set to `false`.
      config.resolve ??= {};
      config.resolve.fallback ??= {};
      // Explicitly disable polyfills that were previously disabled
      const fallbacksToDisable = ['crypto', 'stream', 'buffer', 'util', 'process'];
      for (const item of fallbacksToDisable) {
        config.resolve.fallback[item] = false;
      }

      // --- Configure Bundle Analysis ---
      if (ANALYZE) {
        const { BundleAnalyzerPlugin } = require('webpack-bundle-analyzer');
        config.plugins ??= [];
        config.plugins.push(new BundleAnalyzerPlugin());
        // Note: Need to install webpack-bundle-analyzer if keeping this exact plugin
        // Alternatively, use Rsbuild's built-in stats: `rsbuild build --profile`
      }

      // --- Production specific optimizations ---
      if (env === 'production') {
        // --- Custom SplitChunks ---
        config.optimization ??= {};
        // Ensure splitChunks is an object before modifying cacheGroups
        if (typeof config.optimization.splitChunks !== 'object') {
          config.optimization.splitChunks = {};
        }
        config.optimization.splitChunks ??= {};
        config.optimization.splitChunks.cacheGroups ??= {};

        // Create specific chunks based on the old config
        Object.assign(config.optimization.splitChunks.cacheGroups, {
          mui: {
            test: /[\\/]node_modules[\\/](@mui|@emotion)[\\/]/,
            name: 'vendors-mui',
            chunks: 'all',
            priority: 30,
            reuseExistingChunk: true,
          },
          react: {
            test: /[\\/]node_modules[\\/](react|react-dom|react-router-dom)[\\/]/,
            name: 'vendors-react',
            chunks: 'all',
            priority: 40, // Higher priority for core react libs
            reuseExistingChunk: true,
          },
          pouchdb: {
            test: /[\\/]node_modules[\\/](pouchdb.*)[\\/]/,
            name: 'vendors-pouchdb',
            chunks: 'all',
            priority: 20,
            reuseExistingChunk: true,
          },
          // Keep default vendors chunk for other node_modules
          vendors: {
             test: /[\\/]node_modules[\\/]/,
             name: 'vendors',
             chunks: 'all',
             priority: -10, // Lower priority
             reuseExistingChunk: true,
          },
        });

        // --- Minification ---
        // Rsbuild uses SWC by default. `performance.removeConsole` handles console removal.
        // Check if SWC's defaults (including potential Safari 10 compatibility) are sufficient.
        // If specific Terser options (like safari10: true) are strictly needed,
        // consider using `@rsbuild/plugin-terser`.
      }

      // --- Side Effects ---
      // Rsbuild/Rspack respects `sideEffects` in package.json by default.
      // The previous `config-overrides.js` had a rule marking all node_modules
      // as sideEffect-free except CSS. This is generally handled well by modern
      // bundlers reading package.json flags. Verify build output; explicit
      // rules might not be needed unless specific issues arise.
      // config.module.rules.push({
      //   test: /[\\/]node_modules[\\/].+\.(js|mjs|jsx|ts|tsx)$/,
      //   sideEffects: false,
      // });
      // config.module.rules.push({
      //   test: /[\\/]node_modules[\\/].+\.css$/,
      //   sideEffects: true,
      // });

      return config;
    },
  },
  performance: {
    // Remove all console.* calls in production builds
    removeConsole: true,
    chunkSplit: {
      // Strategy: 'custom' uses the cacheGroups defined in tools.rspack
      strategy: 'custom',
    },
  },
  html: {
    // Ensure the template is used
    template: './public/index.html',
    // Define variables accessible in index.html
    templateParameters: {
      PUBLIC_URL: '',
    },
  },
});