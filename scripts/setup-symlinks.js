const fs = require('fs');
const path = require('path');

// Create node_modules/@app directory if it doesn't exist
const appModulePath = path.join(__dirname, '../node_modules/@app');
if (!fs.existsSync(appModulePath)) {
    fs.mkdirSync(appModulePath, { recursive: true });
}

// Create symlink for CHANGELOG.md
const changelogSource = path.join(__dirname, '../CHANGELOG.md');
const changelogTarget = path.join(appModulePath, 'changelog.md');

try {
    // Remove existing symlink if it exists
    if (fs.existsSync(changelogTarget)) {
        fs.unlinkSync(changelogTarget);
    }
    
    // Create new symlink
    fs.symlinkSync(
        path.relative(path.dirname(changelogTarget), changelogSource),
        changelogTarget
    );
    console.log('Successfully created changelog symlink');
} catch (error) {
    console.error('Error creating symlink:', error);
    process.exit(1);
}