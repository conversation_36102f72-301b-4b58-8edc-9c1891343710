## [0.7.9] - 2025-02-12

### Changed
- Enhanced database sync system efficiency and reliability
  - Implemented debounced sync operations (1 second delay)
  - Added throttling to prevent rapid updates (5 second minimum)
  - Improved memory management with proper cleanup
  - Enhanced state management in FestivalContext
  - Added optimistic updates for better UX
  - Improved handling of Cloudflare worker cold starts

### Fixed
- Updated CORS configuration in database proxy worker
  - Combined development and production allowed origins
  - Fixed festival management page sync issues
  - Ensured consistent behavior regardless of dev console state

## [0.7.8] - 2025-02-12

### Added
- Added comprehensive user documentation
  - Created detailed guides for all main system pages
  - Added documentation for feedback button functionality
  - Included placeholders for future screenshots
  - Improved clarity on Front of House item logging purpose

### Changed
- Removed documentation for non-functional shift features
- Updated system documentation to accurately reflect current features

## [0.7.7] - 2025-02-11

### Fixed
- Fixed incorrect worker name in configuration
  - Changed worker name from 'database-proxy-production' to 'database-proxy'
  - Redeployed worker with correct configuration
  - Updated worker URL to database-proxy.brisflix.workers.dev

## [0.7.6] - 2025-02-11

### Fixed
- Improved database sync behavior to reduce Cloudflare worker requests
  - Added manual sync button to Front of House page
  - Removed automatic periodic syncs
  - Added pending changes tracking to prevent unnecessary syncs
  - Modified sync behavior to only sync on:
    * Page load
    * Manual sync button press
    * After data changes (with pending changes tracking)
  - Fixed sync issues that required developer console to be open

## [0.7.5] - 2025-02-11

### Added
- Added configurable menu visibility in festival management
  - Added checkboxes to control visibility of Admissions, Front of House, and Lost Property
  - Added visibility controls to both new festival form and edit festival modal
  - Updated sidebar to respect visibility settings for each festival
  - Improved festival customization options for different event types

## [0.7.4] - 2025-02-10

### Added
- Added PDF report generation functionality
  - Implemented PDF preview and download capabilities
  - Added comprehensive report layout including admissions, front of house, and lost property data
  - Added PDF export buttons to reports interface
  - Integrated @react-pdf/renderer for PDF generation
  - Included admission reasons, age distribution, item totals, and lost property status in reports

## [0.7.3] - 2025-02-10

### Added
- Added collapsible sidebar for improved responsiveness
  - Added menu button to toggle sidebar collapse
  - Implemented smooth width transitions
  - Added tooltips for navigation items in collapsed state
  - Made main content area responsive to sidebar state
  - Improved mobile landscape mode readability

## [0.7.2] - 2025-02-09

### Added
- Enhanced admission management functionality
  - Added confirmation dialog for discharge action
  - Added bay availability check for readmission
  - Added confirmation dialog for readmission
  - Improved readmission notes with timestamp and additional notes

## [0.7.1] - 2025-02-09

### Fixed
- Improved database initialization and sync reliability
  - Added timeout and retry logic to prevent infinite loading states
  - Enhanced error handling in database initialization
  - Made sync process more resilient to network failures
  - Fixed TypeScript error with fixInconsistentAdmissions method
  - Added proper cleanup of database resources
  - Improved handling of Cloudflare Workers cold starts
  
## [0.7.0] - 2025-02-09

### Added
- Added user feedback system
  - Implemented floating feedback button available across all pages
  - Added feedback modal with name and comment fields
  - Automatic capture of current page context
  - Created feedback database with sync support
  - Added feedback management interface in admin panel
  - Integrated with existing database sync system
  - Added to data cleanup and export functionality

## [0.6.11] - 2025-02-09

### Changed
- Improved Lost Property page functionality
  - Fixed unsubscribe error in sync listener cleanup
  - Removed unused file upload functionality
  - Improved handling of returned item dates
  - Enhanced form layout and user experience

## [0.6.10] - 2025-02-09

### Fixed
- Improved database configuration handling
  - Added dynamic import of secrets file to handle missing file gracefully
  - Made configuration loading asynchronous for better error handling
  - Added proper error recovery when secrets file is not available
  - Improved initialization process for database configuration
  - Enhanced logging for configuration loading process

## [0.6.9] - 2025-02-08

### Added
- Added environment variable support for database configuration
  - Added fallback system: environment variables -> secrets file -> default config
  - Added support for ITHINC_DB_* environment variables
  - Improved deployment flexibility for platforms like Sevalla
  - Enhanced error handling and logging for configuration loading
  - Maintained backward compatibility with existing secrets.ts setup

## [0.6.8] - 2025-02-08

### Changed
- Improved database sync efficiency and reliability
  - Implemented debounced sync operations (1 second delay)
  - Added throttling to prevent rapid updates (minimum 5 seconds between updates)
  - Improved memory management with proper cleanup of event listeners
  - Enhanced state management in FestivalContext to prevent unnecessary rerenders
  - Added optimistic updates for better user experience
  - Improved handling of Cloudflare worker cold starts
  - Added proper cleanup of database connections and handlers

## [0.6.7] - 2025-02-08

### Changed
- Enhanced system stability and performance for live deployment
  - Improved conflict resolution in database sync
  - Added comprehensive pre-deployment testing procedures
  - Enhanced system monitoring capabilities
  - Optimized performance for production environment
  - Added rollback procedures for deployment safety

## Archive

## [0.6.5] - 2025-02-07

### Changed
- Improved festival links accessibility in sidebar
  - Updated festival links to use icon button for better visibility
  - Positioned links button next to active festival name
  - Improved icon styling and hover states
  - Maintained dropdown menu functionality for accessing festival resources

## [0.5.0] - 2025-02-06

### Changed
- Fixed time since admission calculation to use Attended time instead of createdAt
  - Updated AdmissionList component to use correct timestamp
  - Ensures accurate tracking of patient admission duration
  - Better aligns with clinical workflow where admission time is set during form completion
- Improved database sync performance and reliability
  - Increased batch size from 25 to 100 for better sync efficiency
  - Enabled WebSocket support for more reliable sync
  - Unified timeout settings across sync configurations
  - Added improved retry mechanism with exponential backoff
  - Enhanced error handling and recovery for sync failures
  - Added connection testing and better sync status management
- Migrated from Tailwind CSS to Material UI
  - Converted all components to use Material UI components and styling
  - Removed Tailwind CSS dependencies and configuration
  - Updated styling system to use Material UI's sx prop and styled components
  - Improved component consistency and theme management
  - Removed @headlessui/react dependency
  - Synchronized Material UI package versions
  - Removed tailwind.config.js and postcss.config.js

## [0.3.3] - 2025-02-05

### Changed
- Improved database sync performance and reliability
  - Increased batch size from 25 to 100 for better sync efficiency
  - Enabled WebSocket support for more reliable sync
  - Unified timeout settings across sync configurations
  - Added improved retry mechanism with exponential backoff
  - Enhanced error handling and recovery for sync failures
  - Added connection testing and better sync status management

## [0.3.2] - 2025-02-05

### Changed
- Migrated from Tailwind CSS to Material UI
  - Converted all components to use Material UI components and styling
  - Removed Tailwind CSS dependencies and configuration
  - Updated styling system to use Material UI's sx prop and styled components
  - Improved component consistency and theme management
  - Removed @headlessui/react dependency
  - Synchronized Material UI package versions
  - Removed tailwind.config.js and postcss.config.js

## [0.3.1] - 2025-02-03

### Fixed
- Fixed data grid issues in reports page
  - Added proper null checks in useReportData hook
  - Improved error handling and loading states
  - Fixed row destructuring errors in table components
  - Added proper typing and null checks in all table components
  - Added fallback values for undefined data

## [0.2.67] - 2025-02-02

### Fixed
- Fixed "Invalid time value" errors in admission list
  - Added proper date validation before using date formatting functions
  - Improved error handling for invalid dates
  - Removed problematic fallback to AdmissionNotes for createdAt
  - Added fallback display values for invalid dates
  - Fixed date handling in admission manager

## [0.2.66] - 2025-01-02

### Fixed
- Resolved festival state management inconsistency
  - Synchronized FestivalContext's active festival with database isActive flag
  - Updated festival activation/deactivation logic to maintain single source of truth
  - Improved state management when switching between festivals
  - Added automatic database updates when changing active festival

## [0.2.65] - 2025-01-02

### Fixed
- Improved offline operation behavior
  - Fixed unresponsive UI when creating new admissions offline
  - Fixed error display when discharging patients offline
  - Modified database operations to save locally first, then sync in background
  - Added better error handling for background sync failures

## [0.2.64] - 2024-12-22

### Fixed
- Fixed database connection and sync issues
  - Added development proxy to handle CORS and authentication
  - Improved environment-specific authentication handling
  - Fixed Cloudflare Access and CouchDB auth combination
  - Simplified fetch configuration for better reliability
  - Added better error handling and logging for sync issues

## [0.2.63] - 2024-12-22

### Fixed
- Fixed database authentication issues
  - Fixed Cloudflare Access and CouchDB authentication combination
  - Now properly sends both Cloudflare Access headers and basic auth credentials
  - Resolved 401 unauthorized errors when accessing database
  - Improved authentication error handling and logging

## [0.2.62] - 2024-12-22

### Changed
- Improved database authentication handling
  - Added support for Cloudflare Access authentication
  - Added fallback to basic auth when Cloudflare Access is not configured
  - Improved error messages for authentication failures
  - Restructured configuration to better handle Cloudflare Access credentials

## [0.2.61] - 2024-12-22

### Fixed
- Improved database sync reliability
  - Fixed sync issues in incognito mode
  - Added better error handling for network issues
  - Implemented exponential backoff for sync retries
  - Added more detailed error messages
  - Reduced batch size for better stability

## [0.2.60] - 2024-12-22

### Added
- Added shift management to admin panel
  - Added ability to download shift assignments as CSV
  - Added ability to clear all shift assignments
  - Integrated with existing admin panel interface
  - Added to festival management page

## [0.2.59] - 2024-12-22

### Changed
- Improved front of house data storage efficiency
  - Changed from creating new records for each button press to updating daily records
  - Items are now tracked with cumulative counts per day
  - Each item's history can be tracked through document revision timestamps
  - Reduced database size and improved query performance
- Enhanced front of house table display
  - Added count column to show total items given out
  - Improved timestamp display for better tracking
  - Only shows items with non-zero counts

## [0.2.58] - 2024-12-22

### Fixed
- Fixed team leader assignment in shift schedule
  - Fixed issue with team leader not being saved properly
  - Combined team and leader updates into single operation
  - Fixed "Fill Rotating Pattern" team leader detection
  - Fixed team rotation pattern order (A -> B -> C -> D -> E)
  - Added better error handling for team assignments

## [0.2.57] - 2024-12-22

### Added
- Added column-wise team rotation pattern
  - Added "Fill Rotating Pattern" button to shift schedule
  - Starts from Team A's position and fills remaining shifts
  - Follows A -> E -> D -> C -> B pattern down each column
  - Automatically assigns team leaders based on team letter
  - Preserves existing Team A position as starting point
  - Handles column wrapping to next day automatically