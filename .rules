Each time you make a major change to the project you will record it in the changelog
You will update the version in the counter at the bottom of the sidebar and package.json for every major update to the project
I am using pouchdb for local storage and couchdb for remote storage, keeping data integrity is important
app_overview.md is the documentation for the project, refer to it for more information
there will usually be a local development server running on http://localhost:3000
if not the command to start the server is npm run start
