{"name": "@cloudflare/kv-asset-handler", "version": "0.3.4", "description": "Routes requests to KV assets", "keywords": ["kv", "cloudflare", "workers", "wrangler", "assets"], "homepage": "https://github.com/cloudflare/workers-sdk#readme", "bugs": {"url": "https://github.com/cloudflare/workers-sdk/issues"}, "repository": {"type": "git", "url": "git+https://github.com/cloudflare/workers-sdk.git", "directory": "packages/kv-asset-handler"}, "license": "MIT OR Apache-2.0", "author": "<EMAIL>", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["src", "dist", "!src/test", "!dist/test"], "dependencies": {"mime": "^3.0.0"}, "devDependencies": {"@ava/typescript": "^4.1.0", "@cloudflare/workers-types": "^4.20240620.0", "@types/mime": "^3.0.4", "@types/node": "20.8.3", "@types/service-worker-mock": "^2.0.1", "ava": "^6.0.1", "service-worker-mock": "^2.0.5"}, "engines": {"node": ">=16.13"}, "volta": {"extends": "../../package.json"}, "publishConfig": {"access": "public"}, "workers-sdk": {"prerelease": true}, "scripts": {"build": "tsc -d", "check:lint": "eslint .", "check:type": "tsc", "pretest": "npm run build", "test": "ava dist/test/*.js --verbose", "test:ci": "npm run build && ava dist/test/*.js --verbose"}}