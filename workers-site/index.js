import { getAsset<PERSON>romKV } from '@cloudflare/kv-asset-handler';

async function handleEvent(event) {
  const url = new URL(event.request.url);
  const userEmail = event.request.headers.get('Cf-Access-Authenticated-User-Email');
  
  try {
    // First try to get the asset directly (for static files)
    let response = await getAssetFromKV(event);

    // Add CORS header for manifest.json
    if (url.pathname === '/manifest.json' && response.status === 200) {
      const headers = new Headers(response.headers);
      headers.set('Access-Control-Allow-Origin', '*');
      response = new Response(response.body, { status: response.status, headers });
    }
    
    // Process HTML files to replace %PUBLIC_URL% placeholders
    const contentType = response.headers.get('content-type') || '';
    if (contentType.includes('text/html') && response.status === 200) {
      // Get the HTML content
      let html = await response.text();
      
      // Replace all occurrences of %PUBLIC_URL% with empty string (root path)
      html = html.replace(/%PUBLIC_URL%/g, '');
      
      // Create new headers
      const headers = new Headers(response.headers);
      
      // If we have a user email, add it to the headers
      if (userEmail) {
        // Set a secure, httpOnly cookie with the email
        headers.set('Set-Cookie', `auth_user_email=${userEmail}; Path=/; Secure; SameSite=Strict`);
        
        // Also add it as a custom header for easier access in the frontend
        headers.set('X-Auth-User-Email', userEmail);
      }
      
      // Ensure proper caching for client-side routes
      headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
      
      // Create a new response with the modified HTML
      response = new Response(html, {
        status: response.status,
        headers,
      });
    } else if (userEmail && response.status === 200) {
      // For non-HTML files, just add the user email headers if needed
      const headers = new Headers(response.headers);
      
      // Set a secure, httpOnly cookie with the email
      headers.set('Set-Cookie', `auth_user_email=${userEmail}; Path=/; Secure; SameSite=Strict`);
      
      // Also add it as a custom header for easier access in the frontend
      headers.set('X-Auth-User-Email', userEmail);
      
      // Ensure proper caching for client-side routes
      headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
      
      response = new Response(response.body, {
        status: response.status,
        headers,
      });
    }
    
    return response;
  } catch (e) {
    // For client-side routes or missing assets, serve index.html
    try {
      // Create a new request for index.html
      const indexRequest = new Request(`${url.origin}/index.html`, event.request);
      const response = await getAssetFromKV({
        request: indexRequest,
        waitUntil: event.waitUntil.bind(event),
      });

      // Get the HTML content
      let html = await response.text();
      
      // Replace all occurrences of %PUBLIC_URL% with empty string (root path)
      html = html.replace(/%PUBLIC_URL%/g, '');
      
      // Ensure proper caching for client-side routes
      const headers = new Headers(response.headers);
      headers.set('Cache-Control', 'no-store, no-cache, must-revalidate');
      
      // If we have a user email, add it to the response
      if (userEmail) {
        headers.set('Set-Cookie', `auth_user_email=${userEmail}; Path=/; Secure; SameSite=Strict`);
        headers.set('X-Auth-User-Email', userEmail);
      }
      
      return new Response(html, {
        status: 200,
        headers,
      });
    } catch (e) {
      return new Response(`"${url.pathname}" not found`, {
        status: 404,
        statusText: 'not found',
      });
    }
  }
}

addEventListener('fetch', (event) => {
  event.respondWith(handleEvent(event));
});