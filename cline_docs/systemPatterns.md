# System Patterns

## Architecture Patterns

### Multi-site Architecture
- Site Location Management
  - Multiple locations per festival (Arena/Campsite)
  - Site-specific data isolation
  - Context-based data filtering
  - Persistent site selection
  - Flexible site configuration

### Database Architecture
- Local-First Architecture
  - PouchDB for local storage
  - CouchDB for remote storage
  - Offline-first operations with background sync
  - Local operations prioritized for better UI responsiveness
  - Exponential backoff for sync retries
  - Site-specific data queries and filtering

### Authentication System
- Multi-layer Authentication
  - Cloudflare Access integration
  - Basic auth fallback
  - Environment-specific authentication handling
  - Secure header management

### State Management
- Centralized Festival State
  - FestivalContext as single source of truth
  - SiteLocationContext for site management
  - Synchronized with database isActive flag
  - Automatic state updates on festival changes
  - Consistent state across components
  - Site-aware data filtering
  - Real-time festival progress tracking with dynamic counter component
  - Responsive festival header layout adapting to sidebar state

### Permission Management Patterns
- Cascading Permission System
  - Hierarchical role structure (Admin > User > Partner > Public)
  - Role inheritance allowing higher roles to access lower-level pages
  - Database-backed access control with fallback to hardcoded rules
  - Context-aware permission checking throughout the application
  - Dynamic sidebar menu rendering based on user permissions
  - Comprehensive permission verification testing

### Data Flow Patterns
- Unidirectional Data Flow
  - Context providers for global state

## Modernization & Upgrade Patterns

- **Build System:** Rsbuild is used for all build and dev workflows, replacing Create React App.
- **Error-Driven Refactoring:** All dependency upgrades are accompanied by systematic, error-driven codebase refactoring to address breaking changes.
- **Strict Dependency Management:** Major dependencies (React, MUI, PouchDB, etc.) are kept up to date; migration guides and breaking change docs are consulted for each upgrade.
- **MUI 7+ Grid API:** All MUI Grid usage follows the new v7+ pattern: `<Grid size={{ xs: 12, ... }}>` (no `item` prop).
- **Type Safety:** Deprecated types (e.g., `GridValueGetterParams`) are removed promptly; valueGetter signatures are updated to match latest APIs.
- **Documentation-First:** All major changes are documented in the changelog and Memory Bank before release.