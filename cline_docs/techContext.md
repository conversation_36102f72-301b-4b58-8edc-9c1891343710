# Technical Context

## Technology Stack

### Frontend
- React 19+
- TypeScript
- Material-UI (MUI) v7+ components
- Tailwind CSS
- Progressive Web App (PWA)

### Database
- PouchDB 9 (local storage)
- CouchDB (remote storage)
- Real-time synchronization
- Offline-first capability

### Authentication
- Cloudflare Access
- Basic Authentication (fallback)
- Environment-specific auth handling

### Development Tools
- Node.js (v18+ recommended)
- npm 8+ (or yarn)
- Docker
- Nginx
- Visual Studio Code
- Rsbuild (modern build system)
- Webpack Bundle Analyzer (via Rsbuild config)

## Development Setup

### Environment Requirements
- Node.js 18+
- npm 8+
- Docker Desktop
- Git

### Configuration Files
- `rsbuild.config.ts` (build system)
- `.env.example` (environment variables)
- `Dockerfile`, `nginx.conf`, `supervisord.conf` (deployment)
- `package.json`, `tsconfig.json`, `.eslintrc.json` (project config)

## Technical Constraints & Notes
- All MUI Grid usage must use the new v7+ API: `<Grid size={{ xs: 12, ... }}>` (no `item` prop).
- Deprecated types (e.g., `GridValueGetterParams`) have been removed.
- All dependencies are kept up to date; breaking changes are addressed promptly.
- Data integrity between PouchDB (local) and CouchDB (remote) is critical.
- Rsbuild is used for all build and dev workflows.

## Recent Technical Additions
- Festival Counter Component (v1.4.0):
  - Real-time date calculations for festival progress tracking
  - Responsive design patterns for sidebar layout optimization
  - Dynamic state management for festival timing display
- Enhanced Permission System (v1.3.0):
  - Cascading permission logic with proper role hierarchy implementation
  - Comprehensive test coverage for permission verification
  - Context-based access control throughout the application
- Form Stability Improvements:
  - Uncontrolled input patterns to prevent infinite update loops
  - Dynamic key generation for component isolation
  - Enhanced error handling for legacy data compatibility