# Long-Press Functionality for Front of House Item Buttons

## Overview
This document outlines the implementation plan for adding long-press functionality to the Front of House buttons, which will allow users to specify a quantity when giving out items. This feature is primarily intended for iPad users.

## Current Implementation
The Front of House page currently:
- Displays a grid of buttons for different items (Suncream, Poncho, Water, etc.)
- Each button increments a counter by 1 when clicked
- Daily counts are stored in the database and displayed on the buttons
- The system uses PouchDB for local storage and syncs with CouchDB

## User Story
As a welfare staff member using an iPad, I want to be able to long-press on an item button to enter a specific quantity, so that I can quickly record multiple items given out at once rather than tapping the button multiple times.

## Implementation Plan

### 1. Add Long-Press Detection
Create a custom hook (`useLongPress`) that:
- Detects when a button is pressed and held
- Differentiates between normal clicks and long-presses
- Configurable threshold for long-press duration (e.g., 500ms)
- Handles both mouse and touch events for cross-device compatibility

### 2. Create a Number Input Dialog Component
Build a modal dialog that:
- Opens when a long-press is detected
- Contains a numeric input field optimized for touch
- Displays the item name being updated
- Provides confirm and cancel buttons
- Includes validation for numeric input

### 3. Modify the Database Service
Update the `addOrUpdateItemCount` method in `item-manager.ts` to:
- Accept a quantity parameter (with a default value of 1 for backward compatibility)
- Update the increment logic to add the specified quantity instead of always adding 1
- Ensure proper validation of input values

### 4. Update the Front of House Component
Modify the `FrontOfHousePage.tsx` to:
- Use the new long-press hook for item buttons
- Manage the number input dialog state
- Update handlers to work with quantity parameter
- Maintain existing functionality for single clicks

### 5. Add Touch-Friendly Styling
Since this feature is primarily for iPad use:
- Ensure the number input and buttons are large enough for touch input
- Add mobile-friendly styling to the modal
- Disable text selection during long-press interactions
- Optimize touch behavior with proper event handling

## Technical Implementation Details

### Custom Hook: `useLongPress`

```typescript
// src/hooks/useLongPress.ts
import { useCallback, useRef } from 'react';

interface LongPressOptions {
  onLongPress: (e: React.MouseEvent | React.TouchEvent) => void;
  onClick?: (e: React.MouseEvent | React.TouchEvent) => void;
  threshold?: number; // ms to wait before triggering long press
}

export const useLongPress = ({
  onLongPress,
  onClick,
  threshold = 500
}: LongPressOptions) => {
  const timerRef = useRef<number | null>(null);
  const isLongPress = useRef(false);

  const start = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    isLongPress.current = false;
    timerRef.current = window.setTimeout(() => {
      isLongPress.current = true;
      onLongPress(e);
    }, threshold);
  }, [onLongPress, threshold]);

  const clear = useCallback((e: React.MouseEvent | React.TouchEvent, shouldTriggerClick = true) => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    if (shouldTriggerClick && !isLongPress.current && onClick) {
      onClick(e);
    }
  }, [onClick]);

  return {
    onMouseDown: start,
    onMouseUp: clear,
    onMouseLeave: (e: React.MouseEvent) => clear(e, false),
    onTouchStart: start,
    onTouchEnd: clear
  };
};
```

### Number Input Dialog Component

```typescript
// src/components/shared/NumberInputDialog.tsx
import React, { useState } from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box
} from '@mui/material';

interface NumberInputDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (quantity: number) => void;
  itemName: string;
  label: string;
}

export const NumberInputDialog: React.FC<NumberInputDialogProps> = ({
  open,
  onClose,
  onConfirm,
  itemName,
  label
}) => {
  const [quantity, setQuantity] = useState<number>(1);
  const [error, setError] = useState<string | null>(null);

  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseInt(e.target.value, 10);
    if (isNaN(value) || value < 1) {
      setError('Please enter a valid number greater than 0');
    } else {
      setError(null);
      setQuantity(value);
    }
  };

  const handleConfirm = () => {
    if (!error && quantity > 0) {
      onConfirm(quantity);
      onClose();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      PaperProps={{
        sx: {
          width: '90%',
          maxWidth: '400px',
          borderRadius: 2
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        Add {label} Items
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Enter the number of {label} items to add:
          </Typography>
        </Box>
        <TextField
          autoFocus
          margin="dense"
          label="Quantity"
          type="number"
          fullWidth
          variant="outlined"
          value={quantity}
          onChange={handleQuantityChange}
          error={!!error}
          helperText={error}
          inputProps={{ 
            min: 1,
            sx: { 
              fontSize: '1.2rem',
              padding: '12px'
            } 
          }}
        />
      </DialogContent>
      <DialogActions sx={{ p: 2, pt: 1 }}>
        <Button 
          onClick={onClose} 
          color="primary"
          variant="outlined"
          sx={{ mr: 1 }}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleConfirm} 
          color="primary" 
          variant="contained"
          disabled={!!error || quantity < 1}
        >
          Add Items
        </Button>
      </DialogActions>
    </Dialog>
  );
};
```

### Modifications to Database Service

```typescript
// Updated method in item-manager.ts
async addOrUpdateItemCount(
  itemName: ItemName,
  festivalId: string,
  siteLocationId?: string,
  quantity: number = 1  // New parameter with default value of 1
): Promise<ItemDocument> {
  console.log(`Adding/updating count for ${itemName} (${quantity}) in festival ${festivalId}, location ${siteLocationId}`);
  const today = new Date().toISOString();
  let doc = await this.getTodayDocument(festivalId, siteLocationId);

  if (doc) {
    console.log('Found existing document:', doc);
    // Update existing document
    const currentCount = typeof doc[itemName] === 'number' ? Number(doc[itemName]) : 0;
    const updatedDoc: ItemDocument = {
      ...doc,
      [itemName]: Number(currentCount) + quantity,  // Add quantity instead of just 1
      timestamp: today,
      syncStatus: 'sync_pending'
    };
    
    try {
      const response = await this.db.put(updatedDoc);
      console.log('Updated document:', response);
      await this.syncManager.syncAfterChange();
      return {
        ...updatedDoc,
        _rev: response.rev
      };
    } catch (error) {
      console.error('Error updating document:', error);
      throw error;
    }
  } else {
    console.log('Creating new document for today');
    // Create new document for today
    const _id = `item_${uuidv4()}`;
    const newDoc: ItemDocument = {
      _id,
      documentType: 'item',
      type: 'item',
      festivalId,
      siteLocationId,
      timestamp: today,
      syncStatus: 'sync_pending',
      Suncream: 0,
      Poncho: 0,
      Water: 0,
      SanitaryProducts: 0,
      Earplugs: 0,
      Condoms: 0,
      ChildrensWristbands: 0,
      GeneralWristbands: 0,
      Charging: 0,
      Sanitizer: 0,
      ToiletRoll: 0,
      GeneralEnqs: 0,
      Other: ''
    };

    if (itemName === 'Other') {
      newDoc[itemName] = '';
    } else {
      newDoc[itemName] = quantity;  // Use quantity instead of 1
    }

    try {
      const response = await this.db.put(newDoc);
      console.log('Created new document:', response);
      await this.syncManager.syncAfterChange();
      return {
        ...newDoc,
        _rev: response.rev
      };
    } catch (error) {
      console.error('Error creating document:', error);
      throw error;
    }
  }
}
```

### Updates to Front of House Component

Key changes to `FrontOfHousePage.tsx`:

```typescript
// New imports
import { useLongPress } from '../hooks/useLongPress';
import { NumberInputDialog } from '../components/shared/NumberInputDialog';

// New state variables
const [dialogOpen, setDialogOpen] = useState(false);
const [selectedItem, setSelectedItem] = useState<{ name: ItemName, label: string } | null>(null);

// Updated debouncedCountChange function
const debouncedCountChange = useCallback(
  debounce(async (itemName: ItemName, quantity: number = 1) => {
    if (!activeFestival) return;

    try {
      console.log('Updating count for:', itemName, 'Festival ID:', activeFestival._id, 'Quantity:', quantity);
      const result = await databaseService.addOrUpdateItemCount(
        itemName, 
        activeFestival._id,
        activeSiteLocation?.id,
        quantity
      );
      console.log('Count updated successfully:', result);
      
      await loadCounts();
    } catch (error) {
      console.error('Error saving count:', error);
      setError('Failed to update count');
    } finally {
      setUpdatingItem(null);
    }
  }, 300),
  [activeFestival, activeSiteLocation, loadCounts]
);

// Updated handleCountChange function
const handleCountChange = useCallback((itemName: ItemName, quantity: number = 1) => {
  if (!activeFestival || updatingItem === itemName) {
    return;
  }

  setUpdatingItem(itemName);
  setError(null);
  debouncedCountChange(itemName, quantity);
}, [activeFestival, updatingItem, debouncedCountChange]);

// New handler for long press
const handleLongPress = useCallback((itemName: ItemName, label: string) => {
  setSelectedItem({ name: itemName, label });
  setDialogOpen(true);
}, []);

// New handler for dialog confirmation
const handleDialogConfirm = useCallback((quantity: number) => {
  if (selectedItem) {
    handleCountChange(selectedItem.name, quantity);
  }
}, [selectedItem, handleCountChange]);

// Updated renderButton function
const renderButton = useCallback((itemName: ItemName, IconComponent: React.ComponentType<any>, label: string) => {
  const longPressHandlers = useLongPress({
    onClick: () => handleCountChange(itemName),
    onLongPress: () => handleLongPress(itemName, label),
    threshold: 500
  });

  return (
    <Card
      {...longPressHandlers}
      sx={{
        width: 180,
        height: 180,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        opacity: updatingItem === itemName ? 0.5 : 1,
        transition: 'all 0.2s',
        '&:hover': {
          boxShadow: 3
        },
        userSelect: 'none', // Prevent text selection during long press
        touchAction: 'none' // Better touch behavior
      }}
    >
      {/* Existing button content */}
    </Card>
  );
}, [updatingItem, handleCountChange, getItemCount, handleLongPress]);

// Add dialog component to render
return (
  <Container maxWidth="lg" sx={{ py: 4 }}>
    {/* Existing content */}
    
    <NumberInputDialog
      open={dialogOpen}
      onClose={() => setDialogOpen(false)}
      onConfirm={handleDialogConfirm}
      itemName={selectedItem?.name || ''}
      label={selectedItem?.label || ''}
    />
  </Container>
);
```

## Testing Considerations

1. **iPad Testing**
   - Test on actual iPad devices
   - Verify touch event handling works properly
   - Test in different orientations

2. **Accessibility**
   - Ensure dialog meets accessibility standards
   - Verify screen readers can access the functionality

3. **Edge Cases**
   - Very large numbers (set reasonable limits)
   - Network disconnections during quantity updates
   - Concurrent updates from multiple devices

4. **User Experience**
   - Clear visual indication of long-press functionality
   - Appropriate feedback during input and updates
   - Smooth animations for dialog appearance

## Next Steps

1. Create the `useLongPress` custom hook
2. Implement the `NumberInputDialog` component
3. Update the database service to handle quantity
4. Modify the Front of House component
5. Test on iPad and other devices
6. Update documentation