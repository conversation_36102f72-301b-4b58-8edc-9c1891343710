# Product Context

## Purpose
The iThinc Welfare Management System is designed to streamline and enhance the management of welfare services at festivals and events. It provides a comprehensive solution for tracking admissions, managing shifts, monitoring inventory, and handling lost property, all while maintaining data integrity in both online and offline environments.

## Core Problems Solved

### Welfare Management
- Efficient tracking of patient admissions and care
- Real-time monitoring of bay/chair occupancy
- Comprehensive medical and incident recording
- Substance use and mental health tracking
- Safeguarding concern management
- Site-specific data management

### Resource Organization
- Structured shift management system
- Automated team rotation patterns
- Efficient inventory tracking
- Lost property management
- Staff allocation optimization
- Multi-site resource coordination

### Data Integrity
- Reliable offline-first operation
- Secure data synchronization
- Multi-device coordination
- Historical data preservation
- Automated backup systems
- Site-specific data isolation

### Access Control & Security
- Role-based access control with cascading permissions
- Hierarchical user roles (Admin > User > Partner > Public)
- Cloudflare Access integration for secure authentication
- Database-backed permission management
- Granular page-level access control
- Automatic role inheritance for appropriate access levels

## How It Works

### Festival Management
- Create and configure multiple festivals
- Set active/inactive status
- Track festival details and dates
- Manage festival-specific settings
- Monitor festival activities with real-time progress tracking
- Dynamic festival counter showing:
  - Days until festival starts
  - Current day of festival (day X of Y)
  - Days since festival ended
- Configure and manage multiple sites:
  - Arena, Campsite, etc.
- Enhanced sidebar layout with optimized festival information display

### Modernization & Maintenance
- The system is actively maintained with a modernization-first approach:
  - All major dependencies (React, MUI, PouchDB, etc.) are kept up to date.
  - Codebase is refactored promptly to address breaking changes and deprecated patterns.
  - Build system (Rsbuild) and architectural patterns are regularly reviewed for best practices.
  - Documentation and changelog are updated with every significant change.
- This ensures long-term maintainability, security, and a robust user experience.