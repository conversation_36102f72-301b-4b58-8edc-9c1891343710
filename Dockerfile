# Build stage
FROM node:18-alpine AS build

WORKDIR /app

# Copy package files first for better layer caching
COPY package*.json ./
COPY scripts/setup-symlinks.js ./scripts/

# Install dependencies
RUN npm ci && \
    npm install ajv@8.12.0

# Copy project files
COPY . .

# Create symlink directory structure and force create symlink
RUN mkdir -p node_modules/@app && \
    rm -f node_modules/@app/changelog.md && \
    ln -sf /app/CHANGELOG.md node_modules/@app/changelog.md

# Build the app
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built frontend assets
COPY --from=build /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/nginx.conf

# Labels for Unraid
LABEL \
    org.opencontainers.image.title="IThink Welfare" \
    org.opencontainers.image.description="IThink Welfare Management System - A comprehensive welfare management solution for festivals and events" \
    org.opencontainers.image.version="1.0.11"

# Health check using native curl from alpine
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:80/ || exit 1

# Expose port
EXPOSE 80
