#!/bin/bash

# Exit on any error
set -e

# Sync files to remote server
echo "Syncing files to remote server..."
rsync -ahp --progress --exclude='node_modules/.cache/' --delete /Users/<USER>/CascadeProjects/ithinc-welfare/ <EMAIL>:/mnt/user/Coding/ithink-welfare

# Git operations
echo "Enter commit message:"
read commit_message

if [ -z "$commit_message" ]; then
    echo "Commit message cannot be empty"
    exit 1
fi

# Add all files
echo "Adding files to git..."
git add .

# Commit with provided message
echo "Committing changes..."
git commit -m "$commit_message"

# Push to master
echo "Pushing to master..."
git push origin master

echo "Done! Files synced and changes pushed to git."
