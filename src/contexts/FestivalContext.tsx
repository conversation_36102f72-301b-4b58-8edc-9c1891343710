import React, { createContext, useContext, useState, useEffect, useCallback, useRef } from 'react';
import { databaseService } from '../services/database/index';
import { Festival } from '../types/festival';

interface FestivalContextType {
  festivals: Festival[];
  activeFestival: Festival | null;
  setActiveFestival: (festival: Festival | null) => Promise<void>;
  loading: boolean;
  error: Error | null;
  manualSync: () => Promise<void>;
}

const FestivalContext = createContext<FestivalContextType | undefined>(undefined);

export const FestivalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [festivals, setFestivals] = useState<Festival[]>([]);
  const [activeFestival, setActiveFestival] = useState<Festival | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  
  // Use refs to prevent unnecessary re-renders and race conditions
  const loadingRef = useRef(false);
  const initializationAttempts = useRef(0);
  const maxInitAttempts = 3;

  const loadFestivals = useCallback(async (force: boolean = false) => {
    // Prevent concurrent loads
    if (!force && loadingRef.current) {
      return;
    }

    try {
      loadingRef.current = true;
      setLoading(true);

      const fetchedFestivals = await databaseService.getFestivals();
      setFestivals(fetchedFestivals);

      // Try to get active festival from localStorage
      const storedFestivalId = localStorage.getItem('activeFestivalId');
      if (storedFestivalId) {
        const storedFestival = fetchedFestivals.find(f => f._id === storedFestivalId);
        if (storedFestival) {
          // Only update state if the festival ID is different or current is null
          if (activeFestival?._id !== storedFestival._id) {
            setActiveFestival(storedFestival);
          }
        } else {
          // If the stored festival ID doesn't exist anymore, clear it
          localStorage.removeItem('activeFestivalId');
          if (activeFestival !== null) { // Only update if not already null
             setActiveFestival(null);
          }
        }
      } else {
        // If no festival is stored in localStorage, use the first one as default
        if (fetchedFestivals.length > 0) {
          const defaultFestival = fetchedFestivals[0];
          // Only update state if the festival ID is different or current is null
          if (activeFestival?._id !== defaultFestival._id) {
            setActiveFestival(defaultFestival);
            localStorage.setItem('activeFestivalId', defaultFestival._id);
          }
        } else {
           if (activeFestival !== null) { // Only update if not already null
             setActiveFestival(null);
           }
        }
      }
      
      // Clear error state on successful load
      setError(null);
    } catch (err) {
      console.error('Error loading festivals:', err);
      setError(err instanceof Error ? err : new Error('Failed to load festivals'));
    } finally {
      setLoading(false);
      loadingRef.current = false;
    }
  }, []);

  useEffect(() => {
    // Simplified initialization for all devices
    const initializeDatabase = async () => {
      try {
        console.log('[DEBUG] Festival: Simplified database initialization started at:', new Date().toISOString());
        
        // Load festivals directly using simplified database service
        console.log('[DEBUG] Festival: Loading festivals with simplified service...');
        await loadFestivals(true);
        console.log('[DEBUG] Festival: Festivals loaded successfully at:', new Date().toISOString());

        // Start background sync without blocking UI
        console.log('[DEBUG] Festival: Starting background sync (non-blocking)...');
        databaseService.manualSync().then(() => {
          console.log('[DEBUG] Festival: Background sync completed at:', new Date().toISOString());
          loadFestivals(true);
        }).catch((syncErr) => {
          console.warn('[DEBUG] Festival: Background sync failed (non-critical):', syncErr);
        });

      } catch (err) {
        console.error('[DEBUG] Festival: Error loading festivals:', err);
        setError(err instanceof Error ? err : new Error('Failed to load festivals'));
        
        if (initializationAttempts.current < maxInitAttempts) {
          initializationAttempts.current++;
          const backoffTime = Math.min(1000 * Math.pow(2, initializationAttempts.current), 3000);
          console.log('[DEBUG] Festival: Retrying festival load in', backoffTime, 'ms, attempt:', initializationAttempts.current);
          setTimeout(initializeDatabase, backoffTime);
        } else {
          console.log('[DEBUG] Festival: Max attempts reached, unblocking UI at:', new Date().toISOString());
          setLoading(false);
        }
      }
    };

    initializeDatabase();
  }, [loadFestivals]);

  const handleSetActiveFestival = useCallback(async (festival: Festival | null) => {
    try {
      setLoading(true);

      // Trigger background sync when switching festivals
      if (activeFestival?._id !== festival?._id) {
        console.log('[DEBUG] FestivalContext: Festival changed, triggering background sync');
        databaseService.manualSync();
      }

      if (festival) {
        // Store the active festival ID in localStorage only
        setActiveFestival(festival);
        localStorage.setItem('activeFestivalId', festival._id);
      } else {
        setActiveFestival(null);
        localStorage.removeItem('activeFestivalId');
      }

      // No need to refresh festivals list since we're not changing the database
      setLoading(false);
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to update festival'));
      setLoading(false);
      throw err;
    }
  }, [activeFestival, setLoading, setActiveFestival, setError]);

  const manualSync = useCallback(async () => {
    try {
      setLoading(true);
      await databaseService.sync();
      await loadFestivals(true); // Ensure loadFestivals is included if it's from useCallback
    } catch (err) {
      setError(err instanceof Error ? err : new Error('Failed to sync'));
    } finally {
      setLoading(false);
    }
  }, [setLoading, loadFestivals, setError]); // Dependencies: setLoading, loadFestivals, setError

  const contextValue = React.useMemo(() => ({
    festivals,
    activeFestival,
    setActiveFestival: handleSetActiveFestival,
    loading,
    error,
    manualSync
  }), [festivals, activeFestival, handleSetActiveFestival, loading, error, manualSync]);

  return (
    <FestivalContext.Provider value={contextValue}>
      {children}
    </FestivalContext.Provider>
  );
};

export const useFestival = () => {
  const context = useContext(FestivalContext);
  if (context === undefined) {
    throw new Error('useFestival must be used within a FestivalProvider');
  }
  return context;
};
