import React, { createContext, useContext, useState, useCallback, useEffect } from 'react';
import { FestivalLocation } from '../types/festival';
import { useFestival } from './FestivalContext';

interface SiteLocationContextType {
  activeSiteLocation: FestivalLocation | null;
  setActiveSiteLocation: (location: FestivalLocation | null) => void;
}

const SiteLocationContext = createContext<SiteLocationContextType | undefined>(undefined);

export const useSiteLocation = () => {
  const context = useContext(SiteLocationContext);
  if (!context) {
    throw new Error('useSiteLocation must be used within a SiteLocationProvider');
  }
  return context;
};

interface SiteLocationProviderProps {
  children: React.ReactNode;
}

export const SiteLocationProvider: React.FC<SiteLocationProviderProps> = ({ children }) => {
  const { activeFestival, loading } = useFestival();
  const [activeSiteLocation, setActiveSiteLocation] = useState<FestivalLocation | null>(null);

  // Load saved location on mount and when festival changes
  useEffect(() => {
    // Don't try to restore location while festival is loading
    if (loading) {
      return;
    }

    // Clear location if no active festival
    if (!activeFestival || !activeFestival.locations) {
      setActiveSiteLocation(null);
      return;
    }

    const savedLocationId = localStorage.getItem('activeSiteLocationId');
    if (savedLocationId && Array.isArray(activeFestival.locations)) {
      const location = activeFestival.locations.find(loc => loc.id === savedLocationId);
      if (location) {
        setActiveSiteLocation(location);
      } else {
        // If saved location not found in current festival, clear it
        localStorage.removeItem('activeSiteLocationId');
        setActiveSiteLocation(null);
      }
    }
  }, [activeFestival, loading]);

  const handleSetActiveSiteLocation = useCallback((location: FestivalLocation | null) => {
    setActiveSiteLocation(location);
    // Store the selection in localStorage for persistence
    if (location) {
      localStorage.setItem('activeSiteLocationId', location.id);
    } else {
      localStorage.removeItem('activeSiteLocationId');
    }
  }, []);

  const contextValue = React.useMemo(() => ({
    activeSiteLocation,
    setActiveSiteLocation: handleSetActiveSiteLocation,
  }), [activeSiteLocation, handleSetActiveSiteLocation]);

  return (
    <SiteLocationContext.Provider value={contextValue}>
      {children}
    </SiteLocationContext.Provider>
  );
};