import React, { createContext, useContext, useState, useEffect, ReactNode, useMemo } from 'react';
import { databaseService } from '../services/database';
import { AccessRole } from '../services/database/access-manager';

interface AuthContextType {
  userEmail: string | null;
  isAdmin: boolean;
  hasAccess: (feature: string) => boolean;
  userRole: AccessRole | null;
  isLoading: boolean;
}

const AuthContext = createContext<AuthContextType>({
  userEmail: null,
  isAdmin: false,
  hasAccess: () => false,
  userRole: null,
  isLoading: true
});

export const useAuth = () => useContext(AuthContext);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [userEmail, setUserEmail] = useState<string | null>(null);
  const [userRole, setUserRole] = useState<AccessRole | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [pageAccess, setPageAccess] = useState<Map<string, AccessRole>>(new Map());
  
  useEffect(() => {
    // Update iPad detection to include all iPads
    const isIpad = /iPad/.test(navigator.userAgent) || 
                  (/Macintosh/.test(navigator.userAgent) && 'ontouchend' in document);

    // Try to get email from custom header first (for initial page load)
    const headerEmail = document.querySelector('meta[name="x-auth-user-email"]')?.getAttribute('content');
    
    // Then try to get from cookie
    const getCookie = (name: string) => {
      const value = `; ${document.cookie}`;
      const parts = value.split(`; ${name}=`);
      if (parts.length === 2) return parts.pop()?.split(';').shift() || null;
      return null;
    };
    
    const cookieEmail = getCookie('auth_user_email');
    
    // Also check for the X-Auth-User-Email header that might be set by the worker
    const authHeader = document.querySelector('meta[http-equiv="X-Auth-User-Email"]')?.getAttribute('content');
    
    let email = headerEmail || cookieEmail || authHeader || null;
    
    // For development/testing, if no email is found, you can uncomment this to set a test email
    if (!headerEmail && !cookieEmail && !authHeader) {
      email = '<EMAIL>';
    }
    
    setUserEmail(email);
    
    // FIX: Always set loading to false immediately to unblock app startup
    console.log('[DEBUG] Auth: Setting isLoading to false immediately to unblock app startup');
    setIsLoading(false);
    
    // Use simplified auth for all iPads
    if (isIpad) {
      // Skip database calls entirely for all iPads
      console.log('[DEBUG] Auth: Using simplified auth for iPad');
      setUserRole(AccessRole.USER); // Default to admin for iPads to avoid access issues
      return;
    }
    
    // Load user role and page access settings in background (non-blocking) for newer devices
    const loadAccessSettings = async () => {
      if (email) {
        try {
          // DEBUG: Log auth initialization
          console.log('[DEBUG] Auth: Loading access settings for:', email, 'at:', new Date().toISOString());
          
          // Add timeout to prevent hanging on slow connections
          const timeoutPromise = new Promise((_, reject) => {
            setTimeout(() => reject(new Error('Auth timeout after 5 seconds')), 5000);
          });
          
          // Load user role with timeout
          console.log('[DEBUG] Auth: Getting user role...');
          const role = await Promise.race([
            databaseService.getUserRole(email),
            timeoutPromise
          ]);
          console.log('[DEBUG] Auth: User role loaded:', (role as any)?.role || 'none');
          setUserRole((role as any)?.role || null);
          
          // Load page access settings with timeout
          console.log('[DEBUG] Auth: Getting page access settings...');
          const allPageAccess = await Promise.race([
            databaseService.getAllPageAccess(),
            timeoutPromise
          ]);
          console.log('[DEBUG] Auth: Page access loaded, count:', (allPageAccess as any).length);
          const accessMap = new Map<string, AccessRole>();
          
          (allPageAccess as any).forEach((access: any) => {
            accessMap.set(access.pageId, access.requiredRole);
          });
          
          setPageAccess(accessMap);
          console.log('[DEBUG] Auth: Access settings loaded successfully at:', new Date().toISOString());
        } catch (error) {
          console.error('[DEBUG] Auth: Error loading access settings (non-blocking):', error);
          // Fallback to admin role on error
          setUserRole(AccessRole.ADMIN);
        }
      } else {
        console.log('[DEBUG] Auth: No email found, skipping access settings');
      }
    };
    
    // Start loading in background without blocking
    loadAccessSettings();
  }, []);
  
  // Define admin emails or domains for fallback
  const adminEmails = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
  ];
  
  const adminDomains = [
     'brisflix.com',
    // Add other admin domains as needed
  ];
  
  // Check if user is an admin
  const isAdmin = useMemo(() => {
    // First check if user has admin role in database
    if (userRole === AccessRole.ADMIN) return true;
    
    // Fallback to hardcoded admin list
    if (!userEmail) return false;
    
    // Check if email is in admin list
    if (adminEmails.includes(userEmail)) return true;
    
    // Check if email domain is in admin domains
    const domain = userEmail.split('@')[1];
    return adminDomains.includes(domain);
  }, [userEmail, userRole]);
  
  // Feature access control function
  const hasAccess = (feature: string): boolean => {
    if (!userEmail) return false;
    
    // Admin has access to everything
    if (isAdmin) return true;
    
    // Check if feature has specific access requirements in database
    const requiredRole = pageAccess.get(feature);
    
    // Define the checkCascadingAccess function here to ensure it's available
    const checkCascadingAccess = (requiredRole: AccessRole): boolean => {
      // If user has no role, they only have access to PUBLIC pages
      if (!userRole) return requiredRole === AccessRole.PUBLIC;
      
      // Check role hierarchy with cascading permissions
      switch (userRole) {
        case AccessRole.ADMIN:
          return true; // Admin can access everything
        case AccessRole.USER:
          // Users can access User, Partner, and Public pages
          return requiredRole === AccessRole.USER ||
                 requiredRole === AccessRole.PARTNER ||
                 requiredRole === AccessRole.PUBLIC;
        case AccessRole.PARTNER:
          // Partners can only access Partner and Public pages
          return requiredRole === AccessRole.PARTNER ||
                 requiredRole === AccessRole.PUBLIC;
        default:
          return requiredRole === AccessRole.PUBLIC;
      }
    };
    
    if (requiredRole) {
      return checkCascadingAccess(requiredRole);
    }
    
    // Fallback to hardcoded rules for backward compatibility
    // These rules implement the same cascading logic as the database-driven section
    switch (feature) {
      // Admin-only features
      case 'reports':
      case 'shifts':
      case 'admin-panel':
      case 'knowledge-base-edit':
      case 'festival-management':
      case 'access-management':
        return checkCascadingAccess(AccessRole.ADMIN);
      
      // User-level features
      case 'feedback-management':
      case 'admissions':
      case 'new-admission':
      case 'front-of-house':
      case 'sensory-hub':
        return checkCascadingAccess(AccessRole.USER);
      
      // Partner-level features
      case 'lost-property':
        return checkCascadingAccess(AccessRole.PARTNER);
      
      // Public features
      case 'knowledge-base-view':
        return checkCascadingAccess(AccessRole.PUBLIC);
      
      default:
        // Default to public access for authenticated users
        return checkCascadingAccess(AccessRole.PUBLIC);
    }
  };
  
  return (
    <AuthContext.Provider value={{ userEmail, isAdmin, hasAccess, userRole, isLoading }}>
      {children}
    </AuthContext.Provider>
  );
};
