import { useState, useEffect, useCallback } from 'react';
import { WelfareAdmission, NewWelfareAdmission } from '../types/admission';
import { databaseService } from '../services/database';
import { useFestival } from '../contexts/FestivalContext';
import { useSiteLocation } from '../contexts/SiteLocationContext';

export const useAdmissions = () => {
  const [admissions, setAdmissions] = useState<WelfareAdmission[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const { activeFestival } = useFestival();
  const { activeSiteLocation } = useSiteLocation();

  const fetchAdmissions = useCallback(async () => {
    setLoading(true);
    try {
      const data = activeFestival
        ? await databaseService.getAdmissionsByFestival(activeFestival._id)
        : await databaseService.getAdmissionsByFestival('all');
      
      // Filter admissions based on active site location
      const filteredData = activeSiteLocation
        ? data.filter(admission => admission.siteLocationId === activeSiteLocation.id)
        : data;
      
      setAdmissions(filteredData);
      setError(null);
    } catch (err) {
      console.error('Error fetching admissions:', err);
      setError(err as Error);
    } finally {
      setLoading(false);
    }
  }, [activeFestival, activeSiteLocation]);

  useEffect(() => {
    fetchAdmissions();
  }, [fetchAdmissions]);

  const addAdmission = useCallback(async (admission: NewWelfareAdmission) => {
    try {
      await databaseService.addAdmission(admission);
      await fetchAdmissions();
    } catch (err) {
      console.error('Error adding admission:', err);
      throw err;
    }
  }, [fetchAdmissions]);

  const updateAdmission = useCallback(async (admission: WelfareAdmission) => {
    try {
      await databaseService.updateAdmission(admission);
      await fetchAdmissions();
    } catch (err) {
      console.error('Error updating admission:', err);
      throw err;
    }
  }, [fetchAdmissions]);

  const deleteAdmission = useCallback(async (id: string) => {
    try {
      await databaseService.deleteAdmission(id);
      await fetchAdmissions();
    } catch (err) {
      console.error('Error deleting admission:', err);
      throw err;
    }
  }, [fetchAdmissions]);

  return {
    admissions,
    loading,
    error,
    addAdmission,
    updateAdmission,
    deleteAdmission,
    refreshAdmissions: fetchAdmissions
  };
};
