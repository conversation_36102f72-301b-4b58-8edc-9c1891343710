import { useState, useEffect, useMemo } from 'react';
import { WelfareAdmission } from '../types/admission';
import { ItemDocument, LostPropertyItem } from '../types/item';
import { SensoryHubVisit } from '../types/sensory-hub';
import { databaseService } from '../services/database/index';
import { isSameDay, parseISO, startOfDay } from 'date-fns';
import { useSiteLocation } from '../contexts/SiteLocationContext';

export interface ReportData {
  admissions: WelfareAdmission[];
  itemCounts: ItemDocument[];
  lostPropertyItems: LostPropertyItem[];
  sensoryHubVisits: SensoryHubVisit[];
}

export const useReportData = (festivalId: string | undefined, selectedDay: string) => {
  const [data, setData] = useState<ReportData>({ admissions: [], itemCounts: [], lostPropertyItems: [], sensoryHubVisits: [] });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { activeSiteLocation } = useSiteLocation();

  useEffect(() => {
    const fetchData = async () => {
      if (!festivalId) {
        setLoading(false);
        return;
      }
      
      setLoading(true);
      try {
        const [allAdmissions, allItemCounts, allLostPropertyItems, allSensoryHubVisits] = await Promise.all([
          databaseService.getAdmissionsByFestival(festivalId),
          databaseService.getItemCountsByFestival(festivalId),
          databaseService.getLostPropertyItems(),
          databaseService.getSensoryHubVisitsByFestival(festivalId)
        ]);

        // Filter by festival and site location
        const filteredAdmissions = activeSiteLocation
          ? allAdmissions.filter(admission => admission.siteLocationId === activeSiteLocation.id)
          : allAdmissions;

        const filteredItemCounts = activeSiteLocation
          ? allItemCounts.filter(item => item.siteLocationId === activeSiteLocation.id)
          : allItemCounts;

        const filteredLostPropertyItems = allLostPropertyItems.filter(item => {
          const matchesFestival = item.festivalId === festivalId;
          if (!activeSiteLocation) return matchesFestival;
          return matchesFestival && item.siteLocationId === activeSiteLocation.id;
        });

        const filteredSensoryHubVisits = activeSiteLocation
          ? allSensoryHubVisits.filter(visit => visit.siteLocationId === activeSiteLocation.id)
          : allSensoryHubVisits;

        const newData = {
          admissions: filteredAdmissions || [],
          itemCounts: filteredItemCounts || [],
          lostPropertyItems: filteredLostPropertyItems || [],
          sensoryHubVisits: filteredSensoryHubVisits || []
        };

        setData(newData);
        setError(null);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError(err instanceof Error ? err.message : 'An error occurred while fetching data');
        // Set empty arrays when error occurs to prevent undefined data
        setData({ admissions: [], itemCounts: [], lostPropertyItems: [], sensoryHubVisits: [] });
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [festivalId, activeSiteLocation]);

  // Only filter data when not loading and data exists
  const filteredData = useMemo(() => {
    if (loading || !data) {
      return { admissions: [], itemCounts: [], lostPropertyItems: [], sensoryHubVisits: [] };
    }

    if (selectedDay === 'all') {
      return data;
    }

    return {
      admissions: data.admissions.filter((admission: WelfareAdmission) => 
        admission.Attended && isSameDay(parseISO(admission.Attended), startOfDay(parseISO(selectedDay)))
      ),
      itemCounts: data.itemCounts.filter((item: ItemDocument) => 
        item.timestamp && isSameDay(parseISO(item.timestamp), startOfDay(parseISO(selectedDay)))
      ),
      lostPropertyItems: data.lostPropertyItems.filter((item: LostPropertyItem) =>
        item.timeFound && isSameDay(parseISO(item.timeFound), startOfDay(parseISO(selectedDay)))
      ),
      sensoryHubVisits: data.sensoryHubVisits.filter((visit: SensoryHubVisit) =>
        visit.visitTimestamp && isSameDay(parseISO(visit.visitTimestamp), startOfDay(parseISO(selectedDay)))
      )
    };
  }, [data, selectedDay, loading]);

  return { data: filteredData, loading, error };
};
