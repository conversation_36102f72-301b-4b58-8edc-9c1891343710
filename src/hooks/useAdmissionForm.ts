import { useState, useCallback } from 'react';
import { SelectChangeEvent } from '@mui/material';
import { databaseService } from '../services/database';
import { useFestival } from '../contexts/FestivalContext';
import { 
  AdmissionFormData, 
  AdmissionFormHandlers 
} from '../types/forms';

interface UseAdmissionFormProps {
  initialData: AdmissionFormData;
  onSubmit?: (data: AdmissionFormData) => Promise<void>;
}

interface BayStatus {
  isOccupied: boolean;
  message: string;
}

export const useAdmissionForm = ({ initialData, onSubmit }: UseAdmissionFormProps): AdmissionFormHandlers & {
  formData: AdmissionFormData;
  bayStatus: BayStatus | null;
  isSubmitting: boolean;
} => {
  // Simple state with no dependencies or effects
  const [formData, setFormData] = useState<AdmissionFormData>(initialData);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [bayStatus, setBayStatus] = useState<BayStatus | null>(null);
  const { activeFestival } = useFestival();

  // Simple input handlers
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  }, []);

  const handleSelectChange = useCallback((e: SelectChangeEvent<unknown>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }));
  }, []);

  // Simple submit handler
  const handleSubmit = useCallback(async () => {
    if (!activeFestival) {
      throw new Error('No active festival selected');
    }

    setIsSubmitting(true);
    try {
      const timestamp = new Date().toISOString();
      const submissionData: AdmissionFormData = {
        ...formData,
        festivalId: activeFestival._id,
        timestamp,
        updatedAt: timestamp
      };

      if (onSubmit) {
        await onSubmit(submissionData);
      } else {
        if (formData._id) {
          await databaseService.updateAdmission(submissionData);
        } else {
          submissionData.createdAt = timestamp;
          await databaseService.addAdmission(submissionData);
        }
      }
    } catch (error) {
      console.error('Error submitting admission:', error);
      throw error;
    } finally {
      setIsSubmitting(false);
    }
  }, [formData, activeFestival, onSubmit]);

  // Simple reset function
  const resetForm = useCallback((data: AdmissionFormData) => {
    setFormData(data);
  }, []);

  return {
    formData,
    setFormData,
    isSubmitting,
    bayStatus,
    handleInputChange,
    handleSelectChange,
    handleSubmit,
    resetForm
  };
};