import { useState, useEffect } from 'react';
import { syncManager } from '../services/database';

interface SyncStatus {
  status: 'synced' | 'syncing' | 'error' | 'disconnected' | 'paused' | 'auth_error' | 'initial_sync';
  lastSync?: Date;
  pendingChanges: number;
  error?: string;
  authError?: boolean;
}

export const useSyncStatus = (): SyncStatus => {
  const [syncStatus, setSyncStatus] = useState<SyncStatus>({
    status: 'disconnected',
    pendingChanges: 0,
    authError: false
  });

  useEffect(() => {
    if (!syncManager) {
      // Wait for sync manager to be initialized
      const checkSyncManager = () => {
        if (syncManager) {
          const initialStatus = syncManager.getSyncStatus();
          setSyncStatus(initialStatus);

          const unsubscribe = syncManager.addSyncListener(() => {
            if (syncManager) {
              const currentStatus = syncManager.getSyncStatus();
              setSyncStatus(currentStatus);
            }
          });

          return unsubscribe;
        }
        return undefined;
      };

      // Check immediately and then periodically until sync manager is available
      const unsubscribe = checkSyncManager();
      if (unsubscribe) {
        return unsubscribe;
      }

      const interval = setInterval(() => {
        const unsubscribe = checkSyncManager();
        if (unsubscribe) {
          clearInterval(interval);
          return unsubscribe;
        }
      }, 100);

      return () => clearInterval(interval);
    }

    // Get initial status
    const initialStatus = syncManager.getSyncStatus();
    setSyncStatus(initialStatus);

    // Listen for status changes
    const unsubscribe = syncManager.addSyncListener(() => {
      if (syncManager) {
        const currentStatus = syncManager.getSyncStatus();
        setSyncStatus(currentStatus);
      }
    });

    return unsubscribe;
  }, []);

  return syncStatus;
};