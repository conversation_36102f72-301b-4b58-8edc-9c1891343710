import { useCallback, useRef } from 'react';

interface LongPressOptions {
  onLongPress: (e: React.MouseEvent | React.TouchEvent) => void;
  onClick?: (e: React.MouseEvent | React.TouchEvent) => void;
  threshold?: number; // ms to wait before triggering long press
}

/**
 * Custom hook to detect long press events on elements.
 * Handles both mouse and touch events.
 * 
 * @param options Configuration options for the long press behavior
 * @returns Event handlers to be spread onto the target element
 */
export const useLongPress = ({
  onLongPress,
  onClick,
  threshold = 500
}: LongPressOptions) => {
  const timerRef = useRef<number | null>(null);
  const isLongPress = useRef(false);

  const start = useCallback((e: React.MouseEvent | React.TouchEvent) => {
    isLongPress.current = false;
    
    // Prevent default behavior to avoid text selection and context menus
    if (e.target) {
      e.preventDefault();
    }
    
    timerRef.current = window.setTimeout(() => {
      isLongPress.current = true;
      onLongPress(e);
    }, threshold);
  }, [onLongPress, threshold]);

  const clear = useCallback((e: React.MouseEvent | React.TouchEvent, shouldTriggerClick = true) => {
    // Only prevent default for touch events to maintain accessibility
    if (e.type.includes('touch')) {
      e.preventDefault();
    }
    
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
    
    if (shouldTriggerClick && !isLongPress.current && onClick) {
      onClick(e);
    }
  }, [onClick]);

  // Cleanup function to handle component unmount
  const cancelTimer = useCallback(() => {
    if (timerRef.current) {
      clearTimeout(timerRef.current);
      timerRef.current = null;
    }
  }, []);

  // Use effect to clean up timer on unmount
  // We don't include this in the returned object as it would be called
  // directly by React through the useEffect hook in the component
  
  return {
    onMouseDown: start,
    onMouseUp: clear,
    onMouseLeave: (e: React.MouseEvent) => clear(e, false),
    onTouchStart: start,
    onTouchEnd: clear,
    onTouchCancel: (e: React.TouchEvent) => clear(e, false),
    cancelTimer
  };
};