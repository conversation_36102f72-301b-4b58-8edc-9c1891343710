import PouchDB from 'pouchdb-browser';
import PouchDBFind from 'pouchdb-find';

// Initialize PouchDB plugins
PouchDB.plugin(PouchDBFind);

// Database configuration types
interface CloudflareAccess {
  clientId: string;
  clientSecret: string;
}

interface DatabaseConfig {
  localName: string;
  remoteUrl: string;
  remoteName: string;
  cloudflare: CloudflareAccess;
  username?: string;  // Fallback basic auth
  password?: string;  // Fallback basic auth
}

// Default configuration
const defaultConfig: DatabaseConfig = {
  localName: 'ithinc_welfare',
  remoteUrl: 'https://couchdb.brisflix.com/', //'https://welfaredb.brisflix.com/ithinc_welfare', //'https://database-proxy.brisflix.workers.dev',
  remoteName: 'ithinc_welfare',
  cloudflare: {
    clientId: '',
    clientSecret: ''
  },
  username: 'ithinc',
  password: 'all the welfare cases'
};

// Current configuration
let currentConfig: DatabaseConfig = { ...defaultConfig };

// Load configuration from environment variables or secrets file
export const initializeConfig = async (): Promise<DatabaseConfig> => {
  // Try environment variables first
  if (process.env.ITHINC_DB_CF_CLIENT_ID && process.env.ITHINC_DB_CF_CLIENT_SECRET) {
    console.log('Loading configuration from environment variables');
    currentConfig = {
      localName: process.env.ITHINC_DB_LOCAL_NAME || defaultConfig.localName,
      remoteUrl: process.env.ITHINC_DB_REMOTE_URL || defaultConfig.remoteUrl,
      remoteName: process.env.ITHINC_DB_REMOTE_NAME || defaultConfig.remoteName,
      cloudflare: {
        clientId: process.env.ITHINC_DB_CF_CLIENT_ID,
        clientSecret: process.env.ITHINC_DB_CF_CLIENT_SECRET
      },
      username: process.env.ITHINC_DB_USERNAME || defaultConfig.username,
      password: process.env.ITHINC_DB_PASSWORD || defaultConfig.password
    };
    return currentConfig;
  }

  try {
    // If no environment variables, try secrets file as fallback
    const { secrets } = await import('../../config/secrets');
    
    // Check if secrets file has valid configuration
    if (secrets?.database?.cloudflare?.clientId && secrets?.database?.cloudflare?.clientSecret) {
      console.log('Loading configuration from secrets file');
      currentConfig = {
        localName: secrets.database.localName || defaultConfig.localName,
        remoteUrl: secrets.database.remoteUrl || defaultConfig.remoteUrl,
        remoteName: secrets.database.remoteName || defaultConfig.remoteName,
        cloudflare: {
          clientId: secrets.database.cloudflare.clientId,
          clientSecret: secrets.database.cloudflare.clientSecret
        },
        username: secrets.database.username || defaultConfig.username,
        password: secrets.database.password || defaultConfig.password
      };
      return currentConfig;
    }
  } catch (error) {
    // Log error but continue with defaults
    console.warn('Error loading secrets file:', error);
  }

  // If no valid configuration found, use defaults
  console.warn('No valid configuration found in environment variables or secrets file. Using default configuration.');
  return currentConfig;
};

// Get current configuration (synchronous)
export const getConfig = (): DatabaseConfig => currentConfig;

export const PRODUCTION_HOSTNAMES = [
  'welfare.brisflix.com',
  'ithink.brisflix.com',
  'www.ithink.brisflix.com',
  'ithink-welfare.brisflix.workers.dev',
  'localhost'
];

// PouchDB sync options - iPad-optimized for reliability
export const SYNC_OPTIONS = {
  live: false, // Disable continuous sync - only sync on demand
  retry: false, // Disable automatic retry
  ajax: {
    timeout: 3000, // PERFORMANCE: Reduced from 8s to 3s for faster loading
    withCredentials: true,
    cache: false, // Disable caching to avoid stale auth responses
    headers: {
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache, no-store, must-revalidate',
      'Pragma': 'no-cache'
    }
    // PERFORMANCE: Removed verbose logging to reduce overhead
  },
  batch_size: 75, // SYNC FIX: Increased from 10 to handle >25 admissions. Balances performance with server capacity.
  batches_limit: 5, // SYNC FIX: Increased from 1 to allow concurrent batches. Total capacity: 375 records per sync.
  websocket: false, // Disable WebSocket
  heartbeat: false, // Disable heartbeat
  auto_compaction: true, // SYNC FIX: Enable compaction to prevent database bloat and conflicts
  revs_limit: 100, // SYNC FIX: Increased from 3 to prevent revision conflicts that could cause admission loss
  attachments: false
};

// Export database name for consistency
export const DB_NAME = 'ithinc_welfare';

// Constants for sync management - PERFORMANCE OPTIMIZED
export const SYNC_CONSTANTS = {
  DEBOUNCE_DELAY: 500, // PERFORMANCE: Reduced from 2s to 500ms for faster response
  MAX_RETRY_ATTEMPTS: 2, // PERFORMANCE: Reduced from 5 to 2 attempts for faster failure
  RETRY_DELAY_BASE: 500, // PERFORMANCE: Reduced from 1s to 500ms base delay
  CONNECTION_TEST_INTERVAL: 30000, // PERFORMANCE: Reduced from 2min to 30s
  CHANGE_BATCH_TIMEOUT: 1000, // PERFORMANCE: Reduced from 5s to 1s for faster batching
  AUTH_RETRY_DELAY: 2000, // PERFORMANCE: Reduced from 5s to 2s for auth retries
  AUTH_MAX_BACKOFF: 30000, // PERFORMANCE: Reduced from 2min to 30s max backoff
  AUTH_SUPPRESSION_TIME: 60000, // PERFORMANCE: Reduced from 5min to 1min suppression
  DATABASE_RESET_TIMEOUT: 10000, // PERFORMANCE: Reduced from 30s to 10s timeout
  DATABASE_RESET_RETRY_ATTEMPTS: 2, // PERFORMANCE: Reduced from 3 to 2 retry attempts
};

// Database reset configuration
export const DATABASE_RESET_CONFIG = {
  CONFIRMATION_REQUIRED: true, // Require confirmation before database reset
  BACKUP_BEFORE_RESET: false, // Whether to create backup before reset (not implemented)
  RESET_TIMEOUT: 30000, // Timeout for reset operations
  SYNC_AFTER_RESET: true, // Whether to sync after reset
  PRESERVE_USER_SETTINGS: true, // Whether to preserve user settings during reset
};

// Log configuration in development only
if (process.env.NODE_ENV === 'development') {
  console.log('Database Configuration:', {
    remoteUrl: currentConfig.remoteUrl,
    remoteName: currentConfig.remoteName,
    syncOptions: {
      ...SYNC_OPTIONS,
      // Don't log sensitive headers
      ajax: {
        ...SYNC_OPTIONS.ajax,
        headers: Object.keys(SYNC_OPTIONS.ajax.headers)
      }
    }
  });
}
