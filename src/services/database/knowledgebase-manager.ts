import PouchDB from 'pouchdb-browser';
import { KnowledgeBaseItem } from '../../types';
import { SyncManager } from './sync-manager';
import { SyncStatus } from '../../types/base';
import { v4 as uuidv4 } from 'uuid';

interface FindResponse<T> {
  docs: Array<T & PouchDB.Core.IdMeta & PouchDB.Core.GetMeta>;
}

export class KnowledgeBaseManager {
  constructor(
    private db: PouchDB.Database,
    private syncManager: SyncManager
  ) {}

  async getKnowledgeBaseItems(festivalId: string): Promise<KnowledgeBaseItem[]> {
    if (!festivalId) {
      console.log('No festival ID provided');
      return [];
    }
    
    try {
      // Use allDocs to get all documents, then filter in memory
      const allDocs = await this.db.allDocs({
        include_docs: true
      });
      
      // Filter in memory to include:
      // 1. Items specifically for this festival
      // 2. Items marked to show for all festivals
      const knowledgeBaseItems = allDocs.rows
        .map(row => row.doc)
        .filter(doc =>
          doc &&
          (doc as any).documentType === 'knowledgeBase' &&
          (
            (doc as any).festivalId === festivalId ||
            (doc as any).showForAllFestivals === true
          )
        ) as KnowledgeBaseItem[];
      
      // Sort the results manually
      return knowledgeBaseItems.sort((a, b) => {
        // First sort by category
        if (a.category < b.category) return -1;
        if (a.category > b.category) return 1;
        
        // If categories are equal, sort by title
        if (a.title < b.title) return -1;
        if (a.title > b.title) return 1;
        
        return 0;
      });
    } catch (error) {
      console.error('Failed to get knowledge base items:', error);
      // Return an empty array instead of throwing an error
      return [];
    }
  }

  async getKnowledgeBaseCategories(festivalId: string): Promise<string[]> {
    try {
      const items = await this.getKnowledgeBaseItems(festivalId);
      const categories = new Set<string>();
      
      // If no items exist yet, return default categories
      if (items.length === 0) {
        return ['Substance Info', 'Mental Health', 'Support Contacts'];
      }
      
      items.forEach(item => {
        if (item.category) {
          categories.add(item.category);
        }
      });

      return Array.from(categories).sort();
    } catch (error) {
      console.error('Failed to get knowledge base categories:', error);
      // Return default categories instead of throwing an error
      return ['Substance Info', 'Mental Health', 'Support Contacts'];
    }
  }

  async getSubcategoriesForCategory(festivalId: string, category: string): Promise<string[]> {
    try {
      const items = await this.getKnowledgeBaseItems(festivalId);
      const subcategories = new Set<string>();
      
      items.forEach(item => {
        if (item.category === category && item.subcategory) {
          subcategories.add(item.subcategory);
        }
      });

      return Array.from(subcategories).sort();
    } catch (error) {
      console.error(`Failed to get subcategories for category ${category}:`, error);
      return [];
    }
  }

  async addKnowledgeBaseItem(item: Omit<KnowledgeBaseItem, '_id' | '_rev' | 'createdAt'>): Promise<KnowledgeBaseItem> {
    try {
      const _id = `knowledgebase_${uuidv4()}`;
      const timestamp = new Date().toISOString();

      const newItem: KnowledgeBaseItem = {
        ...item,
        _id,
        documentType: 'knowledgeBase',
        createdAt: timestamp,
        syncStatus: 'sync_pending' as SyncStatus
      };

      const response = await this.db.put(newItem);
      
      // Wait for sync to complete
      await this.syncManager.syncAfterChange(_id);
      
      return {
        ...newItem,
        _rev: response.rev
      };
    } catch (error) {
      console.error('Failed to add knowledge base item:', error);
      throw error;
    }
  }

  async updateKnowledgeBaseItem(item: KnowledgeBaseItem): Promise<KnowledgeBaseItem> {
    try {
      if (!item._id) {
        throw new Error('Cannot update knowledge base item without _id');
      }

      const existing = await this.db.get(item._id);
      const updatedItem: KnowledgeBaseItem = {
        ...item,
        _rev: existing._rev,
        updatedAt: new Date().toISOString(),
        syncStatus: 'sync_pending' as SyncStatus
      };

      const response = await this.db.put(updatedItem);
      
      // Wait for sync to complete
      await this.syncManager.syncAfterChange(item._id);
      
      return {
        ...updatedItem,
        _rev: response.rev
      };
    } catch (error) {
      console.error('Failed to update knowledge base item:', error);
      throw error;
    }
  }

  async deleteKnowledgeBaseItem(itemId: string): Promise<void> {
    try {
      const doc = await this.db.get(itemId);
      await this.db.remove(doc._id, doc._rev);
      
      // Wait for sync to complete
      await this.syncManager.syncAfterChange(itemId);
    } catch (error) {
      console.error('Failed to delete knowledge base item:', error);
      throw error;
    }
  }

  // Export method for database export service
  async exportAllKnowledgeBase(): Promise<KnowledgeBaseItem[]> {
    try {
      console.log('[EXPORT] Exporting all knowledge base items...');
      
      const result = await this.db.find({
        selector: {
          documentType: 'knowledge_base'
        }
      }) as FindResponse<KnowledgeBaseItem>;

      console.log(`[EXPORT] Exported ${result.docs.length} knowledge base items`);
      return result.docs;
    } catch (error) {
      console.error('[EXPORT] Failed to export knowledge base items:', error);
      throw error;
    }
  }
}