import PouchDB from 'pouchdb-browser';
import { v4 as uuidv4 } from 'uuid';
import { WelfareAdmission } from '../../types/admission';
import { AdmissionFormData } from '../../types/forms';
import { SyncManager } from './sync-manager';
import { SyncStatus } from '../../types/base';

interface AdmissionAllDocsResult extends PouchDB.Core.AllDocsResponse<WelfareAdmission> {
  rows: Array<{
    doc?: PouchDB.Core.ExistingDocument<WelfareAdmission & PouchDB.Core.AllDocsMeta>;
    id: string;
    key: string;
    value: {
      rev: string;
      deleted?: boolean;
    };
  }>;
}

type ExistingAdmission = Omit<WelfareAdmission, '_rev'> & PouchDB.Core.IdMeta & PouchDB.Core.GetMeta & {
  createdAt: string;
  timestamp: string;
};

interface FindResponse<T> {
  docs: Array<T & PouchDB.Core.IdMeta & PouchDB.Core.GetMeta>;
}

export class AdmissionManager {
  constructor(
    private db: PouchDB.Database,
    private syncManager: SyncManager
  ) {
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      // Check if the database has the createIndex method (PouchDB Find plugin)
      if (typeof this.db.createIndex === 'function') {
        // Create comprehensive index for admission queries
        await this.db.createIndex({
          index: {
            fields: ['documentType', 'festivalId', 'createdAt', 'isDeleted'],
            name: 'admission-festival-index'
          }
        });

        // Create index for location-based queries
        await this.db.createIndex({
          index: {
            fields: ['documentType', 'siteLocationId', 'createdAt', 'isDeleted'],
            name: 'admission-location-index'
          }
        });

        // Create index for status queries
        await this.db.createIndex({
          index: {
            fields: ['documentType', 'status', 'InBayNow', 'isDeleted'],
            name: 'admission-status-index'
          }
        });

        console.log('Admission indexes created successfully');
      }
    } catch (error) {
      console.warn('Failed to create admission indexes:', error);
    }
  }

  private isValidDate(dateString: string | undefined): boolean {
    if (!dateString) return false;
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  }

  async addAdmission(formData: AdmissionFormData): Promise<WelfareAdmission> {
    try {
      const _id = `admission_${uuidv4()}`;
      const timestamp = new Date().toISOString();

      // Log storage usage before attempting to save
      console.log('[ADMISSION] Attempting to save admission:', _id);
      
      // Check storage quota if available
      if ('storage' in navigator && 'estimate' in navigator.storage) {
        try {
          const estimate = await navigator.storage.estimate();
          const usedMB = Math.round((estimate.usage || 0) / 1024 / 1024);
          const quotaMB = Math.round((estimate.quota || 0) / 1024 / 1024);
          const usagePercent = quotaMB > 0 ? Math.round((usedMB / quotaMB) * 100) : 0;
          
          console.log(`[STORAGE] Usage: ${usedMB}MB / ${quotaMB}MB (${usagePercent}%)`);
          
          // Warn if storage is getting full
          if (usagePercent > 80) {
            console.warn(`[STORAGE] Storage usage is high: ${usagePercent}%`);
          }
        } catch (storageError) {
          console.warn('[STORAGE] Could not check storage quota:', storageError);
        }
      }

      const newAdmission: WelfareAdmission = {
        ...formData,
        _id,
        documentType: 'admission',
        type: 'admission',
        createdAt: timestamp,
        timestamp: timestamp,
        status: 'active',
        // Ensure arrays are initialized
        AdditionalNotes: formData.AdditionalNotes || [],
        History: formData.History || [],
        // Ensure required fields have values
        FirstName: formData.FirstName || '',
        Surname: formData.Surname || '',
        Gender: formData.Gender,
        Attended: formData.Attended || timestamp,
        BaysOrChairs: formData.BaysOrChairs,
        InBayNow: formData.InBayNow || false,
        ReferredBy: formData.ReferredBy || '',
        ReasonCategory: formData.ReasonCategory || '',
        SubstanceUsed: formData.SubstanceUsed || [],
        DOB: formData.DOB || '',
        Pronoun: formData.Pronoun || '',
        Ethnicity: formData.Ethnicity,
        ContactName: formData.ContactName || '',
        ContactNumber: formData.ContactNumber || '',
        DischargeTime: formData.DischargeTime || '',
        festivalId: formData.festivalId,
        syncStatus: 'sync_pending' as SyncStatus
      };

      const response = await this.db.put(newAdmission);
      console.log('[ADMISSION] Successfully saved admission to local database:', _id);
      
      // Check document count after save
      try {
        const countResult = await this.db.find({
          selector: {
            documentType: 'admission',
            $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
          },
          fields: ['_id', 'createdAt']
        });
        console.log(`[ADMISSION] Total admission count after save: ${countResult.docs.length}`);
        
        // Also check what the newly saved document looks like
        const newDoc = await this.db.get(_id);
        console.log(`[ADMISSION] Newly saved document structure:`, {
          _id: newDoc._id,
          documentType: (newDoc as any).documentType,
          createdAt: (newDoc as any).createdAt,
          isDeleted: (newDoc as any).isDeleted
        });
        
        // Check if our selector would find this document
        const testResult = await this.db.find({
          selector: {
            _id: _id,
            documentType: 'admission',
            $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
          }
        });
        console.log(`[ADMISSION] Can our selector find the new document? ${testResult.docs.length > 0 ? 'YES' : 'NO'}`);
        
      } catch (countError) {
        console.warn('[ADMISSION] Failed to count admissions after save:', countError);
      }
      
      // Queue sync but don't wait for it to complete to avoid blocking
      this.syncManager.syncAfterChange(_id).catch(syncError => {
        console.warn('[SYNC] Sync failed for admission:', _id, syncError);
        // Don't throw sync errors - admission is saved locally
      });
      
      return {
        ...newAdmission,
        _rev: response.rev
      };
    } catch (error: any) {
      console.error('Failed to add admission:', error);
      
      // Handle specific storage quota errors
      if (error?.name === 'QuotaExceededError' ||
          error?.message?.includes('quota') ||
          error?.message?.includes('storage')) {
        const friendlyError = new Error(
          'Storage quota exceeded. Please clear some data or contact support. The admission could not be saved.'
        );
        friendlyError.name = 'StorageQuotaError';
        throw friendlyError;
      }
      
      // Handle sync queue overflow
      if (error?.message?.includes('queue') || error?.message?.includes('batch')) {
        const friendlyError = new Error(
          'Sync queue is full. Please wait a moment and try again. Your previous admissions are still being processed.'
        );
        friendlyError.name = 'SyncQueueError';
        throw friendlyError;
      }
      
      throw error;
    }
  }

  async updateAdmission(formData: AdmissionFormData): Promise<WelfareAdmission> {
    if (!formData._id) {
      throw new Error('Cannot update admission without _id');
    }

    try {
      const existing = await this.db.get<ExistingAdmission>(formData._id);
      const timestamp = new Date().toISOString();
      
      const updated: WelfareAdmission = {
        ...formData,
        _id: formData._id,
        _rev: existing._rev,
        updatedAt: timestamp,
        // Ensure arrays are initialized
        AdditionalNotes: formData.AdditionalNotes || [],
        History: formData.History || [],
        // Keep original creation timestamp
        createdAt: existing.createdAt,
        timestamp: existing.timestamp,
        syncStatus: 'sync_pending' as SyncStatus
      };

      const response = await this.db.put(updated);
      
      // Wait for sync to complete
      await this.syncManager.syncAfterChange(formData._id);
      
      return {
        ...updated,
        _rev: response.rev
      };
    } catch (error) {
      console.error('Failed to update admission:', error);
      throw error;
    }
  }

  async getAdmissionsByFestival(festivalId: string, includeDeleted: boolean = false): Promise<WelfareAdmission[]> {
    try {
      console.log(`[ADMISSION] Fetching admissions for festival: ${festivalId}, includeDeleted: ${includeDeleted}`);
      
      // CRITICAL FIX: Use allDocs instead of find to bypass the 25-document index limit
      // The PouchDB Find index is corrupted and only returns 25 results, but allDocs finds all 165+ documents
      const allDocsResult = await this.db.allDocs({
        startkey: 'admission_',
        endkey: 'admission_\ufff0',
        include_docs: true
      });
      
      console.log(`[ADMISSION] AllDocs found ${allDocsResult.rows.length} admission documents (bypassing corrupted index)`);
      
      // Filter the results manually since we can't use the corrupted index
      const filteredDocs = allDocsResult.rows
        .filter(row => row.doc && !row.value.deleted)
        .map(row => row.doc as any)
        .filter(doc => {
          // Check if it's an admission document
          if (doc.documentType !== 'admission') return false;
          
          // Check festival ID (support 'all' to get all festivals)
          if (festivalId !== 'all' && doc.festivalId !== festivalId) return false;
          
          // Check deleted status
          if (!includeDeleted && doc.isDeleted === true) return false;
          
          return true;
        })
        .map(doc => {
          // Process the document
          if (!this.isValidDate(doc.createdAt)) {
            doc.createdAt = this.isValidDate(doc.timestamp) ? doc.timestamp : new Date().toISOString();
          }
          if (doc.DischargeTime && doc.status === 'active') {
            doc.status = 'discharged';
            doc.InBayNow = false;
          }
          return doc as WelfareAdmission;
        })
        .sort((a, b) => new Date(b.createdAt || b.timestamp || '').getTime() - new Date(a.createdAt || a.timestamp || '').getTime());

      console.log(`[ADMISSION] After filtering: ${filteredDocs.length} admissions for festival ${festivalId}`);
      return filteredDocs;
    } catch (error) {
      console.error('Failed to get admissions by festival:', error);
      throw error;
    }
  }

  async getAdmissionsByLocation(siteLocationId: string, includeDeleted: boolean = false): Promise<WelfareAdmission[]> {
    try {
      const result = await this.db.find({
        selector: {
          documentType: 'admission',
          siteLocationId: siteLocationId,
          ...(includeDeleted ? {} : { $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }] })
        }
      }) as FindResponse<WelfareAdmission>;

      return result.docs
        .map((doc) => {
          if (doc.documentType === 'admission' && doc.type === 'admission') {
            return doc as WelfareAdmission;
          }
          return null;
        })
        .filter((doc: WelfareAdmission | null): doc is WelfareAdmission => doc !== null);
    } catch (error) {
      console.error('Failed to get admissions by location:', error);
      throw error;
    }
  }

  async getAdmissionById(id: string): Promise<WelfareAdmission | null> {
    try {
      const doc = await this.db.get<ExistingAdmission>(id);
      
      if (doc && doc.documentType === 'admission' && doc.type === 'admission') {
        // Apply any necessary data fixes
        if (!this.isValidDate(doc.createdAt)) {
          doc.createdAt = this.isValidDate(doc.timestamp) ? doc.timestamp : new Date().toISOString();
        }
        if (doc.DischargeTime && doc.status === 'active') {
          doc.status = 'discharged';
          doc.InBayNow = false;
        }
        return doc as WelfareAdmission;
      }
      return null;
    } catch (error) {
      console.error('Failed to get admission by ID:', error);
      return null;
    }
  }

  async deleteAdmission(id: string): Promise<void> {
    try {
      const doc = await this.db.get<ExistingAdmission>(id);
      const timestamp = new Date().toISOString();
      
      const softDeletedDoc = {
        ...doc,
        isDeleted: true,
        deletedAt: timestamp,
        updatedAt: timestamp,
        syncStatus: 'sync_pending' as SyncStatus
      };
      
      await this.db.put(softDeletedDoc);
      
      // Wait for sync to complete
      await this.syncManager.syncAfterChange(id);
    } catch (error) {
      console.error('Failed to soft delete admission:', error);
      throw error;
    }
  }

  async bulkDeleteAdmissions(ids: string[]): Promise<void> {
    try {
      if (ids.length === 0) return;
      
      console.log(`[ADMISSION] Bulk deleting ${ids.length} admissions`);
      const timestamp = new Date().toISOString();
      const docs = [];
      
      // Get all documents to be deleted
      for (const id of ids) {
        try {
          const doc = await this.db.get<ExistingAdmission>(id);
          docs.push({
            ...doc,
            isDeleted: true,
            deletedAt: timestamp,
            updatedAt: timestamp,
            syncStatus: 'sync_pending' as SyncStatus
          });
        } catch (error) {
          console.warn(`[ADMISSION] Failed to get admission ${id} for bulk delete:`, error);
          // Continue with other documents
        }
      }
      
      // Bulk update all documents
      if (docs.length > 0) {
        await this.db.bulkDocs(docs);
        console.log(`[ADMISSION] Successfully bulk deleted ${docs.length} admissions`);
        
        // Queue sync for all changes
        this.syncManager.syncAfterChange().catch(syncError => {
          console.warn('[SYNC] Bulk sync failed for admissions:', syncError);
        });
      }
    } catch (error) {
      console.error('Failed to bulk delete admissions:', error);
      throw error;
    }
  }

  async cleanupOldAdmissions(cutoffTimestamp: string): Promise<number> {
    try {
      let cleanedCount = 0;
      
      // Calculate 6-month cutoff for hard deletion (only hard delete records that are already soft deleted and older than 6 months)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      const hardDeleteCutoff = sixMonthsAgo.toISOString();
      
      // Find old soft-deleted admissions that can be hard deleted (soft deleted and older than 6 months)
      const hardDeleteResult = await this.db.find({
        selector: {
          documentType: 'admission',
          isDeleted: true,
          deletedAt: { $lt: hardDeleteCutoff }
        }
      }) as FindResponse<WelfareAdmission>;

      // Hard delete old soft-deleted records
      for (const doc of hardDeleteResult.docs) {
        if (doc._id && doc._rev) {
          await this.db.remove(doc._id, doc._rev);
          cleanedCount++;
        }
      }

      // Find old discharged admissions that should be soft deleted (discharged and older than cutoff, not already deleted)
      const dischargedResult = await this.db.find({
        selector: {
          documentType: 'admission',
          status: 'discharged',
          createdAt: { $lt: cutoffTimestamp },
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        }
      }) as FindResponse<WelfareAdmission>;

      // Soft delete old discharged admissions
      const timestamp = new Date().toISOString();
      for (const doc of dischargedResult.docs) {
        if (doc._id && doc._rev) {
          const softDeletedDoc = {
            ...doc,
            isDeleted: true,
            deletedAt: timestamp,
            updatedAt: timestamp,
            syncStatus: 'sync_pending' as SyncStatus
          };
          await this.db.put(softDeletedDoc);
          cleanedCount++;
        }
      }

      // Single sync call after all operations
      if (cleanedCount > 0) {
        await this.syncManager.syncAfterChange();
      }

      console.log(`Cleaned up ${cleanedCount} old admission records (hard deleted: ${hardDeleteResult.docs.length}, soft deleted: ${dischargedResult.docs.length})`);
      return cleanedCount;
    } catch (error) {
      console.error('Failed to cleanup old admissions:', error);
      throw error;
    }
  }

  async fixInconsistentAdmissions(): Promise<void> {
    try {
      const result = await this.db.find({
        selector: {
          documentType: 'admission'
        }
      }) as FindResponse<WelfareAdmission>;

      for (const doc of result.docs) {
        let needsUpdate = false;
        const admission: AdmissionFormData = {
          ...doc,
          type: 'admission',
          documentType: 'admission',
          syncStatus: 'sync_pending' as SyncStatus
        };
        
        // Fix inconsistent discharge state
        if (doc.DischargeTime && doc.status === 'active') {
          admission.status = 'discharged';
          admission.InBayNow = false;
          needsUpdate = true;
        }
        
        // Fix inconsistent active state
        if (!doc.DischargeTime && doc.status === 'discharged') {
          admission.status = 'active';
          needsUpdate = true;
        }

        // Fix missing site location fields
        if (!doc.siteLocationId && doc.Location) {
          admission.siteLocationId = '';
          admission.siteLocationName = '';
          admission.siteLocationType = '';
          needsUpdate = true;
        }

        if (needsUpdate) {
          await this.updateAdmission(admission);
        }
      }
    } catch (error) {
      console.error('Failed to fix inconsistent admissions:', error);
      throw error;
    }
  }

  // Export method for database export service
  async exportAllAdmissions(includeDeleted: boolean = false): Promise<WelfareAdmission[]> {
    try {
      console.log('[EXPORT] Exporting all admissions...');
      
      const result = await this.db.find({
        selector: {
          documentType: 'admission',
          ...(includeDeleted ? {} : { $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }] })
        }
      }) as FindResponse<WelfareAdmission>;

      const processedDocs = result.docs
        .map((doc) => {
          if (doc.documentType === 'admission' && doc.type === 'admission') {
            // Apply data fixes for consistency
            if (!this.isValidDate(doc.createdAt)) {
              doc.createdAt = this.isValidDate(doc.timestamp) ? doc.timestamp : new Date().toISOString();
            }
            if (doc.DischargeTime && doc.status === 'active') {
              doc.status = 'discharged';
              doc.InBayNow = false;
            }
            return doc as WelfareAdmission;
          }
          return null;
        })
        .filter((doc: WelfareAdmission | null): doc is WelfareAdmission => doc !== null);
      
      console.log(`[EXPORT] Exported ${processedDocs.length} admission records (includeDeleted: ${includeDeleted})`);
      return processedDocs;
    } catch (error) {
      console.error('[EXPORT] Failed to export admissions:', error);
      throw error;
    }
  }
}
