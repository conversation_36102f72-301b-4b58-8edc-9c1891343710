import PouchDB from 'pouchdb-browser';
import { DB_NAME } from './config';
import { BaseDocument } from '../../types/base';

interface FindResponse<T> {
  docs: Array<T & PouchDB.Core.IdMeta & PouchDB.Core.GetMeta>;
}

// Define access roles
export enum AccessRole {
  ADMIN = 'admin',
  USER = 'user',
  PARTNER = 'partner',
  PUBLIC = 'public'
}

// Define access permission for a page
export interface PageAccess extends BaseDocument {
  type: 'page-access';
  documentType: 'page-access';
  pageId: string;
  pageName: string;
  requiredRole: AccessRole;
}

// Define user role assignment
export interface UserRole extends BaseDocument {
  type: 'user-role';
  documentType: 'user-role';
  email: string;
  role: AccessRole;
}

class AccessManager {
  private db: PouchDB.Database;

  constructor() {
    this.db = new PouchDB(DB_NAME);
  }

  // Page access methods
  async getAllPageAccess(): Promise<PageAccess[]> {
    try {
      const result = await this.db.find({
        selector: {
          type: 'page-access'
        }
      });
      return result.docs.map(doc => doc as unknown as PageAccess);
    } catch (error) {
      console.error('Error getting page access settings:', error);
      return [];
    }
  }

  async getPageAccess(pageId: string): Promise<PageAccess | null> {
    try {
      const result = await this.db.find({
        selector: {
          type: 'page-access',
          pageId
        }
      });
      
      if (result.docs.length > 0) {
        return result.docs[0] as unknown as PageAccess;
      }
      return null;
    } catch (error) {
      console.error(`Error getting page access for ${pageId}:`, error);
      return null;
    }
  }

  async setPageAccess(pageId: string, pageName: string, requiredRole: AccessRole): Promise<boolean> {
    try {
      // Check if access setting already exists
      const existing = await this.getPageAccess(pageId);
      
      if (existing) {
        // Update existing
        await this.db.put({
          ...existing,
          requiredRole,
          updatedAt: new Date().toISOString()
        });
      } else {
        // Create new
        await this.db.put({
          _id: `page-access-${pageId}`,
          type: 'page-access',
          documentType: 'page-access',
          syncStatus: 'sync_pending',
          pageId,
          pageName,
          requiredRole,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }
      return true;
    } catch (error) {
      console.error(`Error setting page access for ${pageId}:`, error);
      return false;
    }
  }

  // User role methods
  async getAllUserRoles(): Promise<UserRole[]> {
    try {
      const result = await this.db.find({
        selector: {
          type: 'user-role'
        }
      });
      return result.docs.map(doc => doc as unknown as UserRole);
    } catch (error) {
      console.error('Error getting user roles:', error);
      return [];
    }
  }

  async getUserRole(email: string): Promise<UserRole | null> {
    try {
      const result = await this.db.find({
        selector: {
          type: 'user-role',
          email
        }
      });
      
      if (result.docs.length > 0) {
        return result.docs[0] as unknown as UserRole;
      }
      return null;
    } catch (error) {
      console.error(`Error getting user role for ${email}:`, error);
      return null;
    }
  }

  async setUserRole(email: string, role: AccessRole): Promise<boolean> {
    try {
      // Check if role already exists
      const existing = await this.getUserRole(email);
      
      if (existing) {
        // Update existing
        await this.db.put({
          ...existing,
          role,
          updatedAt: new Date().toISOString()
        });
      } else {
        // Create new
        await this.db.put({
          _id: `user-role-${email}`,
          type: 'user-role',
          documentType: 'user-role',
          syncStatus: 'sync_pending',
          email,
          role,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });
      }
      return true;
    } catch (error) {
      console.error(`Error setting user role for ${email}:`, error);
      return false;
    }
  }

  async deleteUserRole(email: string): Promise<boolean> {
    try {
      const existing = await this.getUserRole(email);
      if (existing && existing._id && existing._rev) {
        await this.db.remove(existing._id, existing._rev);
        return true;
      }
      return false;
    } catch (error) {
      console.error(`Error deleting user role for ${email}:`, error);
      return false;
    }
  }

  // Export method for database export service
  async exportAllAccessControl(): Promise<{
    pageAccess: PageAccess[];
    userRoles: UserRole[];
  }> {
    try {
      console.log('[EXPORT] Exporting all access control data...');
      
      // Export page access configurations
      const pageAccessResult = await this.db.find({
        selector: {
          documentType: 'page-access'
        }
      }) as FindResponse<PageAccess>;
      
      const pageAccess = pageAccessResult.docs;

      // Export user role assignments
      const userRolesResult = await this.db.find({
        selector: {
          documentType: 'user-role'
        }
      }) as FindResponse<UserRole>;
      
      const userRoles = userRolesResult.docs;

      console.log(`[EXPORT] Exported ${pageAccess.length} page access configs, ${userRoles.length} user roles`);
      
      return {
        pageAccess,
        userRoles
      };
    } catch (error) {
      console.error('[EXPORT] Failed to export access control data:', error);
      throw error;
    }
  }
}

export const accessManager = new AccessManager();