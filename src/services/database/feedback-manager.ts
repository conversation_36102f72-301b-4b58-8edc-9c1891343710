import PouchDB from 'pouchdb-browser';
import { v4 as uuidv4 } from 'uuid';
import { Feedback, FeedbackInput, NewFeedback } from '../../types/feedback';
import { SyncManager } from './sync-manager';

interface FindRequest {
  selector: {
    [key: string]: any;
  };
  use_index?: string[];
  sort?: { [key: string]: 'asc' | 'desc' }[];
}

interface FindResponse<T> {
  docs: T[];
  warning?: string;
  execution_stats?: {
    total_keys_examined: number;
    total_docs_examined: number;
    total_quorum_docs_examined: number;
    results_returned: number;
    execution_time_ms: number;
  };
}

export class FeedbackManager {
  constructor(
    private db: PouchDB.Database,
    private syncManager: SyncManager
  ) {
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      // Check if the database has the createIndex method (PouchDB Find plugin)
      if (typeof this.db.createIndex === 'function') {
        // Create a single comprehensive index for feedback documents
        await this.db.createIndex({
          index: {
            fields: ['type', 'documentType', 'timestamp', 'resolved', 'isDeleted'],
            name: 'feedback_index'
          }
        });
        console.log('Feedback indexes created successfully');
      } else {
        console.warn('PouchDB Find plugin not available, skipping index creation');
      }
    } catch (error) {
      console.error('Error creating feedback indexes:', error);
    }
  }

  async addFeedback(input: FeedbackInput): Promise<Feedback> {
    const timestamp = new Date().toISOString();
    
    const feedback: NewFeedback = {
      _id: `feedback_${uuidv4()}`,
      type: 'feedback',
      documentType: 'feedback',
      syncStatus: 'sync_pending',
      name: input.name,
      page: input.page,
      feedback: input.feedback,
      timestamp,
      createdAt: timestamp,
      updatedAt: timestamp,
      resolved: false,
    };

    try {
      const response = await this.db.put(feedback);
      await this.syncManager.syncAfterChange();
      return {
        ...feedback,
        _rev: response.rev
      };
    } catch (error) {
      console.error('Error adding feedback:', error);
      throw error;
    }
  }

  async getFeedback(id: string): Promise<Feedback> {
    try {
      const doc = await this.db.get(id);
      return doc as Feedback;
    } catch (error) {
      console.error('Error getting feedback:', error);
      throw error;
    }
  }

  private isFeedbackDocument(doc: any): doc is Feedback {
    return (
      doc &&
      typeof doc.documentType === 'string' &&
      doc.documentType === 'feedback' &&
      typeof doc.type === 'string' &&
      doc.type === 'feedback'
    );
  }

  async getAllFeedback(includeDeleted: boolean = false): Promise<Feedback[]> {
    try {
      // First try using find with index
      const selector: any = {
        type: 'feedback',
        documentType: 'feedback'
      };

      // Filter out deleted records unless explicitly requested
      if (!includeDeleted) {
        selector.$or = [
          { isDeleted: { $exists: false } },
          { isDeleted: false }
        ];
      }

      const request: FindRequest = {
        selector,
        sort: [{ timestamp: 'desc' }],
        use_index: ['feedback_index']
      };

      const result = await (this.db as any).find(request) as FindResponse<Feedback>;
      return result.docs
        .filter(this.isFeedbackDocument)
        .sort((a, b) => {
          const dateA = new Date(a.timestamp || 0);
          const dateB = new Date(b.timestamp || 0);
          return dateB.getTime() - dateA.getTime();
        });
    } catch (error) {
      console.error('Error getting all feedback:', error);
      // Try fallback to allDocs if find fails
      try {
        console.log('Attempting fallback to allDocs');
        const result = await this.db.allDocs({
          include_docs: true,
          startkey: 'feedback_',
          endkey: 'feedback_\ufff0'
        });
        
        const feedbackDocs: Feedback[] = [];
        
        for (const row of result.rows) {
          if (row.doc && this.isFeedbackDocument(row.doc)) {
            // Filter out deleted records unless explicitly requested
            if (includeDeleted || !row.doc.isDeleted) {
              feedbackDocs.push(row.doc);
            }
          }
        }
        
        return feedbackDocs.sort((a, b) => {
          const dateA = new Date(a.timestamp || 0);
          const dateB = new Date(b.timestamp || 0);
          return dateB.getTime() - dateA.getTime();
        });
      } catch (fallbackError) {
        console.error('Fallback error:', fallbackError);
        throw error; // Throw original error if fallback fails
      }
    }
  }

  async getUnresolvedFeedback(includeDeleted: boolean = false): Promise<Feedback[]> {
    try {
      const selector: any = {
        type: 'feedback',
        documentType: 'feedback',
        resolved: false
      };

      // Filter out deleted records unless explicitly requested
      if (!includeDeleted) {
        selector.$and = [
          { $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }] }
        ];
      }

      const request: FindRequest = {
        selector,
        sort: [{ timestamp: 'desc' }],
        use_index: ['feedback_index']
      };

      const result = await (this.db as any).find(request) as FindResponse<Feedback>;
      return result.docs
        .filter(this.isFeedbackDocument)
        .sort((a, b) => {
          const dateA = new Date(a.timestamp || 0);
          const dateB = new Date(b.timestamp || 0);
          return dateB.getTime() - dateA.getTime();
        });
    } catch (error) {
      console.error('Error getting unresolved feedback:', error);
      throw error;
    }
  }

  async updateFeedbackStatus(id: string, resolved: boolean): Promise<Feedback> {
    try {
      const feedback = await this.getFeedback(id);
      const updatedFeedback: Feedback = {
        ...feedback,
        resolved,
        syncStatus: 'sync_pending',
        updatedAt: new Date().toISOString()
      };

      const response = await this.db.put(updatedFeedback);
      await this.syncManager.syncAfterChange();
      return {
        ...updatedFeedback,
        _rev: response.rev
      };
    } catch (error) {
      console.error('Error updating feedback status:', error);
      throw error;
    }
  }

  async deleteFeedback(id: string): Promise<void> {
    try {
      const doc = await this.db.get(id) as Feedback;
      const timestamp = new Date().toISOString();
      
      const softDeletedDoc = {
        ...doc,
        isDeleted: true,
        deletedAt: timestamp,
        updatedAt: timestamp,
        syncStatus: 'sync_pending'
      };
      
      await this.db.put(softDeletedDoc);
      await this.syncManager.syncAfterChange();
    } catch (error) {
      console.error('Error deleting feedback:', error);
      throw error;
    }
  }

  async cleanupOldFeedback(cutoffTimestamp: string): Promise<number> {
    try {
      let cleanedCount = 0;
      
      // Calculate 6-month cutoff for hard deletion (only hard delete records that are already soft deleted and older than 6 months)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      const hardDeleteCutoff = sixMonthsAgo.toISOString();
      
      // Find old soft-deleted feedback that can be hard deleted (soft deleted and older than 6 months)
      const hardDeleteRequest: FindRequest = {
        selector: {
          type: 'feedback',
          documentType: 'feedback',
          isDeleted: true,
          deletedAt: { $lt: hardDeleteCutoff }
        },
        use_index: ['feedback_index']
      };

      const hardDeleteResult = await (this.db as any).find(hardDeleteRequest) as FindResponse<Feedback>;
      
      // Hard delete old soft-deleted records
      for (const doc of hardDeleteResult.docs) {
        if (doc._id && doc._rev) {
          await this.db.remove(doc._id, doc._rev);
          cleanedCount++;
        }
      }

      // Find old feedback that should be soft deleted (older than cutoff, not already deleted)
      const oldSoftDeleteRequest: FindRequest = {
        selector: {
          type: 'feedback',
          documentType: 'feedback',
          timestamp: { $lt: cutoffTimestamp },
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        },
        use_index: ['feedback_index']
      };

      // Find old resolved feedback that should be soft deleted (resolved and older than cutoff, not already deleted)
      const resolvedSoftDeleteRequest: FindRequest = {
        selector: {
          type: 'feedback',
          documentType: 'feedback',
          resolved: true,
          timestamp: { $lt: cutoffTimestamp },
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        },
        use_index: ['feedback_index']
      };

      const [oldSoftDeleteResult, resolvedSoftDeleteResult] = await Promise.all([
        (this.db as any).find(oldSoftDeleteRequest) as Promise<FindResponse<Feedback>>,
        (this.db as any).find(resolvedSoftDeleteRequest) as Promise<FindResponse<Feedback>>
      ]);

      // Combine and deduplicate results for soft deletion
      const allDocsToSoftDelete = new Map<string, Feedback>();
      
      [...oldSoftDeleteResult.docs, ...resolvedSoftDeleteResult.docs]
        .filter(this.isFeedbackDocument)
        .forEach(doc => {
          allDocsToSoftDelete.set(doc._id, doc);
        });

      // Soft delete documents
      const timestamp = new Date().toISOString();
      const docsToSoftDelete = Array.from(allDocsToSoftDelete.values());
      for (const doc of docsToSoftDelete) {
        if (doc._id && doc._rev) {
          const softDeletedDoc = {
            ...doc,
            isDeleted: true,
            deletedAt: timestamp,
            updatedAt: timestamp,
            syncStatus: 'sync_pending'
          };
          await this.db.put(softDeletedDoc);
          cleanedCount++;
        }
      }

      // Single sync call after all operations
      if (cleanedCount > 0) {
        await this.syncManager.syncAfterChange();
      }

      console.log(`Cleaned up ${cleanedCount} old feedback records (hard deleted: ${hardDeleteResult.docs.length}, soft deleted: ${docsToSoftDelete.length})`);
      return cleanedCount;
    } catch (error) {
      console.error('Error cleaning up old feedback:', error);
      throw error;
    }
  }

  // Export method for database export service
  async exportAllFeedback(includeDeleted: boolean = false): Promise<Feedback[]> {
    try {
      console.log('[EXPORT] Exporting all feedback...');
      
      const selector: any = {
        type: 'feedback',
        documentType: 'feedback'
      };

      // Filter out deleted records unless explicitly requested
      if (!includeDeleted) {
        selector.$or = [
          { isDeleted: { $exists: false } },
          { isDeleted: false }
        ];
      }

      const request: FindRequest = {
        selector,
        sort: [{ timestamp: 'desc' }],
        use_index: ['feedback_index']
      };

      const result = await (this.db as any).find(request) as FindResponse<Feedback>;
      
      const feedback = result.docs
        .filter(this.isFeedbackDocument)
        .sort((a, b) => {
          const dateA = new Date(a.timestamp || 0);
          const dateB = new Date(b.timestamp || 0);
          return dateB.getTime() - dateA.getTime();
        });
      
      console.log(`[EXPORT] Exported ${feedback.length} feedback records (includeDeleted: ${includeDeleted})`);
      return feedback;
    } catch (error) {
      console.error('[EXPORT] Failed to export feedback:', error);
      throw error;
    }
  }
}