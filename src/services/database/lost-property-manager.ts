import PouchDB from 'pouchdb-browser';
import { v4 as uuidv4 } from 'uuid';
import { LostPropertyItem } from '../../types/item';
import { SyncManager } from './sync-manager';

interface FindRequest {
  selector: {
    [key: string]: any;
  };
  use_index?: string[];
}

interface FindResponse<T> {
  docs: T[];
  warning?: string;
}

export class LostPropertyManager {
  constructor(
    private db: PouchDB.Database,
    private syncManager: SyncManager
  ) {}

  async addLostPropertyItem(item: Omit<LostPropertyItem, '_id' | '_rev'>): Promise<LostPropertyItem> {
    const _id = `lost_property_${uuidv4()}`;
    const timestamp = new Date().toISOString();

    const newItem = {
      ...item,
      _id,
      timestamp,
      documentType: 'lost_property' as const,
      type: 'lost_property' as const,
      status: item.status || 'unclaimed'
    } as LostPropertyItem;

    const response = await this.db.put(newItem);
    await this.syncManager.syncAfterChange();

    return {
      ...newItem,
      _rev: response.rev
    };
  }

  async getLostPropertyItems(festivalId?: string, includeDeleted: boolean = false): Promise<LostPropertyItem[]> {
    if (festivalId) {
      // Use database-level filtering when festivalId is provided
      try {
        const selector: any = {
          documentType: 'lost_property',
          type: 'lost_property',
          festivalId: festivalId
        };

        // Filter out deleted records unless explicitly requested
        if (!includeDeleted) {
          selector.$or = [
            { isDeleted: { $exists: false } },
            { isDeleted: false }
          ];
        }

        const request: FindRequest = {
          selector
        };

        const result = await (this.db as any).find(request) as FindResponse<LostPropertyItem>;

        return result.docs.filter((doc): doc is LostPropertyItem =>
          doc.documentType === 'lost_property' && doc.type === 'lost_property'
        );
      } catch (error) {
        console.error('Error getting lost property items by festival:', error);
        throw error;
      }
    } else {
      // Fallback to original behavior when no festivalId provided (backward compatibility)
      const result = await this.db.allDocs<LostPropertyItem & PouchDB.Core.AllDocsMeta>({
        include_docs: true,
        startkey: 'lost_property_',
        endkey: 'lost_property_\ufff0'
      });

      const filteredDocs = result.rows
        .filter(row => row.doc !== undefined)
        .map(row => {
          const doc = row.doc!;
          if (doc.documentType === 'lost_property' && doc.type === 'lost_property') {
            return doc as LostPropertyItem;
          }
          return null;
        })
        .filter((doc): doc is LostPropertyItem => doc !== null);

      // Filter out deleted records unless explicitly requested
      if (!includeDeleted) {
        return filteredDocs.filter(doc => !doc.isDeleted);
      }

      return filteredDocs;
    }
  }

  async updateLostPropertyItem(item: LostPropertyItem): Promise<LostPropertyItem> {
    const existing = await this.db.get(item._id);
    const updated: LostPropertyItem = {
      ...existing,
      ...item,
      _rev: existing._rev
    };

    const response = await this.db.put(updated);
    await this.syncManager.syncAfterChange();

    return {
      ...updated,
      _rev: response.rev
    };
  }

  async deleteLostPropertyItem(id: string): Promise<void> {
    const doc = await this.db.get(id) as LostPropertyItem;
    const timestamp = new Date().toISOString();
    
    const softDeletedDoc = {
      ...doc,
      isDeleted: true,
      deletedAt: timestamp,
      updatedAt: timestamp,
      syncStatus: 'sync_pending'
    };
    
    await this.db.put(softDeletedDoc);
    await this.syncManager.syncAfterChange();
  }

  async bulkDeleteLostProperty(ids: string[]): Promise<void> {
    if (ids.length === 0) return;

    try {
      console.log(`Starting bulk delete of ${ids.length} lost property items`);
      
      // Retrieve all documents first
      const docsToDelete = [];
      const timestamp = new Date().toISOString();
      
      for (const id of ids) {
        try {
          const doc = await this.db.get(id);
          docsToDelete.push({
            ...doc,
            isDeleted: true,
            deletedAt: timestamp,
            updatedAt: timestamp,
            syncStatus: 'sync_pending'
          });
        } catch (error) {
          console.warn(`Failed to retrieve lost property item ${id} for deletion:`, error);
          // Continue with other documents even if one fails
        }
      }

      if (docsToDelete.length === 0) {
        console.warn('No valid lost property items found for bulk deletion');
        return;
      }

      // Perform bulk update using bulkDocs
      const result = await this.db.bulkDocs(docsToDelete);
      
      // Check for any errors in the bulk operation
      const errors = result.filter(r => 'error' in r);
      if (errors.length > 0) {
        console.error('Some lost property items failed to delete:', errors);
      }

      const successCount = result.filter(r => 'ok' in r && r.ok).length;
      console.log(`Successfully bulk deleted ${successCount} of ${docsToDelete.length} lost property items`);
      
      await this.syncManager.syncAfterChange();
    } catch (error) {
      console.error('Error in bulk delete lost property:', error);
      throw error;
    }
  }

  async cleanupOldLostProperty(cutoffTimestamp: string): Promise<number> {
    try {
      let cleanedCount = 0;
      
      // Calculate 6-month cutoff for hard deletion (only hard delete records that are already soft deleted and older than 6 months)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      const hardDeleteCutoff = sixMonthsAgo.toISOString();
      
      // Find old soft-deleted lost property items that can be hard deleted (soft deleted and older than 6 months)
      const hardDeleteRequest: FindRequest = {
        selector: {
          documentType: 'lost_property',
          type: 'lost_property',
          isDeleted: true,
          deletedAt: { $lt: hardDeleteCutoff }
        }
      };

      const hardDeleteResult = await (this.db as any).find(hardDeleteRequest) as FindResponse<LostPropertyItem>;
      
      // Hard delete old soft-deleted records
      for (const doc of hardDeleteResult.docs) {
        if (doc._id && doc._rev) {
          await this.db.remove(doc._id, doc._rev);
          cleanedCount++;
        }
      }

      // Find old lost property items that should be soft deleted (older than cutoff, not already deleted)
      const oldSoftDeleteRequest: FindRequest = {
        selector: {
          documentType: 'lost_property',
          type: 'lost_property',
          timestamp: { $lt: cutoffTimestamp },
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        }
      };

      // Find old claimed lost property items that should be soft deleted (claimed and older than cutoff, not already deleted)
      const claimedSoftDeleteRequest: FindRequest = {
        selector: {
          documentType: 'lost_property',
          type: 'lost_property',
          status: 'claimed',
          timestamp: { $lt: cutoffTimestamp },
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        }
      };

      const [oldSoftDeleteResult, claimedSoftDeleteResult] = await Promise.all([
        (this.db as any).find(oldSoftDeleteRequest) as Promise<FindResponse<LostPropertyItem>>,
        (this.db as any).find(claimedSoftDeleteRequest) as Promise<FindResponse<LostPropertyItem>>
      ]);

      // Combine and deduplicate results for soft deletion
      const allDocsToSoftDelete = new Map<string, LostPropertyItem>();
      
      [...oldSoftDeleteResult.docs, ...claimedSoftDeleteResult.docs]
        .filter((doc): doc is LostPropertyItem =>
          doc.documentType === 'lost_property' && doc.type === 'lost_property'
        )
        .forEach(doc => {
          allDocsToSoftDelete.set(doc._id, doc);
        });

      // Soft delete documents
      const timestamp = new Date().toISOString();
      const docsToSoftDelete = Array.from(allDocsToSoftDelete.values());
      for (const doc of docsToSoftDelete) {
        if (doc._id && doc._rev) {
          const softDeletedDoc = {
            ...doc,
            isDeleted: true,
            deletedAt: timestamp,
            updatedAt: timestamp,
            syncStatus: 'sync_pending'
          };
          await this.db.put(softDeletedDoc);
          cleanedCount++;
        }
      }

      // Single sync call after all operations
      if (cleanedCount > 0) {
        await this.syncManager.syncAfterChange();
      }

      console.log(`Cleaned up ${cleanedCount} old lost property records (hard deleted: ${hardDeleteResult.docs.length}, soft deleted: ${docsToSoftDelete.length})`);
      return cleanedCount;
    } catch (error) {
      console.error('Error cleaning up old lost property:', error);
      throw error;
    }
  }

  // Export method for database export service
  async exportAllLostProperty(includeDeleted: boolean = false): Promise<LostPropertyItem[]> {
    try {
      console.log('[EXPORT] Exporting all lost property...');
      
      const selector: any = {
        documentType: 'lost_property',
        type: 'lost_property'
      };

      // Filter out deleted records unless explicitly requested
      if (!includeDeleted) {
        selector.$or = [
          { isDeleted: { $exists: false } },
          { isDeleted: false }
        ];
      }

      const request: FindRequest = {
        selector
      };

      const result = await (this.db as any).find(request) as FindResponse<LostPropertyItem>;
      
      const items = result.docs.filter((doc): doc is LostPropertyItem =>
        doc.documentType === 'lost_property' && doc.type === 'lost_property'
      );
      
      console.log(`[EXPORT] Exported ${items.length} lost property records (includeDeleted: ${includeDeleted})`);
      return items;
    } catch (error) {
      console.error('[EXPORT] Failed to export lost property:', error);
      throw error;
    }
  }
}
