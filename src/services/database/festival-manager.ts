import PouchDB from 'pouchdb-browser';
import { v4 as uuidv4 } from 'uuid';
import { Festival, NewFestival, FestivalNotes } from '../../types/festival';
import { SyncManager } from './sync-manager';

export class FestivalManager {
  constructor(
    private db: PouchDB.Database,
    private syncManager: SyncManager
  ) {}

  async getFestivals(includeDeleted: boolean = false): Promise<Festival[]> {
    try {
      console.log('[DEBUG] FestivalManager: getFestivals called with includeDeleted:', includeDeleted);
      
      // Check if the database has the createIndex method (PouchDB Find plugin)
      if (typeof this.db.createIndex === 'function') {
        console.log('[DEBUG] FestivalManager: PouchDB Find plugin available, using find() method');
        
        // Create index for documentType if it doesn't exist
        await this.db.createIndex({
          index: {
            fields: ['documentType', 'isDeleted'],
            name: 'festival_documentType_index'
          }
        });

        const selector: any = {
          documentType: 'festival',
          $or: [
            { type: 'festival' },
            { type: 'regular_event' }
          ]
        };

        if (!includeDeleted) {
          selector.$and = [
            { $or: [{ type: 'festival' }, { type: 'regular_event' }] },
            { $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }] }
          ];
          delete selector.$or; // Remove the original $or since we're using $and now
        }

        console.log('[DEBUG] FestivalManager: Using selector:', JSON.stringify(selector, null, 2));

        const result = await this.db.find({
          selector,
          fields: [
            '_id', 'name', 'startDate', 'endDate', 'isActive',
            'documentType', 'type', 'locations', 'hasMultipleLocations',
            'showAdmissions', 'showFrontOfHouse', 'showLostProperty', 'showShifts' // Added fields for sidebar
          ],
          sort: [{ _id: 'asc' }]
        });

        console.log('[DEBUG] FestivalManager: Query result:', result.docs.length, 'documents found');
        console.log('[DEBUG] FestivalManager: First few results:', result.docs.slice(0, 3));

        // Let's also check what's actually in the database
        const allDocs = await this.db.allDocs({
          include_docs: true,
          startkey: 'festival_',
          endkey: 'festival_\ufff0'
        });
        console.log('[DEBUG] FestivalManager: Total festival documents in DB:', allDocs.rows.length);
        console.log('[DEBUG] FestivalManager: Sample festival docs:', allDocs.rows.slice(0, 3).map(row => {
          const doc = row.doc as any;
          return {
            id: row.id,
            documentType: doc?.documentType,
            type: doc?.type,
            name: doc?.name,
            isDeleted: doc?.isDeleted
          };
        }));

        return result.docs as Festival[];
      } else {
        // Fallback to allDocs if find plugin is not available
        console.warn('PouchDB Find plugin not available, using allDocs fallback for getFestivals. Performance may be impacted.');
        const result = await this.db.allDocs({
          include_docs: true,
          startkey: 'festival_',
          endkey: 'festival_\ufff0'
        });
        
        // Manual projection if using allDocs
        return result.rows
          .map(row => {
            const doc = row.doc as any;
            if (doc && doc.documentType === 'festival' && (doc.type === 'festival' || doc.type === 'regular_event')) {
              // Filter out deleted records if not including deleted
              if (!includeDeleted && doc.isDeleted === true) {
                return null;
              }
              return {
                _id: doc._id,
                _rev: doc._rev, // _rev is important for PouchDB operations
                name: doc.name,
                startDate: doc.startDate,
                endDate: doc.endDate,
                isActive: doc.isActive,
                documentType: doc.documentType,
                type: doc.type,
                locations: doc.locations,
                hasMultipleLocations: doc.hasMultipleLocations,
                showAdmissions: doc.showAdmissions,
                showFrontOfHouse: doc.showFrontOfHouse,
                showLostProperty: doc.showLostProperty,
                showShifts: doc.showShifts
              } as Festival;
            }
            return null;
          })
          .filter(doc => doc !== null) as Festival[];
      }
    } catch (error) {
      console.error('Error fetching festivals with projection:', error);
      throw error;
    }
  }

  async addFestival(festival: NewFestival): Promise<Festival> {
    const _id = `festival_${uuidv4()}`;

    const newFestival: Festival = {
      ...festival,
      _id,
      documentType: 'festival',
      type: festival.type || 'regular_event',
      syncStatus: 'local_only' // Default sync status
    };

    const response = await this.db.put(newFestival);
    await this.syncManager.syncAfterChange();

    return {
      ...newFestival,
      _rev: response.rev
    };
  }

  async updateFestival(festival: Festival): Promise<Festival> {
    const existing = await this.db.get(festival._id);
    const updated: Festival = {
      ...existing,
      ...festival,
      _rev: existing._rev
    };

    const response = await this.db.put(updated);
    await this.syncManager.syncAfterChange();

    return {
      ...updated,
      _rev: response.rev
    };
  }

  async deleteFestival(id: string): Promise<void> {
    try {
      const timestamp = new Date().toISOString();
      
      // Soft delete the festival document itself
      const doc = await this.db.get(id);
      const softDeletedFestival = {
        ...doc,
        isDeleted: true,
        deletedAt: timestamp,
        updatedAt: timestamp,
        syncStatus: 'sync_pending'
      };
      await this.db.put(softDeletedFestival);
      
      // Soft delete all associated data
      // 1. Admissions
      const admissions = await this.db.find({
        selector: {
          documentType: 'admission',
          festivalId: id,
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        }
      });
      for (const admission of admissions.docs) {
        const softDeletedAdmission = {
          ...admission,
          isDeleted: true,
          deletedAt: timestamp,
          updatedAt: timestamp,
          syncStatus: 'sync_pending'
        };
        await this.db.put(softDeletedAdmission);
      }
      
      // 2. Item counts
      const items = await this.db.find({
        selector: {
          documentType: 'item',
          festivalId: id,
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        }
      });
      for (const item of items.docs) {
        const softDeletedItem = {
          ...item,
          isDeleted: true,
          deletedAt: timestamp,
          updatedAt: timestamp,
          syncStatus: 'sync_pending'
        };
        await this.db.put(softDeletedItem);
      }
      
      // 3. Shift assignments
      const shifts = await this.db.find({
        selector: {
          documentType: 'shift_assignment',
          festivalId: id,
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        }
      });
      for (const shift of shifts.docs) {
        const softDeletedShift = {
          ...shift,
          isDeleted: true,
          deletedAt: timestamp,
          updatedAt: timestamp,
          syncStatus: 'sync_pending'
        };
        await this.db.put(softDeletedShift);
      }
      
      // 4. Festival notes
      try {
        const notesId = `notes_${id}`;
        const notes = await this.db.get(notesId);
        const softDeletedNotes = {
          ...notes,
          isDeleted: true,
          deletedAt: timestamp,
          updatedAt: timestamp,
          syncStatus: 'sync_pending'
        };
        await this.db.put(softDeletedNotes);
      } catch (error: any) {
        // Ignore 404 errors (notes might not exist)
        if (error.status !== 404) throw error;
      }
      
      // 5. Knowledge base items specific to this festival
      const knowledgeItems = await this.db.find({
        selector: {
          documentType: 'knowledgeBase',
          festivalId: id,
          showForAllFestivals: { $ne: true },
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        }
      });
      for (const item of knowledgeItems.docs) {
        const softDeletedKnowledge = {
          ...item,
          isDeleted: true,
          deletedAt: timestamp,
          updatedAt: timestamp,
          syncStatus: 'sync_pending'
        };
        await this.db.put(softDeletedKnowledge);
      }
      
      // Sync all changes
      await this.syncManager.syncAfterChange();
      
    } catch (error) {
      console.error('Error soft deleting festival and associated data:', error);
      throw error;
    }
  }

  async getFestivalNotes(festivalId: string): Promise<string> {
    const _id = `notes_${festivalId}`;
    try {
      const doc = await this.db.get(_id) as FestivalNotes;
      return doc.notes;
    } catch (error: any) {
      if (error.status === 404) {
        return '';
      }
      throw error;
    }
  }

  async updateFestivalNotes(festivalId: string, notes: string): Promise<void> {
    const _id = `notes_${festivalId}`;
    const timestamp = new Date().toISOString();

    const newNotes: FestivalNotes = {
      _id,
      documentType: 'festival_notes',
      type: 'festival_notes',
      festivalId: festivalId,
      notes: notes,
      lastUpdated: timestamp,
      syncStatus: 'local_only' // Default sync status
    };

    try {
      const existing = await this.db.get(_id);
      const updated: FestivalNotes = {
        ...existing,
        ...newNotes,
        _rev: existing._rev
      };

      await this.db.put(updated);
    } catch (error: any) {
      if (error.status === 404) {
        await this.db.put(newNotes);
      } else {
        throw error;
      }
    }

    await this.syncManager.syncAfterChange();
  }

  // Export method for database export service
  async exportAllFestivals(includeDeleted: boolean = false): Promise<Festival[]> {
    try {
      console.log('[EXPORT] Exporting all festivals...');
      
      // Check if the database has the createIndex method (PouchDB Find plugin)
      if (typeof this.db.createIndex === 'function') {
        // Create index for documentType if it doesn't exist
        await this.db.createIndex({
          index: {
            fields: ['documentType', 'isDeleted'],
            name: 'festival_documentType_index'
          }
        });

        const selector: any = {
          documentType: 'festival',
          $or: [
            { type: 'festival' },
            { type: 'regular_event' }
          ]
        };

        if (!includeDeleted) {
          selector.$and = [
            { $or: [{ type: 'festival' }, { type: 'regular_event' }] },
            { $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }] }
          ];
          delete selector.$or;
        }

        const result = await this.db.find({
          selector,
          sort: [{ _id: 'asc' }]
        });

        console.log(`[EXPORT] Exported ${result.docs.length} festival records (includeDeleted: ${includeDeleted})`);
        return result.docs as Festival[];
      } else {
        // Fallback to allDocs if find plugin is not available
        console.warn('[EXPORT] PouchDB Find plugin not available, using allDocs fallback for festival export');
        const result = await this.db.allDocs({
          include_docs: true,
          startkey: 'festival_',
          endkey: 'festival_\ufff0'
        });
        
        const festivals = result.rows
          .map(row => row.doc)
          .filter(doc => {
            if (!doc || (doc as any).documentType !== 'festival') return false;
            // Filter out deleted records if not including deleted
            if (!includeDeleted && (doc as any).isDeleted === true) return false;
            return true;
          }) as Festival[];
        
        console.log(`[EXPORT] Exported ${festivals.length} festival records (includeDeleted: ${includeDeleted})`);
        return festivals;
      }
    } catch (error) {
      console.error('[EXPORT] Failed to export festivals:', error);
      throw error;
    }
  }
}
