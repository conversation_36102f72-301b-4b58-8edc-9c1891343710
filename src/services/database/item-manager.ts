import PouchDB from 'pouchdb-browser';
import { v4 as uuidv4 } from 'uuid';
import { ItemDocument, ItemName } from '../../types/item';
import { SyncManager } from './sync-manager';

interface FindRequest {
  selector: {
    [key: string]: any;
  };
  use_index?: string[];
}

interface FindResponse<T> {
  docs: T[];
  warning?: string;
  execution_stats?: {
    total_keys_examined: number;
    total_docs_examined: number;
    total_quorum_docs_examined: number;
    results_returned: number;
    execution_time_ms: number;
  };
}

export class ItemManager {
  constructor(
    private db: PouchDB.Database,
    private syncManager: SyncManager
  ) {
    this.createIndexes();
  }

  private async createIndexes() {
    try {
      // Check if the database has the createIndex method (PouchDB Find plugin)
      if (typeof this.db.createIndex === 'function') {
        await this.db.createIndex({
          index: {
            fields: ['documentType', 'type', 'festivalId', 'siteLocationId', 'timestamp', 'isDeleted'],
            name: 'item_index'
          }
        });
        console.log('Item indexes created successfully');
      } else {
        console.warn('PouchDB Find plugin not available, skipping index creation');
      }
    } catch (error) {
      console.error('Error creating indexes:', error);
    }
  }

  private async getTodayDocument(festivalId: string, siteLocationId?: string): Promise<ItemDocument | null> {
    const startOfDay = new Date();
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date();
    endOfDay.setHours(23, 59, 59, 999);

    try {
      const selector: any = {
        documentType: 'item',
        type: 'item',
        festivalId: festivalId,
        timestamp: {
          $gte: startOfDay.toISOString(),
          $lte: endOfDay.toISOString()
        },
        $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
      };

      if (siteLocationId) {
        selector.siteLocationId = siteLocationId;
      }

      const request: FindRequest = { selector };

      const result = await (this.db as any).find(request) as FindResponse<ItemDocument>;
      console.log('Found today document:', result.docs);
      return result.docs[0] || null;
    } catch (error) {
      console.error('Error getting today document:', error);
      throw error;
    }
  }

  async addOrUpdateItemCount(
    itemName: ItemName,
    festivalId: string,
    siteLocationId?: string,
    quantity: number = 1
  ): Promise<ItemDocument> {
    console.log(`Adding/updating count for ${itemName} (${quantity}) in festival ${festivalId}, location ${siteLocationId}`);
    const today = new Date().toISOString();
    let doc = await this.getTodayDocument(festivalId, siteLocationId);

    if (doc) {
      console.log('Found existing document:', doc);
      // Update existing document
      const currentCount = typeof doc[itemName] === 'number' ? Number(doc[itemName]) : 0;
      const updatedDoc: ItemDocument = {
        ...doc,
        [itemName]: Number(currentCount) + quantity,
        timestamp: today,
        syncStatus: 'sync_pending'
      };
      
      try {
        const response = await this.db.put(updatedDoc);
        console.log('Updated document:', response);
        await this.syncManager.syncAfterChange();
        return {
          ...updatedDoc,
          _rev: response.rev
        };
      } catch (error) {
        console.error('Error updating document:', error);
        throw error;
      }
    } else {
      console.log('Creating new document for today');
      // Create new document for today
      const _id = `item_${uuidv4()}`;
      const newDoc: ItemDocument = {
        _id,
        documentType: 'item',
        type: 'item',
        festivalId,
        siteLocationId,
        timestamp: today,
        syncStatus: 'sync_pending',
        Suncream: 0,
        Poncho: 0,
        Water: 0,
        SanitaryProducts: 0,
        Earplugs: 0,
        Condoms: 0,
        ChildrensWristbands: 0,
        GeneralWristbands: 0,
        Charging: 0,
        Sanitizer: 0,
        ToiletRoll: 0,
        GeneralEnqs: 0,
        HotWater: 0,
        RestAndRecuperation: 0,
        Other: ''
      };

      if (itemName === 'Other') {
        newDoc[itemName] = '';
      } else {
        newDoc[itemName] = quantity;
      }

      try {
        const response = await this.db.put(newDoc);
        console.log('Created new document:', response);
        await this.syncManager.syncAfterChange();
        return {
          ...newDoc,
          _rev: response.rev
        };
      } catch (error) {
        console.error('Error creating document:', error);
        throw error;
      }
    }
  }

  async getItemCountsByFestival(festivalId: string, includeDeleted: boolean = false): Promise<ItemDocument[]> {
    console.log(`Getting item counts for festival ${festivalId}`);
    try {
      const request: FindRequest = {
        selector: {
          documentType: 'item',
          type: 'item',
          festivalId: festivalId,
          ...(includeDeleted ? {} : { $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }] })
        }
      };

      const result = await (this.db as any).find(request) as FindResponse<ItemDocument>;
      console.log('Found item counts:', result.docs);
      return result.docs.filter((doc): doc is ItemDocument =>
        doc.documentType === 'item' && doc.type === 'item'
      );
    } catch (error) {
      console.error('Error getting item counts:', error);
      throw error;
    }
  }

  async updateItemCount(item: ItemDocument): Promise<ItemDocument> {
    try {
      const existing = await this.db.get(item._id);
      const updated: ItemDocument = {
        ...existing,
        ...item,
        _rev: existing._rev,
        syncStatus: 'sync_pending'
      };

      const response = await this.db.put(updated);
      await this.syncManager.syncAfterChange();

      return {
        ...updated,
        _rev: response.rev
      };
    } catch (error) {
      console.error('Error updating item count:', error);
      throw error;
    }
  }

  async deleteItemCount(id: string): Promise<void> {
    try {
      const doc = await this.db.get(id);
      const timestamp = new Date().toISOString();
      
      const softDeletedDoc = {
        ...doc,
        isDeleted: true,
        deletedAt: timestamp,
        updatedAt: timestamp,
        syncStatus: 'sync_pending'
      };
      
      await this.db.put(softDeletedDoc);
      await this.syncManager.syncAfterChange();
    } catch (error) {
      console.error('Error soft deleting item count:', error);
      throw error;
    }
  }

  async bulkDeleteItems(ids: string[]): Promise<void> {
    if (ids.length === 0) return;

    try {
      console.log(`Starting bulk delete of ${ids.length} items`);
      
      // Retrieve all documents first
      const docsToDelete = [];
      const timestamp = new Date().toISOString();
      
      for (const id of ids) {
        try {
          const doc = await this.db.get(id);
          docsToDelete.push({
            ...doc,
            isDeleted: true,
            deletedAt: timestamp,
            updatedAt: timestamp,
            syncStatus: 'sync_pending'
          });
        } catch (error) {
          console.warn(`Failed to retrieve item ${id} for deletion:`, error);
          // Continue with other documents even if one fails
        }
      }

      if (docsToDelete.length === 0) {
        console.warn('No valid items found for bulk deletion');
        return;
      }

      // Perform bulk update using bulkDocs
      const result = await this.db.bulkDocs(docsToDelete);
      
      // Check for any errors in the bulk operation
      const errors = result.filter(r => 'error' in r);
      if (errors.length > 0) {
        console.error('Some items failed to delete:', errors);
      }

      const successCount = result.filter(r => 'ok' in r && r.ok).length;
      console.log(`Successfully bulk deleted ${successCount} of ${docsToDelete.length} items`);
      
      await this.syncManager.syncAfterChange();
    } catch (error) {
      console.error('Error in bulk delete items:', error);
      throw error;
    }
  }

  async cleanupOldItems(cutoffTimestamp: string): Promise<number> {
    try {
      let cleanedCount = 0;
      
      // Calculate 6-month cutoff for hard deletion (only hard delete records that are already soft deleted and older than 6 months)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      const hardDeleteCutoff = sixMonthsAgo.toISOString();
      
      // Find old soft-deleted items that can be hard deleted (soft deleted and older than 6 months)
      const hardDeleteRequest: FindRequest = {
        selector: {
          documentType: 'item',
          type: 'item',
          isDeleted: true,
          deletedAt: { $lt: hardDeleteCutoff }
        },
        use_index: ['item_index']
      };

      const hardDeleteResult = await (this.db as any).find(hardDeleteRequest) as FindResponse<ItemDocument>;
      
      // Hard delete old soft-deleted records
      for (const doc of hardDeleteResult.docs) {
        if (doc._id && doc._rev) {
          await this.db.remove(doc._id, doc._rev);
          cleanedCount++;
        }
      }

      // Find old item aggregations that should be soft deleted (older than cutoff, not already deleted)
      const softDeleteRequest: FindRequest = {
        selector: {
          documentType: 'item',
          type: 'item',
          timestamp: { $lt: cutoffTimestamp },
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        },
        use_index: ['item_index']
      };

      const softDeleteResult = await (this.db as any).find(softDeleteRequest) as FindResponse<ItemDocument>;
      
      // Soft delete old daily aggregations
      const timestamp = new Date().toISOString();
      for (const doc of softDeleteResult.docs) {
        if (doc._id && doc._rev) {
          const softDeletedDoc = {
            ...doc,
            isDeleted: true,
            deletedAt: timestamp,
            updatedAt: timestamp,
            syncStatus: 'sync_pending'
          };
          await this.db.put(softDeletedDoc);
          cleanedCount++;
        }
      }

      // Single sync call after all operations
      if (cleanedCount > 0) {
        await this.syncManager.syncAfterChange();
      }

      console.log(`Cleaned up ${cleanedCount} old item aggregation records (hard deleted: ${hardDeleteResult.docs.length}, soft deleted: ${softDeleteResult.docs.length})`);
      return cleanedCount;
    } catch (error) {
      console.error('Error cleaning up old items:', error);
      throw error;
    }
  }

  // Export method for database export service
  async exportAllItems(includeDeleted: boolean = false): Promise<ItemDocument[]> {
    try {
      console.log('[EXPORT] Exporting all items...');
      
      const selector: any = {
        documentType: 'item',
        type: 'item'
      };

      // Filter out deleted records unless explicitly requested
      if (!includeDeleted) {
        selector.$or = [
          { isDeleted: { $exists: false } },
          { isDeleted: false }
        ];
      }

      const request: FindRequest = {
        selector
      };

      const result = await (this.db as any).find(request) as FindResponse<ItemDocument>;
      
      const items = result.docs.filter((doc): doc is ItemDocument =>
        doc.documentType === 'item' && doc.type === 'item'
      );
      
      console.log(`[EXPORT] Exported ${items.length} item records (includeDeleted: ${includeDeleted})`);
      return items;
    } catch (error) {
      console.error('[EXPORT] Failed to export items:', error);
      throw error;
    }
  }
}
