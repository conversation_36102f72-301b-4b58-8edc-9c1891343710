import PouchDB from 'pouchdb-browser';
import { WelfareAdmission } from '../../types/admission';
import { SyncStatus } from '../../types/base';
import { databaseService } from './index';
import { SyncManager } from './sync-manager';
import debounce from 'lodash/debounce';

export interface BayStatus {
  isOccupied: boolean;
  occupiedBy?: {
    name: string;
    admissionId: string;
  };
  message: string;
}

export class BayManager {
  private syncManager: SyncManager;
  private debouncedSync: ReturnType<typeof debounce>;
  private readonly DEBOUNCE_DELAY = 2000; // 2 seconds

  constructor(
    private db: PouchDB.Database,
    syncManager: SyncManager
  ) {
    this.syncManager = syncManager;
    this.debouncedSync = debounce(this.syncChanges.bind(this), this.DEBOUNCE_DELAY);
  }

  private async syncChanges(admissionId: string): Promise<void> {
    await this.syncManager.syncAfterChange(admissionId);
  }

  async listAdmissionsForBay(bayNumber: number): Promise<WelfareAdmission[]> {
    try {
      const admissions = await databaseService.getAdmissionsByFestival('all');
      return admissions.filter((admission: WelfareAdmission) => admission.Location === bayNumber);
    } catch (error) {
      console.error('Error listing admissions for bay:', error);
      throw error;
    }
  }

  async checkBayAvailability(bayNumber: number): Promise<BayStatus> {
    try {
      // Get all admissions for this bay for debugging
      const bayAdmissions = await this.listAdmissionsForBay(bayNumber);
      
      // Find any active admission in this bay
      const occupyingAdmission = bayAdmissions.find(
        admission => 
          admission.BaysOrChairs === 'Bay' && 
          admission.InBayNow &&
          admission.status === 'active'
      );

      if (occupyingAdmission) {
        return {
          isOccupied: true,
          occupiedBy: {
            name: `${occupyingAdmission.FirstName} ${occupyingAdmission.Surname}`,
            admissionId: occupyingAdmission._id
          },
          message: `Bay ${bayNumber} is currently occupied by ${occupyingAdmission.FirstName} ${occupyingAdmission.Surname}`
        };
      }

      return {
        isOccupied: false,
        message: `Bay ${bayNumber} is available`
      };
    } catch (error) {
      console.error('Error checking bay availability:', error);
      throw error;
    }
  }

  async validateBayAssignment(bayNumber: number, admissionId: string): Promise<boolean> {
    try {
      const status = await this.checkBayAvailability(bayNumber);
      
      // Bay is available or occupied by this admission
      return !status.isOccupied || status.occupiedBy?.admissionId === admissionId;
    } catch (error) {
      console.error('Error validating bay assignment:', error);
      throw error;
    }
  }

  async assignToBay(admissionId: string, bayNumber: number): Promise<void> {
    try {
      const admission = await this.db.get<WelfareAdmission>(admissionId);
      
      // Update admission with new bay assignment
      const updated: WelfareAdmission = {
        ...admission,
        Location: bayNumber,
        InBayNow: true,
        syncStatus: 'sync_pending' as SyncStatus,
        updatedAt: new Date().toISOString()
      };

      await this.db.put(updated);
      
      // Use debounced sync for rapid changes
      this.debouncedSync(admissionId);
    } catch (error) {
      console.error('Error assigning to bay:', error);
      throw error;
    }
  }

  async removeFromBay(admissionId: string): Promise<void> {
    try {
      const admission = await this.db.get<WelfareAdmission>(admissionId);
      
      // Update admission to remove from bay
      const updated: WelfareAdmission = {
        ...admission,
        InBayNow: false,
        syncStatus: 'sync_pending' as SyncStatus,
        updatedAt: new Date().toISOString()
      };

      await this.db.put(updated);
      
      // Use debounced sync for rapid changes
      this.debouncedSync(admissionId);
    } catch (error) {
      console.error('Error removing from bay:', error);
      throw error;
    }
  }

  cleanup() {
    if (this.debouncedSync) {
      this.debouncedSync.cancel();
    }
  }
}