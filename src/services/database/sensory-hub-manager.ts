import PouchDB from 'pouchdb-browser';
import { v4 as uuidv4 } from 'uuid';
import { SensoryHubVisit, NewSensoryHubVisit } from '../../types/sensory-hub';
import { SyncManager } from './sync-manager';
import { SyncStatus } from '../../types/base';

interface FindResponse<T> {
  docs: Array<T & PouchDB.Core.IdMeta & PouchDB.Core.GetMeta>;
}

type ExistingSensoryHubVisit = Omit<SensoryHubVisit, '_rev'> & PouchDB.Core.IdMeta & PouchDB.Core.GetMeta & {
  createdAt: string;
  timestamp: string;
};

/**
 * SensoryHubManager handles all database operations for sensory hub visitor tracking.
 * Follows the established patterns for data management using PouchDB with manager classes.
 */
export class SensoryHubManager {
  constructor(
    private db: PouchDB.Database,
    private syncManager: SyncManager
  ) {
    this.createIndexes();
  }

  /**
   * Creates database indexes for efficient querying of sensory hub visits
   */
  private async createIndexes() {
    try {
      // Check if the database has the createIndex method (PouchDB Find plugin)
      if (typeof this.db.createIndex === 'function') {
        // Create comprehensive index for sensory hub visit queries
        await this.db.createIndex({
          index: {
            fields: ['documentType', 'festivalId', 'visitTimestamp', 'isDeleted'],
            name: 'sensory-hub-festival-index'
          }
        });

        // Create index for location-based queries
        await this.db.createIndex({
          index: {
            fields: ['documentType', 'siteLocationId', 'visitTimestamp', 'isDeleted'],
            name: 'sensory-hub-location-index'
          }
        });

        // Create index for user type and purpose queries
        await this.db.createIndex({
          index: {
            fields: ['documentType', 'userType', 'purpose', 'visitTimestamp', 'isDeleted'],
            name: 'sensory-hub-type-index'
          }
        });

        console.log('Sensory hub indexes created successfully');
      } else {
        console.warn('PouchDB Find plugin not available, skipping index creation');
      }
    } catch (error) {
      console.warn('Failed to create sensory hub indexes:', error);
    }
  }

  /**
   * Validates that crew visits have a team name
   */
  private validateVisitData(visitData: NewSensoryHubVisit): void {
    if (visitData.userType === 'crew' && !visitData.teamName) {
      throw new Error('Team name is required for crew visits');
    }
  }

  /**
   * Adds a new sensory hub visit to the database
   * @param visitData - The visit data to add
   * @returns Promise<SensoryHubVisit> - The created visit with database metadata
   */
  async addVisit(visitData: NewSensoryHubVisit): Promise<SensoryHubVisit> {
    try {
      // Validate the visit data
      this.validateVisitData(visitData);

      const _id = `sensory-hub-visit_${uuidv4()}`;
      const timestamp = new Date().toISOString();

      const newVisit: SensoryHubVisit = {
        ...visitData,
        _id,
        documentType: 'sensory-hub-visit',
        type: 'sensory-hub-visit',
        createdAt: timestamp,
        timestamp: timestamp,
        syncStatus: 'sync_pending' as SyncStatus
      };

      const response = await this.db.put(newVisit);
      
      // Wait for sync to complete
      await this.syncManager.syncAfterChange(_id);
      
      return {
        ...newVisit,
        _rev: response.rev
      };
    } catch (error) {
      console.error('Failed to add sensory hub visit:', error);
      throw error;
    }
  }

  /**
   * Retrieves all sensory hub visits for a specific festival
   * @param festivalId - The festival ID to filter by
   * @param includeDeleted - Whether to include soft-deleted records
   * @returns Promise<SensoryHubVisit[]> - Array of visits for the festival
   */
  async getVisitsByFestival(festivalId: string, includeDeleted: boolean = false): Promise<SensoryHubVisit[]> {
    try {
      const selector = festivalId === 'all'
        ? {
            documentType: 'sensory-hub-visit',
            ...(includeDeleted ? {} : { $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }] })
          }
        : {
            documentType: 'sensory-hub-visit',
            festivalId: festivalId,
            ...(includeDeleted ? {} : { $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }] })
          };
      
      const result = await this.db.find({
        selector: selector
      }) as FindResponse<SensoryHubVisit>;

      const visits = result.docs
        .map((doc) => {
          if (doc.documentType === 'sensory-hub-visit' && doc.type === 'sensory-hub-visit') {
            return doc as SensoryHubVisit;
          }
          return null;
        })
        .filter((doc: SensoryHubVisit | null): doc is SensoryHubVisit => doc !== null);

      // Sort by visitTimestamp in descending order (most recent first)
      return visits.sort((a, b) => new Date(b.visitTimestamp).getTime() - new Date(a.visitTimestamp).getTime());
    } catch (error) {
      console.error('Failed to get sensory hub visits by festival:', error);
      throw error;
    }
  }

  /**
   * Retrieves all sensory hub visits for a specific location
   * @param siteLocationId - The site location ID to filter by
   * @param includeDeleted - Whether to include soft-deleted records
   * @returns Promise<SensoryHubVisit[]> - Array of visits for the location
   */
  async getVisitsByLocation(siteLocationId: string, includeDeleted: boolean = false): Promise<SensoryHubVisit[]> {
    try {
      const result = await this.db.find({
        selector: {
          documentType: 'sensory-hub-visit',
          siteLocationId: siteLocationId,
          ...(includeDeleted ? {} : { $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }] })
        }
      }) as FindResponse<SensoryHubVisit>;

      const visits = result.docs
        .map((doc) => {
          if (doc.documentType === 'sensory-hub-visit' && doc.type === 'sensory-hub-visit') {
            return doc as SensoryHubVisit;
          }
          return null;
        })
        .filter((doc: SensoryHubVisit | null): doc is SensoryHubVisit => doc !== null);

      // Sort by visitTimestamp in descending order (most recent first)
      return visits.sort((a, b) => new Date(b.visitTimestamp).getTime() - new Date(a.visitTimestamp).getTime());
    } catch (error) {
      console.error('Failed to get sensory hub visits by location:', error);
      throw error;
    }
  }

  /**
   * Updates an existing sensory hub visit
   * @param visitData - The updated visit data
   * @returns Promise<SensoryHubVisit> - The updated visit
   */
  async updateVisit(visitData: SensoryHubVisit): Promise<SensoryHubVisit> {
    if (!visitData._id) {
      throw new Error('Cannot update sensory hub visit without _id');
    }

    try {
      // Validate the visit data
      this.validateVisitData(visitData);

      const existing = await this.db.get<ExistingSensoryHubVisit>(visitData._id);
      const timestamp = new Date().toISOString();
      
      const updated: SensoryHubVisit = {
        ...visitData,
        _id: visitData._id,
        _rev: existing._rev,
        updatedAt: timestamp,
        // Keep original creation timestamp
        createdAt: existing.createdAt,
        timestamp: existing.timestamp,
        syncStatus: 'sync_pending' as SyncStatus
      };

      const response = await this.db.put(updated);
      
      // Wait for sync to complete
      await this.syncManager.syncAfterChange(visitData._id);
      
      return {
        ...updated,
        _rev: response.rev
      };
    } catch (error) {
      console.error('Failed to update sensory hub visit:', error);
      throw error;
    }
  }

  /**
   * Soft deletes a sensory hub visit (marks as deleted without removing from database)
   * @param id - The ID of the visit to delete
   * @returns Promise<void>
   */
  async deleteVisit(id: string): Promise<void> {
    try {
      const doc = await this.db.get<ExistingSensoryHubVisit>(id);
      const timestamp = new Date().toISOString();
      
      const softDeletedDoc = {
        ...doc,
        isDeleted: true,
        deletedAt: timestamp,
        updatedAt: timestamp,
        syncStatus: 'sync_pending' as SyncStatus
      };
      
      await this.db.put(softDeletedDoc);
      
      // Wait for sync to complete
      await this.syncManager.syncAfterChange(id);
    } catch (error) {
      console.error('Failed to soft delete sensory hub visit:', error);
      throw error;
    }
  }

  /**
   * Bulk soft deletes multiple sensory hub visits
   * @param ids - Array of visit IDs to delete
   * @returns Promise<void>
   */
  async bulkDeleteVisits(ids: string[]): Promise<void> {
    try {
      if (ids.length === 0) return;
      
      console.log(`[SENSORY-HUB] Bulk deleting ${ids.length} visits`);
      const timestamp = new Date().toISOString();
      const docs = [];
      
      // Get all documents to be deleted
      for (const id of ids) {
        try {
          const doc = await this.db.get<ExistingSensoryHubVisit>(id);
          docs.push({
            ...doc,
            isDeleted: true,
            deletedAt: timestamp,
            updatedAt: timestamp,
            syncStatus: 'sync_pending' as SyncStatus
          });
        } catch (error) {
          console.warn(`[SENSORY-HUB] Failed to get visit ${id} for bulk delete:`, error);
          // Continue with other documents
        }
      }
      
      // Bulk update all documents
      if (docs.length > 0) {
        await this.db.bulkDocs(docs);
        console.log(`[SENSORY-HUB] Successfully bulk deleted ${docs.length} visits`);
        
        // Queue sync for all changes
        this.syncManager.syncAfterChange().catch(syncError => {
          console.warn('[SYNC] Bulk sync failed for sensory hub visits:', syncError);
        });
      }
    } catch (error) {
      console.error('Failed to bulk delete sensory hub visits:', error);
      throw error;
    }
  }

  /**
   * Cleans up old sensory hub visit records
   * Soft deletes visits older than cutoff, hard deletes soft-deleted visits older than 6 months
   * @param cutoffTimestamp - ISO timestamp for soft deletion cutoff
   * @returns Promise<number> - Number of records cleaned up
   */
  async cleanupOldVisits(cutoffTimestamp: string): Promise<number> {
    try {
      let cleanedCount = 0;
      
      // Calculate 6-month cutoff for hard deletion (only hard delete records that are already soft deleted and older than 6 months)
      const sixMonthsAgo = new Date();
      sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);
      const hardDeleteCutoff = sixMonthsAgo.toISOString();
      
      // Find old soft-deleted visits that can be hard deleted (soft deleted and older than 6 months)
      const hardDeleteResult = await this.db.find({
        selector: {
          documentType: 'sensory-hub-visit',
          isDeleted: true,
          deletedAt: { $lt: hardDeleteCutoff }
        }
      }) as FindResponse<SensoryHubVisit>;

      // Hard delete old soft-deleted records
      for (const doc of hardDeleteResult.docs) {
        if (doc._id && doc._rev) {
          await this.db.remove(doc._id, doc._rev);
          cleanedCount++;
        }
      }

      // Find old visits that should be soft deleted (older than cutoff, not already deleted)
      const softDeleteResult = await this.db.find({
        selector: {
          documentType: 'sensory-hub-visit',
          visitTimestamp: { $lt: cutoffTimestamp },
          $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }]
        }
      }) as FindResponse<SensoryHubVisit>;

      // Soft delete old visits
      const timestamp = new Date().toISOString();
      for (const doc of softDeleteResult.docs) {
        if (doc._id && doc._rev) {
          const softDeletedDoc = {
            ...doc,
            isDeleted: true,
            deletedAt: timestamp,
            updatedAt: timestamp,
            syncStatus: 'sync_pending' as SyncStatus
          };
          await this.db.put(softDeletedDoc);
          cleanedCount++;
        }
      }

      // Single sync call after all operations
      if (cleanedCount > 0) {
        await this.syncManager.syncAfterChange();
      }

      console.log(`Cleaned up ${cleanedCount} old sensory hub visit records (hard deleted: ${hardDeleteResult.docs.length}, soft deleted: ${softDeleteResult.docs.length})`);
      return cleanedCount;
    } catch (error) {
      console.error('Failed to cleanup old sensory hub visits:', error);
      throw error;
    }
  }

  /**
   * Export method for database export service
   * @param includeDeleted - Whether to include soft-deleted records
   * @returns Promise<SensoryHubVisit[]> - All sensory hub visits
   */
  async exportAllVisits(includeDeleted: boolean = false): Promise<SensoryHubVisit[]> {
    try {
      console.log('[EXPORT] Exporting all sensory hub visits...');
      
      const result = await this.db.find({
        selector: {
          documentType: 'sensory-hub-visit',
          ...(includeDeleted ? {} : { $or: [{ isDeleted: { $exists: false } }, { isDeleted: false }] })
        }
      }) as FindResponse<SensoryHubVisit>;

      const visits = result.docs
        .map((doc) => {
          if (doc.documentType === 'sensory-hub-visit' && doc.type === 'sensory-hub-visit') {
            return doc as SensoryHubVisit;
          }
          return null;
        })
        .filter((doc: SensoryHubVisit | null): doc is SensoryHubVisit => doc !== null);

      // Sort by visitTimestamp in descending order (most recent first)
      const sortedVisits = visits.sort((a, b) => new Date(b.visitTimestamp).getTime() - new Date(a.visitTimestamp).getTime());
      
      console.log(`[EXPORT] Exported ${sortedVisits.length} sensory hub visit records (includeDeleted: ${includeDeleted})`);
      return sortedVisits;
    } catch (error) {
      console.error('[EXPORT] Failed to export sensory hub visits:', error);
      throw error;
    }
  }
}