import PouchDB from 'pouchdb-browser';
import { v4 as uuidv4 } from 'uuid';
import { 
  ShiftConfig, 
  NewShiftConfig, 
  TeamLeader, 
  NewTeamLeader, 
  ShiftAssignment, 
  NewShiftAssignment 
} from '../../types/shift';
import { SyncManager } from './sync-manager';

interface FindResponse<T> {
  docs: Array<T & PouchDB.Core.IdMeta & PouchDB.Core.GetMeta>;
}

export class ShiftManager {
  constructor(
    private db: PouchDB.Database,
    private syncManager: SyncManager
  ) {}

  // Shift Configuration Methods
  async getShiftConfig(festivalId: string): Promise<ShiftConfig | null> {
    const _id = `shift_config_${festivalId}`;
    try {
      const doc = await this.db.get(_id) as ShiftConfig;
      return doc;
    } catch (error: any) {
      if (error.status === 404) {
        return null;
      }
      throw error;
    }
  }

  async saveShiftConfig(config: NewShiftConfig): Promise<ShiftConfig> {
    const _id = `shift_config_${config.festivalId}`;
    const newConfig: ShiftConfig = {
      ...config,
      _id,
      documentType: 'shift_config',
      type: 'shift_config',
      syncStatus: 'local_only'
    };

    try {
      const existing = await this.db.get(_id);
      const updated = {
        ...existing,
        ...newConfig,
        _rev: existing._rev
      };
      const response = await this.db.put(updated);
      return { ...updated, _rev: response.rev };
    } catch (error: any) {
      if (error.status === 404) {
        const response = await this.db.put(newConfig);
        return { ...newConfig, _rev: response.rev };
      }
      throw error;
    }
  }

  // Team Leader Methods
  async getTeamLeaders(festivalId: string): Promise<TeamLeader[]> {
    try {
      const result = await this.db.allDocs<TeamLeader>({
        include_docs: true,
        startkey: `team_leader_${festivalId}_`,
        endkey: `team_leader_${festivalId}_\ufff0`
      });

      return result.rows
        .filter(row => row.doc !== undefined)
        .map(row => row.doc!);
    } catch (error) {
      console.error('Error fetching team leaders:', error);
      throw error;
    }
  }

  async addTeamLeader(leader: NewTeamLeader): Promise<TeamLeader> {
    const _id = `team_leader_${leader.festivalId}_${uuidv4()}`;
    const newLeader: TeamLeader = {
      ...leader,
      _id,
      documentType: 'team_leader',
      type: 'team_leader',
      teams: leader.teams || [], // Initialize teams array
      syncStatus: 'local_only'
    };

    const response = await this.db.put(newLeader);
    await this.syncManager.syncAfterChange();
    return { ...newLeader, _rev: response.rev };
  }

  async updateTeamLeader(leader: TeamLeader): Promise<TeamLeader> {
    const existing = await this.db.get(leader._id);
    const updated = {
      ...existing,
      ...leader,
      _rev: existing._rev
    };

    const response = await this.db.put(updated);
    await this.syncManager.syncAfterChange();
    return { ...updated, _rev: response.rev };
  }

  async deleteTeamLeader(id: string): Promise<void> {
    const doc = await this.db.get(id);
    await this.db.remove(doc);
    await this.syncManager.syncAfterChange();
  }

  // Shift Assignment Methods
  async getShiftAssignments(festivalId: string): Promise<ShiftAssignment[]> {
    try {
      const result = await this.db.allDocs<ShiftAssignment>({
        include_docs: true,
        startkey: `shift_assignment_${festivalId}_`,
        endkey: `shift_assignment_${festivalId}_\ufff0`
      });

      return result.rows
        .filter(row => row.doc !== undefined)
        .map(row => row.doc!)
        .sort((a, b) => {
          // Sort by date first
          const dateCompare = a.date.localeCompare(b.date);
          if (dateCompare !== 0) return dateCompare;
          // Then by shift number
          return a.shiftNumber - b.shiftNumber;
        });
    } catch (error) {
      console.error('Error fetching shift assignments:', error);
      throw error;
    }
  }

  async addShiftAssignment(assignment: NewShiftAssignment): Promise<ShiftAssignment> {
    const _id = `shift_assignment_${assignment.festivalId}_${uuidv4()}`;
    const newAssignment: ShiftAssignment = {
      ...assignment,
      _id,
      documentType: 'shift_assignment',
      type: 'shift_assignment',
      syncStatus: 'local_only'
    };

    const response = await this.db.put(newAssignment);
    await this.syncManager.syncAfterChange();
    return { ...newAssignment, _rev: response.rev };
  }

  async updateShiftAssignment(assignment: ShiftAssignment): Promise<ShiftAssignment> {
    const existing = await this.db.get(assignment._id);
    const updated = {
      ...existing,
      ...assignment,
      _rev: existing._rev
    };

    const response = await this.db.put(updated);
    await this.syncManager.syncAfterChange();
    return { ...updated, _rev: response.rev };
  }

  async deleteShiftAssignment(id: string): Promise<void> {
    const doc = await this.db.get(id);
    await this.db.remove(doc);
    await this.syncManager.syncAfterChange();
  }

  // Utility Methods
  async generateShiftSchedule(
    festivalId: string,
    startDate: string,
    endDate: string,
    config: ShiftConfig
  ): Promise<ShiftAssignment[]> {
    const assignments: ShiftAssignment[] = [];
    const start = new Date(startDate);
    const end = new Date(endDate);

    // Validate config
    if (!this.validateShiftTimes(config.firstShiftStart, '00:00')) {
      console.error('Invalid first shift start time:', config.firstShiftStart);
      return assignments;
    }

    for (let date = new Date(start); date <= end; date.setDate(date.getDate() + 1)) {
      for (let shiftNum = 1; shiftNum <= config.shiftsPerDay; shiftNum++) {
        // Calculate shift times
        const times = this.calculateShiftTimes(config.firstShiftStart, config.shiftDuration, shiftNum);

        // Ensure times are valid
        if (!this.validateShiftTimes(times.start, times.end)) {
          console.error('Invalid shift times:', times);
          continue;
        }

        const teamLetter = String.fromCharCode(65 + (shiftNum - 1)); // A, B, C, etc.
        const newAssignment: NewShiftAssignment = {
          type: 'shift_assignment',
          documentType: 'shift_assignment',
          festivalId,
          date: date.toISOString().split('T')[0],
          shiftNumber: shiftNum,
          startTime: times.start,
          endTime: times.end,
          teamLeaderId: '', // To be assigned
          teamLetter,
          teamMembers: [], // To be assigned
          syncStatus: 'local_only'
        };

        try {
          const assignment = await this.addShiftAssignment(newAssignment);
          assignments.push(assignment);
        } catch (error) {
          console.error('Error creating shift assignment:', error);
        }
      }
    }

    return assignments;
  }

  private calculateShiftTimes(firstShiftStart: string, duration: number, shiftNumber: number): { start: string; end: string } {
    try {
      const [startHour, startMinute] = firstShiftStart.split(':').map(Number);
      if (isNaN(startHour) || isNaN(startMinute)) {
        console.error('Invalid time format:', firstShiftStart);
        return { start: '00:00', end: '00:00' };
      }

      const shiftOffsetHours = (shiftNumber - 1) * duration;
      
      const startTime = new Date();
      startTime.setHours(startHour + shiftOffsetHours, startMinute, 0);
      
      const endTime = new Date(startTime);
      endTime.setHours(startTime.getHours() + duration);

      return {
        start: startTime.toTimeString().slice(0, 5),
        end: endTime.toTimeString().slice(0, 5)
      };
    } catch (error) {
      console.error('Error calculating shift times:', error);
      return { start: '00:00', end: '00:00' };
    }
  }

  // Helper method to validate shift times
  private validateShiftTimes(startTime: string, endTime: string): boolean {
    const timePattern = /^([01]?[0-9]|2[0-3]):[0-5][0-9]$/;
    return timePattern.test(startTime) && timePattern.test(endTime);
  }

  async cleanupOldShifts(cutoffTimestamp: string): Promise<number> {
    try {
      let cleanedCount = 0;
      const cutoffDate = new Date(cutoffTimestamp);
      
      // Get all shift assignments and filter by date
      const allAssignments = await this.db.allDocs<ShiftAssignment>({
        include_docs: true,
        startkey: 'shift_assignment_',
        endkey: 'shift_assignment_\ufff0'
      });

      const oldAssignments = allAssignments.rows
        .filter(row => row.doc !== undefined)
        .map(row => row.doc!)
        .filter(doc => {
          // Check if shift date is older than cutoff
          const shiftDate = new Date(doc.date);
          return shiftDate < cutoffDate;
        });

      // Remove old shift assignments
      for (const doc of oldAssignments) {
        if (doc._id && doc._rev) {
          await this.db.remove(doc._id, doc._rev);
          cleanedCount++;
        }
      }

      // Also clean up old team leaders (those without recent assignments)
      const allLeaders = await this.db.allDocs<TeamLeader>({
        include_docs: true,
        startkey: 'team_leader_',
        endkey: 'team_leader_\ufff0'
      });

      // Get remaining assignments to check which leaders are still needed
      const remainingAssignments = await this.db.allDocs<ShiftAssignment>({
        include_docs: true,
        startkey: 'shift_assignment_',
        endkey: 'shift_assignment_\ufff0'
      });

      const activeLeaderIds = new Set(
        remainingAssignments.rows
          .filter(row => row.doc !== undefined)
          .map(row => row.doc!.teamLeaderId)
          .filter(id => id) // Remove empty strings
      );

      // Remove team leaders that are no longer referenced
      for (const row of allLeaders.rows) {
        const leader = row.doc;
        if (leader && leader._id && leader._rev && !activeLeaderIds.has(leader._id)) {
          await this.db.remove(leader._id, leader._rev);
          cleanedCount++;
        }
      }

      // Single sync call after all removals
      if (cleanedCount > 0) {
        await this.syncManager.syncAfterChange();
      }

      console.log(`Cleaned up ${cleanedCount} old shift records`);
      return cleanedCount;
    } catch (error) {
      console.error('Error cleaning up old shifts:', error);
      throw error;
    }
  }

  // Export method for database export service
  async exportAllShifts(): Promise<{
    configs: ShiftConfig[];
    teamLeaders: TeamLeader[];
    assignments: ShiftAssignment[];
  }> {
    try {
      console.log('[EXPORT] Exporting all shift data...');
      
      // Export shift configurations
      const configResult = await this.db.find({
        selector: {
          documentType: 'shift_config'
        }
      }) as FindResponse<ShiftConfig>;
      
      const configs = configResult.docs;

      // Export team leaders
      const leadersResult = await this.db.find({
        selector: {
          documentType: 'team_leader'
        }
      }) as FindResponse<TeamLeader>;
      
      const teamLeaders = leadersResult.docs;

      // Export shift assignments
      const assignmentsResult = await this.db.find({
        selector: {
          documentType: 'shift_assignment'
        }
      }) as FindResponse<ShiftAssignment>;
      
      const assignments = assignmentsResult.docs;

      console.log(`[EXPORT] Exported ${configs.length} shift configs, ${teamLeaders.length} team leaders, ${assignments.length} shift assignments`);
      
      return {
        configs,
        teamLeaders,
        assignments
      };
    } catch (error) {
      console.error('[EXPORT] Failed to export shift data:', error);
      throw error;
    }
  }
}
