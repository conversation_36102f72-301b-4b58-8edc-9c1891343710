import React, { useState, useEffect } from 'react';
import { NewFestival, FestivalLocation } from '../types/festival';
import { databaseService } from '../services/database/index';
import { useFestival } from '../contexts/FestivalContext';
import { FestivalEditModal } from '../components/FestivalEditModal';
import { ShiftNotes } from '../components/ShiftNotes';
import { FestivalHeader } from '../components/festival/FestivalHeader';
import { FestivalForm } from '../components/festival/FestivalForm';
import { AdminPanelSection } from '../components/festival/AdminPanelSection';
import {
  Box,
  Stack,
  Alert,
  AlertTitle,
  CircularProgress,
  Container,
  Typography,
} from '@mui/material';

interface FestivalFormData {
  name: string;
  startDate: string;
  endDate: string;
  location: string;
  type: 'festival' | 'regular_event';
  mainUrl: string;
  mapUrl: string;
  travelInfoUrl: string;
  faqsUrl: string;
  showAdmissions: boolean;
  showFrontOfHouse: boolean;
  showLostProperty: boolean;
  showShifts: boolean;
  hasMultipleLocations?: boolean;
  locations?: FestivalLocation[];
}

export const FestivalManagementPage: React.FC = () => {
  const { activeFestival, loading, error: contextError, manualSync } = useFestival();
  const [error, setError] = useState<Error | null>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [formData, setFormData] = useState<FestivalFormData>({
    name: '',
    startDate: '',
    endDate: '',
    location: '',
    type: 'festival',
    mainUrl: '',
    mapUrl: '',
    travelInfoUrl: '',
    faqsUrl: '',
    showAdmissions: true,
    showFrontOfHouse: true,
    showLostProperty: true,
    showShifts: false,
    hasMultipleLocations: false,
    locations: []
  });
  const [isEditModalOpen, setIsEditModalOpen] = useState(false);
  const [isNotesOpen, setIsNotesOpen] = useState(false);

  useEffect(() => {
    const initializeDatabase = async () => {
      try {
        const unsubscribe = databaseService.addSyncListener(() => {
          setIsInitialized(true);
        });

        setIsInitialized(true);

        return () => {
          unsubscribe();
        };
      } catch (err) {
        console.error('Error initializing database:', err);
        setError(err as Error);
        setIsInitialized(true);
      }
    };

    if (!isInitialized) {
      initializeDatabase();
    }
  }, [isInitialized]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const newFestival: NewFestival = {
        ...formData,
        type: 'festival',
        documentType: 'festival',
        syncStatus: 'sync_pending',
        isActive: false,
        locations: formData.locations || [],
        hasMultipleLocations: formData.hasMultipleLocations || false
      };

      await databaseService.addFestival(newFestival);
      setFormData({
        name: '',
        startDate: '',
        endDate: '',
        location: '',
        type: 'festival',
        mainUrl: '',
        mapUrl: '',
        travelInfoUrl: '',
        faqsUrl: '',
        showAdmissions: true,
        showFrontOfHouse: true,
        showLostProperty: true,
        showShifts: false,
        hasMultipleLocations: false,
        locations: []
      });
      setError(null);
    } catch (err) {
      setError(err as Error);
    }
  };

  if (!isInitialized || loading) {
    return (
      <Box
        sx={{
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          justifyContent: 'center',
          minHeight: '200px',
          gap: 2
        }}
      >
        <CircularProgress />
        <Typography color="text.secondary">
          Initializing database...
        </Typography>
      </Box>
    );
  }

  return (
    <Container maxWidth="lg">
      <Stack spacing={3}>
        <FestivalHeader
          onEditClick={() => setIsEditModalOpen(true)}
          onNotesClick={() => setIsNotesOpen(true)}
          onSyncClick={manualSync}
        />

        <FestivalForm
          formData={formData}
          onSubmit={handleSubmit}
          onChange={setFormData}
        />

        {(error || contextError) && (
          <Alert severity="error">
            <AlertTitle>Error</AlertTitle>
            {error?.message || contextError?.message}
          </Alert>
        )}

        <AdminPanelSection festivalId={activeFestival?._id || null} />

        {activeFestival && isEditModalOpen && (
          <FestivalEditModal
            festival={activeFestival}
            isOpen={true}
            onClose={() => setIsEditModalOpen(false)}
            onSave={manualSync}
          />
        )}
        {isNotesOpen && <ShiftNotes onClose={() => setIsNotesOpen(false)} />}
      </Stack>
    </Container>
  );
};
