import React, { useState, useEffect } from 'react';
import { useParams } from 'react-router-dom';
import { Box, Typography, Stack } from '@mui/material';
import { databaseService } from '../services/database';
import { ShiftConfig, TeamLeader, ShiftAssignment } from '../types/shift';
import { useFestival } from '../contexts/FestivalContext';
import ShiftConfigForm from '../components/shifts/ShiftConfigForm';
import TeamLeaderForm from '../components/shifts/TeamLeaderForm';
import ShiftScheduleTable from '../components/shifts/ShiftScheduleTable';

const ShiftsPage: React.FC = () => {
  const { festivalId } = useParams<{ festivalId: string }>();
  const { activeFestival } = useFestival();
  const [shiftConfig, setShiftConfig] = useState<ShiftConfig | null>(null);
  const [teamLeaders, setTeamLeaders] = useState<TeamLeader[]>([]);
  const [shiftAssignments, setShiftAssignments] = useState<ShiftAssignment[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadShiftData = async () => {
      if (!festivalId) {
        setLoading(false);
        return;
      }

      try {
        const [config, leaders, assignments] = await Promise.all([
          databaseService.getShiftConfig(festivalId),
          databaseService.getTeamLeaders(festivalId),
          databaseService.getShiftAssignments(festivalId)
        ]);

        setShiftConfig(config);
        setTeamLeaders(leaders);
        setShiftAssignments(assignments);
      } catch (error) {
        console.error('Error loading shift data:', error);
      } finally {
        setLoading(false);
      }
    };

    loadShiftData();
  }, [festivalId]);

  if (loading) {
    return <Box sx={{ p: 2 }}>Loading...</Box>;
  }

  if (!activeFestival || !festivalId) {
    return <Box sx={{ p: 2 }}>No festival selected</Box>;
  }

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 2 }}>
        Shift Management
      </Typography>
      <Typography variant="h5" sx={{ mb: 2 }}>
        {activeFestival.name}
      </Typography>

      <Stack spacing={4}>
        {/* Configuration Section */}
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
            Shift Configuration
          </Typography>
          <ShiftConfigForm
            festivalId={festivalId}
            existingConfig={shiftConfig}
            onConfigSaved={(config) => setShiftConfig(config)}
          />
        </Box>

        {/* Team Leaders Section */}
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
            Team Leaders
          </Typography>
          <TeamLeaderForm
            festivalId={festivalId}
            teamLeaders={teamLeaders}
            onTeamLeaderChange={() => {
              databaseService.getTeamLeaders(festivalId).then(setTeamLeaders);
            }}
          />
        </Box>

        {/* Shift Schedule Section */}
        <Box>
          <Typography variant="h6" sx={{ fontWeight: 600, mb: 2 }}>
            Shift Schedule
          </Typography>
          {shiftConfig ? (
            <ShiftScheduleTable
              festivalId={festivalId}
              startDate={activeFestival.startDate}
              endDate={activeFestival.endDate}
              shiftConfig={shiftConfig}
              teamLeaders={teamLeaders}
              assignments={shiftAssignments}
              onAssignmentChange={() => {
                databaseService.getShiftAssignments(festivalId).then(setShiftAssignments);
              }}
            />
          ) : (
            <Typography color="text.secondary">
              Please configure shift settings first
            </Typography>
          )}
        </Box>
      </Stack>
    </Box>
  );
};

export default ShiftsPage;
