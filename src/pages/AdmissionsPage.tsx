import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { AdmissionList } from '../components/AdmissionList';
import { useAdmissions } from '../hooks/useAdmissions';
import { WelfareAdmission } from '../types/admission';
import { databaseService } from '../services/database';

export const AdmissionsPage: React.FC = () => {
  const { admissions, loading, error, updateAdmission, refreshAdmissions } = useAdmissions();
  const navigate = useNavigate();
  
  const handleDischarge = async (id: string, notes: string, dischargeTime: string) => {
    const admission = admissions.find((a: WelfareAdmission) => a._id === id || a._id === id);
    if (admission) {
      const formattedTime = new Date(dischargeTime).toLocaleTimeString('en-GB', { 
        hour: '2-digit', 
        minute: '2-digit' 
      });

      const dischargeEntry = `[${formattedTime}] DISCHARGE - ${notes}`;
      
      const updatedAdmission = {
        ...admission,
        InBayNow: false,
        DischargeTime: dischargeTime,
        status: 'discharged',
        AdmissionNotes: admission.AdmissionNotes 
          ? `${admission.AdmissionNotes}\n${dischargeEntry}`
          : dischargeEntry,
        AdditionalNotes: [
          ...(admission.AdditionalNotes || []),
          {
            timestamp: dischargeTime,
            note: `Discharge Notes: ${notes}`,
            author: 'Staff'
          }
        ],
        History: [
          ...(admission.History || []),
          {
            timestamp: dischargeTime,
            action: 'Discharged',
            details: notes,
            author: 'Staff'
          }
        ]
      };
      await updateAdmission(updatedAdmission);
      await refreshAdmissions();
    }
  };

  const handleEdit = (admission: WelfareAdmission) => {
    navigate(`/edit-admission/${admission._id}`);
  };

  // Run data consistency fix on component mount
  useEffect(() => {
    const fixData = async () => {
      try {
        await databaseService.fixInconsistentAdmissions();
        await refreshAdmissions();
      } catch (error) {
        console.error('Error fixing inconsistent admissions:', error);
      }
    };
    fixData();
  }, [refreshAdmissions]);

  return (
    <AdmissionList 
      admissions={admissions} 
      loading={loading} 
      error={error} 
      onDischarge={handleDischarge}
      onEdit={handleEdit}
    />
  );
};

export default AdmissionsPage;
