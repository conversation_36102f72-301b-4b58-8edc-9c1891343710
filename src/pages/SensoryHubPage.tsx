import React, { useState, useEffect, useCallback } from 'react';
import {
  Container,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Paper,
  Grid
} from '@mui/material';
import { useFestival } from '../contexts/FestivalContext';
import { useSiteLocation } from '../contexts/SiteLocationContext';
import { SensoryHubVisitForm } from '../components/sensory-hub/SensoryHubVisitForm';
import { databaseService } from '../services/database/index';
import { SensoryHubVisit } from '../types/sensory-hub';

export const SensoryHubPage: React.FC = () => {
  const { activeFestival } = useFestival();
  const { activeSiteLocation } = useSiteLocation();
  
  // State for managing visits data
  const [visits, setVisits] = useState<SensoryHubVisit[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Load visits data
  const loadVisits = useCallback(async () => {
    if (!activeFestival) {
      setVisits([]);
      setIsLoading(false);
      return;
    }

    try {
      setIsLoading(true);
      setError(null);
      
      // Wait for database initialization
      await databaseService.waitForInitialization();
      
      console.log('Loading sensory hub visits for festival:', activeFestival._id);
      
      let visitsData: SensoryHubVisit[];
      
      if (activeSiteLocation) {
        // Load visits for specific location
        visitsData = await databaseService.getSensoryHubVisitsByLocation(activeSiteLocation.id);
      } else {
        // Load all visits for festival
        visitsData = await databaseService.getSensoryHubVisitsByFestival(activeFestival._id);
      }
      
      console.log('Loaded sensory hub visits:', visitsData.length);
      setVisits(visitsData);
      
    } catch (error) {
      console.error('Error loading sensory hub visits:', error);
      setError('Failed to load visits data');
    } finally {
      setIsLoading(false);
    }
  }, [activeFestival, activeSiteLocation]);

  // Load visits when festival or location changes
  useEffect(() => {
    loadVisits();
  }, [loadVisits]);

  // Handle successful visit submission
  const handleVisitSuccess = useCallback(() => {
    // Reload visits data after successful submission
    loadVisits();
  }, [loadVisits]);

  // Get page title based on location
  const getPageTitle = () => {
    const baseTitle = 'Sensory Hub Visitor Tracking';
    if (activeSiteLocation) {
      const locationName = activeSiteLocation.type === 'arena' ? 'Arena' : 'Campsite';
      return `${baseTitle} - ${locationName}`;
    }
    return baseTitle;
  };

  // Get today's visit statistics
  const getTodayStats = useCallback(() => {
    const today = new Date().toDateString();
    const todayVisits = visits.filter(visit => {
      const visitDate = new Date(visit.visitTimestamp).toDateString();
      return visitDate === today;
    });

    const lookAroundCount = todayVisits.filter(v => v.purpose === 'look_around').length;
    const useServiceCount = todayVisits.filter(v => v.purpose === 'use_service').length;
    const crewCount = todayVisits.filter(v => v.userType === 'crew').length;
    const publicCount = todayVisits.filter(v => v.userType === 'public').length;

    return {
      total: todayVisits.length,
      lookAround: lookAroundCount,
      useService: useServiceCount,
      crew: crewCount,
      public: publicCount
    };
  }, [visits]);

  const todayStats = getTodayStats();

  if (!activeFestival) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="warning">
          Please select an active festival to access sensory hub visitor tracking.
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Page Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" sx={{ fontWeight: 'bold', color: 'text.primary', mb: 1 }}>
          {getPageTitle()}
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Record and track visitor interactions with the sensory hub
        </Typography>
      </Box>

      {/* Error Alert */}
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Visit Form */}
        <Grid size={{ xs: 12, lg: 8 }}>
          <SensoryHubVisitForm onSuccess={handleVisitSuccess} />
        </Grid>

        {/* Today's Statistics */}
        <Grid size={{ xs: 12, lg: 4 }}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold', color: 'text.primary' }}>
              Today's Visits
            </Typography>
            
            {isLoading ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 2 }}>
                <CircularProgress size={24} />
              </Box>
            ) : (
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                {/* Total Visits */}
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                  <Typography variant="body1" fontWeight="medium">
                    Total Visits:
                  </Typography>
                  <Typography variant="h6" color="primary" fontWeight="bold">
                    {todayStats.total}
                  </Typography>
                </Box>

                {/* Purpose Breakdown */}
                <Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    By Purpose:
                  </Typography>
                  <Box sx={{ pl: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Look Around:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {todayStats.lookAround}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Use Service:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {todayStats.useService}
                      </Typography>
                    </Box>
                  </Box>
                </Box>

                {/* User Type Breakdown */}
                <Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                    By User Type:
                  </Typography>
                  <Box sx={{ pl: 2 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Public:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {todayStats.public}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                      <Typography variant="body2">Crew:</Typography>
                      <Typography variant="body2" fontWeight="medium">
                        {todayStats.crew}
                      </Typography>
                    </Box>
                  </Box>
                </Box>

                {/* Festival Info */}
                <Box sx={{ pt: 2, borderTop: 1, borderColor: 'divider' }}>
                  <Typography variant="body2" color="text.secondary">
                    <strong>Festival:</strong> {activeFestival.name}
                  </Typography>
                  {activeSiteLocation && (
                    <Typography variant="body2" color="text.secondary">
                      <strong>Location:</strong> {activeSiteLocation.type === 'arena' ? 'Arena' : 'Campsite'}
                    </Typography>
                  )}
                </Box>
              </Box>
            )}
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};