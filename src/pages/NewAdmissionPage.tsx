import React, { useEffect, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { Box, Typography, CircularProgress } from '@mui/material';
import { AdmissionForm } from '../components/AdmissionForm';
import { useAdmissions } from '../hooks/useAdmissions';
import { NewWelfareAdmission, WelfareAdmission } from '../types/admission';
import { useFestival } from '../contexts/FestivalContext';
import { AdmissionFormData, createInitialFormData } from '../types/forms';
import { databaseService } from '../services/database';

export const NewAdmissionPage: React.FC = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { addAdmission, updateAdmission, admissions, refreshAdmissions } = useAdmissions();
  const { activeFestival } = useFestival();
  const [loading, setLoading] = useState(false);
  const [initialFormData, setInitialFormData] = useState<AdmissionFormData | null>(null);

  // Load existing admission data when in edit mode
  useEffect(() => {
    const loadAdmissionData = async () => {
      if (id) {
        setLoading(true);
        
        try {
          // First try to find the admission in the current admissions array
          let existingAdmission: WelfareAdmission | null = admissions.find(a => a._id === id) || null;
          
          // If not found, try to refresh admissions and check again
          if (!existingAdmission) {
            await refreshAdmissions();
            existingAdmission = admissions.find(a => a._id === id) || null;
          }
          
          // If still not found, try to get it directly from the database
          if (!existingAdmission) {
            try {
              // Get the admission directly from the database using our new method
              existingAdmission = await databaseService.getAdmissionById(id);
            } catch (err) {
              console.error('Error fetching admission directly:', err);
            }
          }
          
          if (existingAdmission) {
            // Convert WelfareAdmission to AdmissionFormData
            setInitialFormData(existingAdmission as unknown as AdmissionFormData);
          } else {
            // If admission not found, use empty form
            setInitialFormData(createInitialFormData());
          }
        } catch (error) {
          console.error('Error loading admission data:', error);
          setInitialFormData(createInitialFormData());
        } finally {
          setLoading(false);
        }
      } else {
        // For new admissions, use empty form
        setInitialFormData(createInitialFormData());
      }
    };
    
    loadAdmissionData();
  }, [id, admissions, refreshAdmissions]);

  const handleSubmit = async (formData: NewWelfareAdmission) => {
    try {
      if (id) {
        const existingAdmission = admissions.find(a => a._id === id);
        if (existingAdmission) {
          await updateAdmission({
            ...existingAdmission,
            ...formData
          });
        }
      } else {
        await addAdmission(formData);
      }
      navigate('/admissions');
    } catch (error) {
      console.error('Error saving admission:', error);
    }
  };

  if (loading || !initialFormData) {
    return (
      <Box sx={{ p: 2, display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 2 }}>
      <Typography variant="h4" sx={{ fontWeight: 'bold', mb: 2 }}>
        {id ? 'Edit Admission' : 'New Admission'}
      </Typography>
      <AdmissionForm
        onSubmit={handleSubmit}
        activeFestival={activeFestival}
        initialData={initialFormData}
      />
    </Box>
  );
};

export default NewAdmissionPage;
