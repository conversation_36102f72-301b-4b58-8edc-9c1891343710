import React, { useState } from 'react';
import { Box, Typography, Container, Paper, Divider, Fade } from '@mui/material';
import Grid from '@mui/material/Grid';
import { UserGuideTabSelector, UserGuideTab } from '../components/userguide/UserGuideTabSelector';
import { UserGuideImage } from '../components/userguide/UserGuideImage';

export const UserGuidePageSimple: React.FC = () => {
  const [activeTab, setActiveTab] = useState<UserGuideTab>('dashboard');

  // Map tab keys to display titles
  const tabTitles: Record<UserGuideTab, string> = {
    'getting-started': 'Getting Started',
    'dashboard': 'Dashboard',
    'admissions': 'Admissions',
    'front-of-house': 'Front of House',
    'shift-management': 'Shift Management',
    'lost-property': 'Lost Property',
    'knowledge-base': 'Knowledge Base',
    'reports': 'Reports',
    'feedback': 'Feedback',
    'access-management': 'Access Management',
    'data-management': 'Data Management',
    'technical-support': 'Technical Support',
    'faq': 'FAQ'
  };

  // Render content based on active tab
  const renderContent = () => {
    switch (activeTab) {
      case 'getting-started':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Getting Started
            </Typography>
            <Typography variant="body1" paragraph>
              To access the iThink Welfare system, you'll need to:
            </Typography>
            <ul>
              <li>Navigate to https://ithink-welfare.brisflix.workers.dev/</li>
              <li>You'll be presented with a Cloudflare login page</li>
              <li>Enter your email address when prompted</li>
              <li>Complete the authentication process</li>
            </ul>
            <Typography variant="body1" paragraph>
              <strong>Note:</strong> This secure login process ensures that only authorized personnel can access the system. Access to specific features is controlled by your assigned role (Admin, Partner, User, or Public).
            </Typography>
            <UserGuideImage imageName="getting-started-1" altText="Active Festival selector in sidebar" />
          </>
        );
      
      case 'dashboard':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Dashboard
            </Typography>
            <Typography variant="body1" paragraph>
              The Dashboard is your central hub for monitoring welfare activities. Here you can:
            </Typography>
            <ul>
              <li>View current festival status and key statistics</li>
              <li>Access quick links to common tasks</li>
              <li>Monitor recent admissions and activities</li>
              <li>See current bay/chair occupancy status</li>
            </ul>
            <UserGuideImage imageName="dashboard-1" altText="Dashboard overview" />
          </>
        );
      
      case 'admissions':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Admissions
            </Typography>
            <Typography variant="body1" paragraph>
              The Admissions page shows all welfare admissions. You can:
            </Typography>
            <ul>
              <li>View all current and past admissions</li>
              <li>Filter admissions by date, status, or other criteria</li>
              <li>Access detailed information about each admission</li>
              <li>Update admission status and add notes</li>
              <li>Monitor bay/chair assignments</li>
            </ul>
            <UserGuideImage imageName="admissions-1" altText="Admissions list" />
            
            <Typography variant="h6" gutterBottom color="primary.dark" fontWeight="500" sx={{ mt: 4 }}>
              Creating a New Admission
            </Typography>
            <Typography variant="body1" paragraph>
              To create a new admission:
            </Typography>
            <ol>
              <li>Click the "New Admission" button</li>
              <li>Fill out the personal information</li>
              <li>Complete all relevant sections</li>
              <li>Add any additional notes</li>
              <li>Save the admission to create the record</li>
            </ol>
            <UserGuideImage imageName="admissions-2" altText="New admission form" />
          </>
        );
      
      case 'front-of-house':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Front of House
            </Typography>
            <Typography variant="body1" paragraph>
              The Front of House page helps track welfare items given to festival attendees:
            </Typography>
            <ul>
              <li>Record items provided to visitors</li>
              <li>Track inventory of essential supplies</li>
              <li>Monitor usage patterns</li>
              <li>View low stock warnings</li>
            </ul>
            <UserGuideImage imageName="front-of-house-1" altText="Front of house interface" />
            
            <Typography variant="h6" gutterBottom color="primary.dark" fontWeight="500" sx={{ mt: 4 }}>
              Item Distribution
            </Typography>
            <Typography variant="body1" paragraph>
              There are two ways to record distributed items:
            </Typography>
            <ol>
              <li><strong>Single Click</strong>: Click once on an item button to add one of that item</li>
              <li><strong>Long Press</strong>: Press and hold an item button to enter a specific quantity
                <ul>
                  <li>Especially useful for distributing multiple items at once</li>
                  <li>Optimized for iPad and touch devices</li>
                </ul>
              </li>
            </ol>
          </>
        );
      
      case 'shift-management':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Shift Management ** Work in Progress **
            </Typography>
            <Typography variant="body1" paragraph>
              Manage staff scheduling through:
            </Typography>
            <ul>
              <li>Viewing current shift patterns</li>
              <li>Checking team leader assignments</li>
              <li>Monitoring shift notes</li>
              <li>Viewing team member schedules</li>
              <li>Following the rotation pattern (A → B → C → D → E)</li>
            </ul>
          </>
        );
      
      case 'lost-property':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Lost Property
            </Typography>
            <Typography variant="body1" paragraph>
              Track and manage lost property items:
            </Typography>
            <ul>
              <li>Log new found items with detailed descriptions</li>
              <li>Categorize items (Phone, Passport, Keys, etc.)</li>
              <li>Add photos of found items when possible</li>
              <li>Record claimed items and return information</li>
              <li>Search existing lost property using the search box</li>
              <li>Generate lost property reports</li>
            </ul>
            <UserGuideImage imageName="lost-property-1" altText="Lost property logging form" />
            
            <Typography variant="h6" gutterBottom color="primary.dark" fontWeight="500" sx={{ mt: 4 }}>
              Managing Returns
            </Typography>
            <Typography variant="body1" paragraph>
              When an item is claimed:
            </Typography>
            <ol>
              <li>Find the item in the list</li>
              <li>Click "Mark as Returned"</li>
              <li>Enter the return details</li>
            </ol>
            
            <Typography variant="body1" paragraph>
              If an item was accidentally marked as returned:
            </Typography>
            <ol>
              <li>Find the item in the returned items list</li>
              <li>Click "Unmark as Returned"</li>
              <li>The item will be moved back to the active list</li>
            </ol>
          </>
        );
      
      case 'knowledge-base':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Knowledge Base
            </Typography>
            <Typography variant="body1" paragraph>
              The Knowledge Base provides quick access to important external resources:
            </Typography>
            <ul>
              <li>Browse resources organized by category and subcategory</li>
              <li>Access substance information, mental health resources, and support contacts</li>
              <li>View phone numbers and website links for external services</li>
              <li>Resources may be specific to the current festival or available across all festivals</li>
              <li>Search for specific resources using the search function</li>
            </ul>
            <UserGuideImage imageName="knowledge-base-1" altText="Knowledge Base tile view" />
            
            <Typography variant="h6" gutterBottom color="primary.dark" fontWeight="500" sx={{ mt: 4 }}>
              Adding Knowledge Base Resources
            </Typography>
            <Typography variant="body1" paragraph>
              If you have admin access, you can add new resources:
            </Typography>
            <ol>
              <li>Click the "Add Resource" button</li>
              <li>Enter the resource details:
                <ul>
                  <li>Title and description</li>
                  <li>URL and/or phone number</li>
                  <li>Category and subcategory</li>
                  <li>Specify if the resource should be available for all festivals</li>
                </ul>
              </li>
              <li>Save the resource to make it available to all users</li>
            </ol>
          </>
        );
      
      case 'reports':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Reports
            </Typography>
            <Typography variant="body1" paragraph>
              Generate comprehensive analytics through:
            </Typography>
            <ul>
              <li>Admissions Reports
                <ul>
                  <li>View admission trends</li>
                  <li>Track substance use patterns</li>
                  <li>Monitor safeguarding incidents</li>
                </ul>
              </li>
              <li>Front of House Reports
                <ul>
                  <li>Item distribution statistics</li>
                  <li>Stock level tracking</li>
                  <li>Usage patterns</li>
                </ul>
              </li>
              <li>Lost Property Reports
                <ul>
                  <li>Items found/claimed statistics</li>
                  <li>Category breakdowns</li>
                  <li>Time-based analysis</li>
                </ul>
              </li>
            </ul>
            <UserGuideImage imageName="reports-1" altText="Reports dashboard" />
            
            <Typography variant="h6" gutterBottom color="primary.dark" fontWeight="500" sx={{ mt: 4 }}>
              Generating Reports
            </Typography>
            <Typography variant="body1" paragraph>
              To create a report:
            </Typography>
            <ol>
              <li>Select the report type</li>
              <li>Choose the date range</li>
              <li>Select specific data points to include</li>
              <li>Generate the report in your preferred format (PDF available)</li>
            </ol>
            
            <Typography variant="h6" gutterBottom color="primary.dark" fontWeight="500" sx={{ mt: 4 }}>
              Interactive Reports
            </Typography>
            <Typography variant="body1" paragraph>
              The reports include interactive elements:
            </Typography>
            <ul>
              <li>Click on any bar in the admissions chart to see patients admitted during that time period</li>
              <li>Click on any row in the tables to view detailed information about that record</li>
              <li>Use the search and filter functions to find specific records</li>
              <li>Sort columns by clicking on column headers</li>
            </ul>
          </>
        );
      
      case 'feedback':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Feedback
            </Typography>
            <Typography variant="body1" paragraph>
              Help improve the system:
            </Typography>
            <ul>
              <li>Click the feedback button in the bottom-right corner</li>
              <li>Rate your experience</li>
              <li>Provide detailed comments</li>
              <li>Report any issues encountered</li>
              <li>Track the status of your submitted feedback</li>
            </ul>
            <UserGuideImage imageName="feedback-1" altText="Feedback form" />
          </>
        );
      
      case 'access-management':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Access Management
            </Typography>
            <Typography variant="body1" paragraph>
              For administrators, the Access Management page provides control over user permissions:
            </Typography>
            <ul>
              <li>View all users who have accessed the system</li>
              <li>Assign roles to users (Admin, Partner, User, Public)</li>
              <li>Set required access levels for different features</li>
              <li>Monitor user activity and access patterns</li>
            </ul>
            
            <Typography variant="h6" gutterBottom color="primary.dark" fontWeight="500" sx={{ mt: 4 }}>
              User Roles
            </Typography>
            <Typography variant="body1" paragraph>
              The system uses a role-based access control system:
            </Typography>
            <ul>
              <li><strong>Admin</strong>: Full access to all features and management functions</li>
              <li><strong>Partner</strong>: Access to most features except system administration</li>
              <li><strong>User</strong>: Standard access to core welfare functions</li>
              <li><strong>Public</strong>: Limited access to basic information only</li>
            </ul>
          </>
        );
      
      case 'data-management':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Data Management
            </Typography>
            <Typography variant="body1" paragraph>
              Important notes about data handling:
            </Typography>
            <ul>
              <li>All data is automatically synchronized between devices</li>
              <li>The system works offline with automatic sync when online</li>
              <li>Live sync with WebSocket support ensures real-time updates</li>
              <li>Improved conflict resolution for simultaneous edits</li>
              <li>Records are retained for 3 months</li>
              <li>Regular backups ensure data safety</li>
              <li>Export functionality available for data preservation</li>
            </ul>
            
            <Typography variant="h6" gutterBottom color="primary.dark" fontWeight="500" sx={{ mt: 4 }}>
              Festival-Specific Data
            </Typography>
            <Typography variant="body1" paragraph>
              How data is organized by festival:
            </Typography>
            <ul>
              <li>Each festival maintains its own separate data</li>
              <li>Some resources (like Knowledge Base items) can be shared across festivals</li>
              <li>Your active festival selection is browser-specific, allowing different users to work with different festivals simultaneously</li>
            </ul>
          </>
        );
      
      case 'technical-support':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Technical Support
            </Typography>
            <Typography variant="body1" paragraph>
              If you encounter any issues:
            </Typography>
            <ol>
              <li>Check your internet connection</li>
              <li>Try refreshing the page</li>
              <li>Use the feedback button to report specific problems</li>
              <li>Contact your system administrator if problems persist</li>
            </ol>
            <Typography variant="body1" paragraph sx={{ mt: 3 }}>
              <strong>Note:</strong> Screenshots will be updated regularly to reflect the latest interface changes. For technical support or questions, please contact your system administrator.
            </Typography>
          </>
        );

      case 'faq':
        return (
          <>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              Frequently Asked Questions (FAQ)
            </Typography>

            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Q: What is the iThinc Welfare Management System?</Typography>
            <Typography variant="body1" paragraph>
              A: It's a web application designed for managing welfare services at festivals and events, covering admissions, shifts, inventory, lost property, and reporting across multiple sites if needed.
            </Typography>

            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Q: Can I use the system offline?</Typography>
            <Typography variant="body1" paragraph>
              A: Yes, the system is designed with offline-first capability. It uses local storage (PouchDB) allowing you to continue working without an internet connection. Data syncs automatically with the central database (CouchDB) when you're back online.
            </Typography>

            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Q: How does the system handle multiple sites at a festival (e.g., Arena and Campsite)?</Typography>
            <Typography variant="body1" paragraph>
              A: The system supports multi-site management. You can easily switch between configured sites (like Arena or Campsite) using the selector in the festival header. Data for admissions, inventory, and shifts is kept separate for each site, ensuring accurate tracking.
            </Typography>

            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Q: How do I add a new festival or manage existing ones?</Typography>
            <Typography variant="body1" paragraph>
              A: Navigate to the Festival Management page. Here you can create new festivals, edit details of existing ones (name, dates, location), configure sites (Arena, Campsite), and set the active festival.
            </Typography>

            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Q: How is patient data recorded in Admissions?</Typography>
            <Typography variant="body1" paragraph>
              A: The Admission form is comprehensive and modular. It includes sections for Personal Information, Location within the festival, Physical Description, Substance Use details, Safeguarding concerns, Referral information, and general Admission Notes. Fill in the relevant sections to create a detailed record.
            </Typography>

            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Q: Can I track inventory usage per site?</Typography>
            <Typography variant="body1" paragraph>
              A: Yes, inventory management is site-specific. When you record item usage (e.g., handing out water, ponchos, first aid supplies), it's tracked against the currently selected site (Arena or Campsite).
            </Typography>

            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Q: How does shift scheduling work?</Typography>
            <Typography variant="body1" paragraph>
              A: Administrators can configure shift patterns and assign team leaders via the Shifts page. The system helps generate schedules, often following a predefined rotation pattern. Shift notes specific to each site can also be recorded.
            </Typography>

            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Q: What happens if two users edit the same record while offline?</Typography>
            <Typography variant="body1" paragraph>
              A: The system uses PouchDB and CouchDB, which include mechanisms for conflict resolution. When data syncs, the system attempts to merge changes intelligently to maintain data integrity.
            </Typography>

            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Q: How do I find a specific lost property item?</Typography>
            <Typography variant="body1" paragraph>
              A: Go to the Lost Property page. You can use the quick search bar for general searches or utilize the advanced filtering options (filter by category, status, date found, description keywords, etc.) to locate specific items efficiently.
            </Typography>

            <Typography variant="h6" gutterBottom sx={{ mt: 2 }}>Q: How can I provide feedback about the system?</Typography>
            <Typography variant="body1" paragraph>
              A: A feedback button is available on most pages. Clicking this button opens a form where you can submit comments, suggestions, or report any issues you encounter. This feedback is reviewed by the administrators.
            </Typography>
          </>
        );

      default:
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Paper 
        elevation={0}
        sx={{ 
          p: 3, 
          mb: 4, 
          bgcolor: 'background.paper',
          borderRadius: 2
        }}
      >
        <Grid container spacing={2} alignItems="center">
          <Grid size={{ xs: 12 }}>
            <Typography variant="h4" component="h1" gutterBottom color="primary">
              iThink Welfare System User Guide
            </Typography>
            <Typography variant="body1" color="text.secondary" paragraph>
              This guide provides comprehensive information on using the iThink Welfare System.
              Select a section from the tabs below to learn more about specific features.
            </Typography>
          </Grid>
        </Grid>
      </Paper>

      <UserGuideTabSelector activeTab={activeTab} onTabChange={setActiveTab} />

      <Fade in={true} timeout={500}>
        <Paper sx={{ p: 4, mb: 3, borderRadius: 2 }}>
          {renderContent()}
        </Paper>
      </Fade>
    </Container>
  );
};