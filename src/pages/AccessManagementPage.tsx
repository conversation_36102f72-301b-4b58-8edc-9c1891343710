import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Button,
  Dialog,
  DialogActions,
  DialogContent,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Snackbar,
  Alert,
  CircularProgress,
  SelectChangeEvent
} from '@mui/material';
import { Delete as DeleteIcon, Edit as EditIcon } from '@mui/icons-material';
import { databaseService } from '../services/database';
import { AccessRole, PageAccess, UserRole } from '../services/database/access-manager';
import { useAuth } from '../contexts/AuthContext';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`access-tabpanel-${index}`}
      aria-labelledby={`access-tab-${index}`}
      {...other}
    >
      {value === index && <Box sx={{ p: 3 }}>{children}</Box>}
    </div>
  );
};

// Define available pages for access control
const availablePages = [
  { id: 'dashboard', name: 'Dashboard' },
  { id: 'admissions', name: 'Admissions' },
  { id: 'new-admission', name: 'New Admission' },
  { id: 'front-of-house', name: 'Front of House' },
  { id: 'lost-property', name: 'Lost Property' },
  { id: 'sensory-hub', name: 'Sensory Hub' },
  { id: 'reports', name: 'Reports' },
  { id: 'shifts', name: 'Shifts' },
  { id: 'knowledge-base-view', name: 'Knowledge Base (View)' },
  { id: 'knowledge-base-edit', name: 'Knowledge Base (Edit)' },
  { id: 'festival-management', name: 'Festival Management' },
  { id: 'feedback-management', name: 'Feedback Management' },
  { id: 'access-management', name: 'Access Management' },
];

const AccessManagementPage: React.FC = () => {
  const { isAdmin } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [pageAccess, setPageAccess] = useState<PageAccess[]>([]);
  const [userRoles, setUserRoles] = useState<UserRole[]>([]);
  const [loading, setLoading] = useState(true);
  const [openPageDialog, setOpenPageDialog] = useState(false);
  const [openUserDialog, setOpenUserDialog] = useState(false);
  const [selectedPage, setSelectedPage] = useState<PageAccess | null>(null);
  const [selectedUser, setSelectedUser] = useState<UserRole | null>(null);
  const [newPageId, setNewPageId] = useState('');
  const [newPageName, setNewPageName] = useState('');
  const [newPageRole, setNewPageRole] = useState<AccessRole>(AccessRole.USER);
  const [newUserEmail, setNewUserEmail] = useState('');
  const [newUserRole, setNewUserRole] = useState<AccessRole>(AccessRole.USER);
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState<'success' | 'error'>('success');

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    setLoading(true);
    try {
      const pages = await databaseService.getAllPageAccess();
      const users = await databaseService.getAllUserRoles();
      
      setPageAccess(pages);
      setUserRoles(users);
    } catch (error) {
      console.error('Error loading access data:', error);
      showSnackbar('Error loading data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (_: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleOpenPageDialog = (page?: PageAccess) => {
    if (page) {
      setSelectedPage(page);
      setNewPageId(page.pageId);
      setNewPageName(page.pageName);
      setNewPageRole(page.requiredRole);
    } else {
      setSelectedPage(null);
      setNewPageId('');
      setNewPageName('');
      setNewPageRole(AccessRole.USER);
    }
    setOpenPageDialog(true);
  };

  const handleOpenUserDialog = (user?: UserRole) => {
    if (user) {
      setSelectedUser(user);
      setNewUserEmail(user.email);
      setNewUserRole(user.role);
    } else {
      setSelectedUser(null);
      setNewUserEmail('');
      setNewUserRole(AccessRole.USER);
    }
    setOpenUserDialog(true);
  };

  const handleClosePageDialog = () => {
    setOpenPageDialog(false);
  };

  const handleCloseUserDialog = () => {
    setOpenUserDialog(false);
  };

  const handlePageSelect = (event: SelectChangeEvent) => {
    const pageId = event.target.value;
    const page = availablePages.find(p => p.id === pageId);
    if (page) {
      setNewPageId(page.id);
      setNewPageName(page.name);
    }
  };

  const handleSavePage = async () => {
    try {
      await databaseService.setPageAccess(newPageId, newPageName, newPageRole);
      handleClosePageDialog();
      showSnackbar('Page access saved successfully', 'success');
      loadData();
    } catch (error) {
      console.error('Error saving page access:', error);
      showSnackbar('Error saving page access', 'error');
    }
  };

  const handleSaveUser = async () => {
    try {
      await databaseService.setUserRole(newUserEmail, newUserRole);
      handleCloseUserDialog();
      showSnackbar('User role saved successfully', 'success');
      loadData();
    } catch (error) {
      console.error('Error saving user role:', error);
      showSnackbar('Error saving user role', 'error');
    }
  };

  const handleDeleteUser = async (email: string) => {
    try {
      await databaseService.deleteUserRole(email);
      showSnackbar('User role deleted successfully', 'success');
      loadData();
    } catch (error) {
      console.error('Error deleting user role:', error);
      showSnackbar('Error deleting user role', 'error');
    }
  };

  const showSnackbar = (message: string, severity: 'success' | 'error') => {
    setSnackbarMessage(message);
    setSnackbarSeverity(severity);
    setSnackbarOpen(true);
  };

  const handleCloseSnackbar = () => {
    setSnackbarOpen(false);
  };

  if (!isAdmin) {
    return (
      <Box sx={{ p: 3 }}>
        <Typography variant="h4">Access Denied</Typography>
        <Typography variant="body1">
          You do not have permission to access this page.
        </Typography>
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Access Management
      </Typography>
      
      <Paper sx={{ width: '100%', mb: 2 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          indicatorColor="primary"
          textColor="primary"
          centered
        >
          <Tab label="Page Access" />
          <Tab label="User Roles" />
        </Tabs>
        
        {loading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress />
          </Box>
        ) : (
          <>
            <TabPanel value={tabValue} index={0}>
              <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={() => handleOpenPageDialog()}
                >
                  Add Page Access
                </Button>
              </Box>
              
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Page ID</TableCell>
                      <TableCell>Page Name</TableCell>
                      <TableCell>Required Role</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {pageAccess.map((page) => (
                      <TableRow key={page.pageId}>
                        <TableCell>{page.pageId}</TableCell>
                        <TableCell>{page.pageName}</TableCell>
                        <TableCell>{page.requiredRole}</TableCell>
                        <TableCell>
                          <IconButton 
                            color="primary" 
                            onClick={() => handleOpenPageDialog(page)}
                          >
                            <EditIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                    {pageAccess.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={4} align="center">
                          No page access settings found
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>
            
            <TabPanel value={tabValue} index={1}>
              <Box sx={{ mb: 2, display: 'flex', justifyContent: 'flex-end' }}>
                <Button 
                  variant="contained" 
                  color="primary" 
                  onClick={() => handleOpenUserDialog()}
                >
                  Add User Role
                </Button>
              </Box>
              
              <TableContainer>
                <Table>
                  <TableHead>
                    <TableRow>
                      <TableCell>Email</TableCell>
                      <TableCell>Role</TableCell>
                      <TableCell>Actions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {userRoles.map((user) => (
                      <TableRow key={user.email}>
                        <TableCell>{user.email}</TableCell>
                        <TableCell>{user.role}</TableCell>
                        <TableCell>
                          <IconButton 
                            color="primary" 
                            onClick={() => handleOpenUserDialog(user)}
                          >
                            <EditIcon />
                          </IconButton>
                          <IconButton 
                            color="error" 
                            onClick={() => handleDeleteUser(user.email)}
                          >
                            <DeleteIcon />
                          </IconButton>
                        </TableCell>
                      </TableRow>
                    ))}
                    {userRoles.length === 0 && (
                      <TableRow>
                        <TableCell colSpan={3} align="center">
                          No user roles found
                        </TableCell>
                      </TableRow>
                    )}
                  </TableBody>
                </Table>
              </TableContainer>
            </TabPanel>
          </>
        )}
      </Paper>
      
      {/* Page Access Dialog */}
      <Dialog open={openPageDialog} onClose={handleClosePageDialog}>
        <DialogTitle>
          {selectedPage ? 'Edit Page Access' : 'Add Page Access'}
        </DialogTitle>
        <DialogContent>
          <FormControl fullWidth margin="normal" disabled={!!selectedPage}>
            <InputLabel id="page-select-label">Page</InputLabel>
            <Select
              labelId="page-select-label"
              value={newPageId}
              onChange={handlePageSelect}
              label="Page"
            >
              {availablePages.map((page) => (
                <MenuItem key={page.id} value={page.id}>
                  {page.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <TextField
            margin="normal"
            label="Page Name"
            fullWidth
            value={newPageName}
            onChange={(e) => setNewPageName(e.target.value)}
            disabled={!!selectedPage}
          />
          
          <FormControl fullWidth margin="normal">
            <InputLabel id="role-select-label">Required Role</InputLabel>
            <Select
              labelId="role-select-label"
              value={newPageRole}
              onChange={(e) => setNewPageRole(e.target.value as AccessRole)}
              label="Required Role"
            >
              <MenuItem value={AccessRole.ADMIN}>Admin</MenuItem>
              <MenuItem value={AccessRole.PARTNER}>Partner</MenuItem>
              <MenuItem value={AccessRole.USER}>User</MenuItem>
              <MenuItem value={AccessRole.PUBLIC}>Public</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleClosePageDialog}>Cancel</Button>
          <Button onClick={handleSavePage} color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* User Role Dialog */}
      <Dialog open={openUserDialog} onClose={handleCloseUserDialog}>
        <DialogTitle>
          {selectedUser ? 'Edit User Role' : 'Add User Role'}
        </DialogTitle>
        <DialogContent>
          <TextField
            margin="normal"
            label="Email"
            fullWidth
            value={newUserEmail}
            onChange={(e) => setNewUserEmail(e.target.value)}
            disabled={!!selectedUser}
          />
          
          <FormControl fullWidth margin="normal">
            <InputLabel id="user-role-select-label">Role</InputLabel>
            <Select
              labelId="user-role-select-label"
              value={newUserRole}
              onChange={(e) => setNewUserRole(e.target.value as AccessRole)}
              label="Role"
            >
              <MenuItem value={AccessRole.ADMIN}>Admin</MenuItem>
              <MenuItem value={AccessRole.PARTNER}>Partner</MenuItem>
              <MenuItem value={AccessRole.USER}>User</MenuItem>
              <MenuItem value={AccessRole.PUBLIC}>Public</MenuItem>
            </Select>
          </FormControl>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseUserDialog}>Cancel</Button>
          <Button onClick={handleSaveUser} color="primary">
            Save
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Snackbar for notifications */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={handleCloseSnackbar}
      >
        <Alert 
          onClose={handleCloseSnackbar} 
          severity={snackbarSeverity}
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default AccessManagementPage;