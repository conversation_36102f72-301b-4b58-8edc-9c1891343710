import React, { useState, useEffect } from 'react';
import { Box, Typography, Container, Paper, Fade } from '@mui/material';
import Grid from '@mui/material/Grid';
import MenuBookIcon from '@mui/icons-material/MenuBook';
import { UserGuideTabSelector, UserGuideTab } from '../components/userguide/UserGuideTabSelector';
import { MarkdownContent } from '../components/userguide/MarkdownContent';
import { USER_GUIDE } from '../utils/userguide';

export const UserGuidePage: React.FC = () => {
  const [activeTab, setActiveTab] = useState<UserGuideTab>('getting-started');

  // Map tab keys to display titles
  const tabTitles: Record<UserGuideTab, string> = {
    'getting-started': 'Getting Started',
    'dashboard': 'Dashboard',
    'admissions': 'Admissions',
    'front-of-house': 'Front of House',
    'shift-management': 'Shift Management',
    'lost-property': 'Lost Property',
    'knowledge-base': 'Knowledge Base',
    'reports': 'Reports',
    'feedback': 'Feedback',
    'access-management': 'Access Management',
    'data-management': 'Data Management',
    'technical-support': 'Technical Support',
    'faq': 'FAQ'
  };

  const [fadeIn, setFadeIn] = useState(false);
  
  useEffect(() => {
    // Trigger fade-in animation after component mounts
    setFadeIn(true);
  }, []);
  
  useEffect(() => {
    // Scroll to top when tab changes
    window.scrollTo(0, 0);
  }, [activeTab]);

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      <Fade in={fadeIn} timeout={800}>
        <Paper
          elevation={2}
          sx={{
            p: 4,
            mb: 4,
            bgcolor: 'background.paper',
            borderRadius: 2,
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.08)'
          }}
        >
          <Grid container spacing={3} alignItems="center">
            <Grid size={{ xs: 12, md: 1 }}>
              <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                <MenuBookIcon sx={{ fontSize: 60, color: 'primary.main', opacity: 0.8 }} />
              </Box>
            </Grid>
            <Grid size={{ xs: 12, md: 11 }}>
              <Typography variant="h3" component="h1" gutterBottom color="primary" fontWeight="500">
                iThink Welfare System User Guide
              </Typography>
              <Typography variant="body1" color="text.secondary" paragraph>
                This guide provides comprehensive information on using the iThink Welfare System.
                Select a section from the tabs below to learn more about specific features.
              </Typography>
            </Grid>
          </Grid>
        </Paper>
      </Fade>

      <UserGuideTabSelector activeTab={activeTab} onTabChange={setActiveTab} />

      <Box sx={{ mt: 3 }}>
        {/* Display content based on active tab */}
        <MarkdownContent
          content={USER_GUIDE.sections[activeTab]}
          title={tabTitles[activeTab]}
        />
      </Box>
    </Container>
  );
};