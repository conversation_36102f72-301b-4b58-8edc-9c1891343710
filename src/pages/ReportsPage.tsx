import React, { useState } from 'react';
import { Box, Alert } from '@mui/material';
import { useFestival } from '../contexts/FestivalContext';
import { useReportData } from '../hooks/useReportData';
import { ReportLayout } from '../components/reports/shared/ReportLayout';
import { AdmissionsReport } from '../components/reports/admissions/AdmissionsReport';
import { FrontOfHouseReport } from '../components/reports/frontOfHouse/FrontOfHouseReport';
import { LostPropertyReport } from '../components/reports/lostProperty/LostPropertyReport';
import { SensoryHubReport } from '../components/reports/sensoryHub/SensoryHubReport';
import { ReportTab } from '../components/reports/shared/TabSelector';
import { BulkDeleteConfirmDialog } from '../components/reports/shared/BulkDeleteConfirmDialog';
import { databaseService } from '../services/database';
import { WelfareAdmission } from '../types/admission';
import { AdmissionFormData } from '../types/forms';
import { SensoryHubVisit } from '../types/sensory-hub';

export const ReportsPage: React.FC = () => {
  const { activeFestival } = useFestival();
  const [selectedDay, setSelectedDay] = useState<string>('all');
  const [activeTab, setActiveTab] = useState<ReportTab>('admissions');
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);

  const { data, loading, error } = useReportData(
    activeFestival?._id,
    selectedDay
  );

  if (!activeFestival) {
    return (
      <Box 
        display="flex" 
        justifyContent="center" 
        alignItems="center" 
        minHeight="200px"
      >
        <Alert severity="info">
          Please select a festival to view reports.
        </Alert>
      </Box>
    );
  }

  const handleSelectAll = (items: WelfareAdmission[] | any[]) => {
    if (!items) return; // Guard against undefined items
    if (selectedItems.length === items.length) {
      setSelectedItems([]);
    } else {
      setSelectedItems(items.map(item => item._id).filter(Boolean));
    }
  };

  const handleSelectItem = (id: string) => {
    setSelectedItems(prev => {
      if (prev.includes(id)) {
        return prev.filter(itemId => itemId !== id);
      } else {
        return [...prev, id];
      }
    });
  };

  const handleDelete = () => {
    if (selectedItems.length === 0) return;
    setShowDeleteDialog(true);
  };

  const handleConfirmDelete = async () => {
    if (selectedItems.length === 0) return;
    
    setIsDeleting(true);
    try {
      switch (activeTab) {
        case 'admissions':
          await databaseService.bulkDeleteAdmissions(selectedItems);
          break;
        case 'frontOfHouse':
          await databaseService.bulkDeleteItems(selectedItems);
          break;
        case 'lostProperty':
          await databaseService.bulkDeleteLostProperty(selectedItems);
          break;
        case 'sensoryHub':
          await databaseService.bulkDeleteVisits(selectedItems);
          break;
      }
      setSelectedItems([]);
      setShowDeleteDialog(false);
    } catch (err) {
      console.error('Error deleting items:', err);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setShowDeleteDialog(false);
  };

  const handleUpdateAdmission = async (admission: WelfareAdmission) => {
    try {
      // Convert WelfareAdmission to AdmissionFormData
      const formData: AdmissionFormData = {
        ...admission,
        type: 'admission',
        documentType: 'admission',
        syncStatus: 'sync_pending',
        AdditionalNotes: admission.AdditionalNotes || [],
        History: admission.History || [],
        timestamp: admission.timestamp || new Date().toISOString(),
        createdAt: admission.createdAt || admission.timestamp || new Date().toISOString(),
        updatedAt: new Date().toISOString()
      };
      await databaseService.updateAdmission(formData);
    } catch (err) {
      console.error('Error updating admission:', err);
    }
  };

  const renderActiveTab = () => {
    // Ensure data exists before rendering
    if (!data) return null;

    switch (activeTab) {
      case 'admissions':
        return (
          <AdmissionsReport
            admissions={data.admissions || []}
            selectedItems={selectedItems}
            onSelectAll={handleSelectAll}
            onSelectItem={handleSelectItem}
            onDelete={handleDelete}
            onUpdateAdmission={handleUpdateAdmission}
          />
        );
      case 'frontOfHouse':
        return (
          <FrontOfHouseReport
            itemCounts={data.itemCounts || []}
            selectedItems={selectedItems}
            onSelectAll={handleSelectAll}
            onSelectItem={handleSelectItem}
            onDelete={handleDelete}
          />
        );
      case 'lostProperty':
        return (
          <LostPropertyReport
            items={data.lostPropertyItems || []}
            selectedItems={selectedItems}
            onSelectAll={handleSelectAll}
            onSelectItem={handleSelectItem}
            onDelete={handleDelete}
          />
        );
      case 'sensoryHub':
        return (
          <SensoryHubReport
            visits={data.sensoryHubVisits || []}
            selectedItems={selectedItems}
            onSelectAll={handleSelectAll}
            onSelectItem={handleSelectItem}
            onDelete={handleDelete}
          />
        );
      default:
        return null;
    }
  };

  return (
    <>
      <ReportLayout
        festival={activeFestival}
        selectedDay={selectedDay}
        onDayChange={setSelectedDay}
        activeTab={activeTab}
        onTabChange={setActiveTab}
        loading={loading}
        error={error}
        data={data}
      >
        {renderActiveTab()}
      </ReportLayout>

      <BulkDeleteConfirmDialog
        open={showDeleteDialog}
        onConfirm={handleConfirmDelete}
        onClose={handleCancelDelete}
        itemCount={selectedItems.length}
        itemType={activeTab === 'admissions' ? 'admission' :
                  activeTab === 'frontOfHouse' ? 'item' :
                  activeTab === 'lostProperty' ? 'lost property item' : 'visit'}
        loading={isDeleting}
      />
    </>
  );
};
