import React, { useState, useEffect } from 'react';
import { Box, Typography, CircularProgress, Alert, Container, Paper } from '@mui/material';
import { KnowledgeBase } from '../components/knowledgebase/KnowledgeBase';
import { useFestival } from '../contexts/FestivalContext';
import MenuBookIcon from '@mui/icons-material/MenuBook';

export const KnowledgeBasePage: React.FC = () => {
  const { activeFestival } = useFestival();
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!activeFestival) {
      setError('Please select a festival first');
      setIsLoading(false);
      return;
    }
    setIsLoading(false);
  }, [activeFestival]);

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: 3 }}>
      <Paper
        elevation={0}
        sx={{
          p: 3,
          mb: 4,
          bgcolor: 'primary.light',
          color: 'primary.contrastText',
          borderRadius: 2
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          <MenuBookIcon fontSize="large" />
          <Box>
            <Typography variant="h4" sx={{ fontWeight: 'bold' }}>
              Knowledge Base
            </Typography>
            <Typography variant="subtitle1">
              Access important resources organized by category and subcategory
            </Typography>
          </Box>
        </Box>
      </Paper>
      
      {activeFestival && <KnowledgeBase />}
    </Container>
  );
};