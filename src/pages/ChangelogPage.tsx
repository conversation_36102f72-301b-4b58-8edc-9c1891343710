import React from 'react';
import { Box, Typography, Paper } from '@mui/material';
import { CHANGELOG } from '../utils/changelog';

export const ChangelogPage: React.FC = () => {
  return (
    <Box sx={{ maxWidth: 800, mx: 'auto', py: 4 }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Changelog
      </Typography>
      <Paper sx={{ p: 3 }}>
        <Typography component="pre" sx={{ 
          whiteSpace: 'pre-wrap',
          fontFamily: 'monospace',
          fontSize: '0.875rem',
          lineHeight: 1.5
        }}>
          {CHANGELOG}
        </Typography>
      </Paper>
    </Box>
  );
};