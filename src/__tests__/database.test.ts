import PouchDB from 'pouchdb-browser';
// Import the mocked service instance, not the class
import { databaseService } from '../services/database/index';
// Import types needed for test data
import { Festival, WelfareAdmission, ItemDocument, LostPropertyItem, ItemName } from '../types';

// Mock the database service
jest.mock('../services/database/index', () => ({
  databaseService: {
    addFestival: jest.fn(),
    getFestivals: jest.fn(),
    addAdmission: jest.fn(),
    getAdmissionsByFestival: jest.fn(), // Corrected method name
    addOrUpdateItemCount: jest.fn(), // Corrected method name
    getItemCountsByFestival: jest.fn(), // Corrected method name
    addLostPropertyItem: jest.fn(),
    getLostPropertyItems: jest.fn(),
    // Add other methods if needed by tests
  }
}));


describe('DatabaseService Tests', () => {
  // No need for real DB instance or service instantiation when mocking
  // let db: PouchDB.Database;

  beforeEach(() => {
    // Reset mocks before each test
    jest.clearAllMocks();
    // db = new PouchDB('test-db'); // Not needed
    // databaseService = new DatabaseService(); // Not needed
  });

  afterEach(async () => {
    // await db.destroy(); // Not needed
  });

  describe('Festival Management', () => {
    it('should add and retrieve festivals', async () => {
      // Add missing required fields for Festival type
      const festival: Festival = {
        _id: 'test-festival',
        _rev: '1-rev', // Add _rev for BaseDocument
        syncStatus: 'synced', // Add missing required property
        type: 'festival',
        documentType: 'festival',
        name: 'Test Festival',
        startDate: '2024-01-01',
        endDate: '2024-01-03',
        location: 'Test Location', // Kept for backward compatibility
        locations: [{ id: 'loc1', name: 'Main Stage', type: 'arena' }], // Add locations array
        hasMultipleLocations: true, // Add boolean flag
        isActive: true, // Add boolean flag
        showAdmissions: true,
        showFrontOfHouse: true,
        showLostProperty: true,
        showShifts: true,
      };

      // Mock the implementation for this test
      (databaseService.addFestival as jest.Mock).mockResolvedValue(festival);
      (databaseService.getFestivals as jest.Mock).mockResolvedValue([festival]);

      await databaseService.addFestival(festival);
      const festivals = await databaseService.getFestivals();

      expect(databaseService.addFestival).toHaveBeenCalledWith(festival);
      expect(databaseService.getFestivals).toHaveBeenCalled();
      expect(festivals).toHaveLength(1);
      // Use objectContaining because the mock might add/change properties
      expect(festivals[0]).toEqual(expect.objectContaining({ _id: 'test-festival', name: 'Test Festival' }));
    });
  });

  describe('Admission Management', () => {
    it('should add and retrieve admissions', async () => {
      // Add missing required fields and correct names/types for WelfareAdmission
      const admission: WelfareAdmission = {
        _id: 'test-admission',
        _rev: '1-rev', // Add _rev
        syncStatus: 'synced', // Add missing required property
        type: 'admission',
        documentType: 'admission',
        festivalId: 'test-festival',
        FirstName: 'Test', // Corrected field name
        Surname: 'Person', // Corrected field name
        Age: 25, // Corrected field name (optional)
        Gender: 'Male', // Corrected type/value
        Attended: new Date().toISOString(), // Corrected field name
        BaysOrChairs: 'Bay', // Add required field
        InBayNow: true, // Add required field
        ReferredBy: 'Self Referral', // Add required field
        ReasonCategory: 'Rest and Recuperation', // Corrected field name, valid value
        SubstanceUsed: ['Nothing'], // Add required field
        AdmissionNotes: 'Test notes', // Add required field
        AdditionalNotes: [], // Add required field
        History: [], // Add required field
        DOB: '', // Add required field
        Pronoun: '', // Add required field
        Ethnicity: 'Prefer not to say', // Add required field
        ContactName: '', // Add required field
        ContactNumber: '', // Add required field
        DischargeTime: '', // Add required field
        HairColour: '', // Add required field
        HairStyle: '', // Add required field
        ClothingTop: '', // Add required field
        ClothingBottom: '', // Add required field
        Footwear: '', // Add required field
        OtherFeatures: '', // Add required field
        status: 'active', // Add required field
      };

      // Mock implementation and call correct method
      (databaseService.addAdmission as jest.Mock).mockResolvedValue(admission);
      (databaseService.getAdmissionsByFestival as jest.Mock).mockResolvedValue([admission]);

      await databaseService.addAdmission(admission);
      // Call the correct method getAdmissionsByFestival
      const admissions = await databaseService.getAdmissionsByFestival('test-festival');

      expect(databaseService.addAdmission).toHaveBeenCalledWith(admission);
      expect(databaseService.getAdmissionsByFestival).toHaveBeenCalledWith('test-festival');
      expect(admissions).toHaveLength(1);
      expect(admissions[0]).toEqual(expect.objectContaining({ _id: 'test-admission', FirstName: 'Test' }));
    });
  });

  describe('Item Management', () => {
    it('should add/update and retrieve item counts', async () => {
      // Create a valid ItemDocument structure
      const itemDoc: ItemDocument = {
        _id: 'items_test-festival', // Example ID structure
        _rev: '1-rev',
        syncStatus: 'synced', // Add missing required property
        documentType: 'item',
        type: 'item',
        festivalId: 'test-festival',
        Suncream: 0,
        Poncho: 0,
        Water: 10, // Set the count for Water
        SanitaryProducts: 0,
        Earplugs: 0,
        Condoms: 0,
        ChildrensWristbands: 0,
        GeneralWristbands: 0,
        Charging: 0,
        Sanitizer: 0,
        ToiletRoll: 0,
        GeneralEnqs: 0,
        HotWater: 0,
        RestAndRecuperation: 0,
        Other: '',
      };

      // Mock implementation for corrected methods
      (databaseService.addOrUpdateItemCount as jest.Mock).mockResolvedValue(itemDoc);
      (databaseService.getItemCountsByFestival as jest.Mock).mockResolvedValue([itemDoc]);

      // Use the correct method: addOrUpdateItemCount(itemName, festivalId, siteLocationId?, quantity?)
      const itemName: ItemName = 'Water';
      const quantity = 10;
      await databaseService.addOrUpdateItemCount(itemName, 'test-festival', undefined, quantity);

      // Use the correct method: getItemCountsByFestival
      const items = await databaseService.getItemCountsByFestival('test-festival');

      expect(databaseService.addOrUpdateItemCount).toHaveBeenCalledWith(itemName, 'test-festival', undefined, quantity);
      expect(databaseService.getItemCountsByFestival).toHaveBeenCalledWith('test-festival');
      expect(items).toHaveLength(1);
      // Check the specific property updated
      expect(items[0]).toEqual(expect.objectContaining({ festivalId: 'test-festival', Water: 10 }));
    });
  });

  describe('Lost Property Management', () => {
    it('should add and retrieve lost property items', async () => {
      // Add missing fields and correct names/values for LostPropertyItem
      const lostItem: LostPropertyItem = {
        _id: 'test-lost-item',
        _rev: '1-rev',
        syncStatus: 'synced', // Add missing required property
        type: 'lost_property',
        documentType: 'lost_property',
        festivalId: 'test-festival',
        category: 'Phone', // Corrected value
        quickDescription: 'Black iPhone', // Add required field
        timeFound: new Date().toISOString(), // Corrected field name
        foundBy: 'Staff Member', // Add required field
        whereFound: 'Main Stage', // Corrected field name
        description: 'Test Phone', // Correct field
        status: 'unclaimed', // Corrected value
        // siteLocationId, locationName, locationType are optional
      };

      // Mock implementation
      (databaseService.addLostPropertyItem as jest.Mock).mockResolvedValue(lostItem);
      (databaseService.getLostPropertyItems as jest.Mock).mockResolvedValue([lostItem]);

      // Note: The service expects Omit<LostPropertyItem, '_id' | '_rev'> for adding
      // For simplicity in mock, we pass the full object, but real implementation differs
      await databaseService.addLostPropertyItem(lostItem);
      const lostItems = await databaseService.getLostPropertyItems();

      expect(databaseService.addLostPropertyItem).toHaveBeenCalledWith(lostItem);
      expect(databaseService.getLostPropertyItems).toHaveBeenCalled();
      expect(lostItems).toHaveLength(1);
      expect(lostItems[0]).toEqual(expect.objectContaining({ _id: 'test-lost-item', category: 'Phone' }));
    });
  });
});
