import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MemoryRouter } from 'react-router-dom';
import AdmissionsPage from '../pages/AdmissionsPage';
import { databaseService } from '../services/database/index';
import { FestivalProvider } from '../contexts/FestivalContext';

// Mock the database service
jest.mock('../services/database/index', () => ({
  databaseService: {
    addAdmission: jest.fn(),
    getAdmissions: jest.fn(),
    getAdmissionsByFestival: jest.fn(),
    updateAdmission: jest.fn(),
    deleteAdmission: jest.fn(),
    getFestivals: jest.fn()
  }
}));

describe('AdmissionsPage', () => {
  const mockFestival = {
    id: 'fest-1',
    name: 'Test Festival',
    startDate: '2024-12-21',
    endDate: '2024-12-22',
    documentType: 'festival'
  };

  beforeEach(() => {
    // Reset all mocks before each test
    jest.clearAllMocks();

    // Setup default mock responses
    (databaseService.getFestivals as jest.Mock).mockResolvedValue([mockFestival]);
    (databaseService.getAdmissionsByFestival as jest.Mock).mockResolvedValue([]);
  });

  describe('New Admission Form', () => {
    it('should handle admission submission correctly', async () => {
      const mockAdmission = {
        id: 'admission-1',
        name: 'John Doe',
        age: 25,
        festivalId: 'fest-1',
        bayOrChair: 'Bay 1',
        admissionTime: expect.any(String),
        documentType: 'admission',
        status: 'active'
      };

      (databaseService.addAdmission as jest.Mock).mockResolvedValue(mockAdmission);
      (databaseService.getAdmissionsByFestival as jest.Mock).mockResolvedValue([mockAdmission]);

      render(
        <FestivalProvider>
          <MemoryRouter>
            <AdmissionsPage />
          </MemoryRouter>
        </FestivalProvider>
      );

      // Wait for the festival data to load
      await waitFor(() => {
        expect(screen.getByText(/test festival/i)).toBeInTheDocument();
      });

      // Fill in admission form
      fireEvent.change(screen.getByLabelText(/name/i), {
        target: { value: 'John Doe' }
      });
      
      fireEvent.change(screen.getByLabelText(/age/i), {
        target: { value: '25' }
      });

      fireEvent.change(screen.getByLabelText(/bay or chair/i), {
        target: { value: 'Bay 1' }
      });

      // Submit form
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));

      await waitFor(() => {
        expect(databaseService.addAdmission).toHaveBeenCalledWith(
          expect.objectContaining({
            name: 'John Doe',
            age: 25,
            bayOrChair: 'Bay 1',
            festivalId: 'fest-1'
          })
        );
      });

      // Verify admission appears in list after adding
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Bay 1')).toBeInTheDocument();
      });
    });

    it('should show validation errors for required fields', async () => {
      render(
        <FestivalProvider>
          <MemoryRouter>
            <AdmissionsPage />
          </MemoryRouter>
        </FestivalProvider>
      );

      // Wait for the component to load
      await waitFor(() => {
        expect(screen.getByText(/test festival/i)).toBeInTheDocument();
      });

      // Try to submit without required fields
      fireEvent.click(screen.getByRole('button', { name: /submit/i }));

      await waitFor(() => {
        expect(screen.getByText(/name is required/i)).toBeInTheDocument();
        expect(databaseService.addAdmission).not.toHaveBeenCalled();
      });
    });
  });

  describe('Admission List', () => {
    it('should display existing admissions', async () => {
      const mockAdmissions = [
        {
          id: 'admission-1',
          name: 'John Doe',
          age: 25,
          festivalId: 'fest-1',
          bayOrChair: 'Bay 1',
          admissionTime: '2024-12-21T10:00:00Z',
          documentType: 'admission',
          status: 'active'
        }
      ];

      (databaseService.getAdmissionsByFestival as jest.Mock).mockResolvedValue(mockAdmissions);

      render(
        <FestivalProvider>
          <MemoryRouter>
            <AdmissionsPage />
          </MemoryRouter>
        </FestivalProvider>
      );

      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
        expect(screen.getByText('Bay 1')).toBeInTheDocument();
      });
    });

    it('should handle admission updates', async () => {
      const mockAdmission = {
        id: 'admission-1',
        name: 'John Doe',
        age: 25,
        festivalId: 'fest-1',
        bayOrChair: 'Bay 1',
        admissionTime: '2024-12-21T10:00:00Z',
        documentType: 'admission',
        status: 'active'
      };

      (databaseService.getAdmissionsByFestival as jest.Mock).mockResolvedValue([mockAdmission]);
      (databaseService.updateAdmission as jest.Mock).mockImplementation(async (admission) => admission);

      render(
        <FestivalProvider>
          <MemoryRouter>
            <AdmissionsPage />
          </MemoryRouter>
        </FestivalProvider>
      );

      // Wait for admission to be displayed
      await waitFor(() => {
        expect(screen.getByText('John Doe')).toBeInTheDocument();
      });

      // Find and click the discharge button
      const dischargeButton = screen.getByRole('button', { name: /discharge/i });
      fireEvent.click(dischargeButton);

      // Fill in discharge notes
      const notesInput = screen.getByLabelText(/discharge notes/i);
      fireEvent.change(notesInput, {
        target: { value: 'Discharged safely' }
      });

      // Submit discharge
      const submitButton = screen.getByRole('button', { name: /confirm discharge/i });
      fireEvent.click(submitButton);

      await waitFor(() => {
        expect(databaseService.updateAdmission).toHaveBeenCalledWith(
          expect.objectContaining({
            id: 'admission-1',
            status: 'discharged'
          })
        );
      });
    });
  });
});
