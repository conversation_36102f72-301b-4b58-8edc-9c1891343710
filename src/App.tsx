import React, { Suspense, lazy } from 'react';
import { Routes, Route } from 'react-router-dom';
import { Sidebar } from './components/Sidebar';
// Import MUI components individually for better tree-shaking
import Box from '@mui/material/Box';
import CircularProgress from '@mui/material/CircularProgress';
import { SiteLocationProvider } from './contexts/SiteLocationContext';
import { AuthProvider } from './contexts/AuthContext';
import { ProtectedRoute } from './components/ProtectedRoute';
import { FeedbackButton } from './components/feedback/FeedbackButton';
import './App.css';

// Lazy load all page components
const DashboardPage = lazy(() =>
  import('./pages/DashboardPage').then(module => ({ default: module.DashboardPage }))
);
const AdmissionsPage = lazy(() =>
  import('./pages/AdmissionsPage').then(module => ({ default: module.AdmissionsPage }))
);
const NewAdmissionPage = lazy(() =>
  import('./pages/NewAdmissionPage').then(module => ({ default: module.NewAdmissionPage }))
);
const FrontOfHousePage = lazy(() =>
  import('./pages/FrontOfHousePage').then(module => ({ default: module.FrontOfHousePage }))
);
const LostPropertyPage = lazy(() =>
  import('./components/LostPropertyPage').then(module => ({ default: module.LostPropertyPage }))
);
const ReportsPage = lazy(() =>
  import('./pages/ReportsPage').then(module => ({ default: module.ReportsPage }))
);
const ShiftsPage = lazy(() =>
  import('./pages/ShiftsPage')
);
const FestivalManagementPage = lazy(() =>
  import('./pages/FestivalManagementPage').then(module => ({ default: module.FestivalManagementPage }))
);
const ChangelogPage = lazy(() =>
  import('./pages/ChangelogPage').then(module => ({ default: module.ChangelogPage }))
);
const UserGuidePageSimple = lazy(() =>
  import('./pages/UserGuidePageSimple').then(module => ({ default: module.UserGuidePageSimple }))
);
const FeedbackManagementPage = lazy(() =>
  import('./pages/FeedbackManagementPage').then(module => ({ default: module.FeedbackManagementPage }))
);
const KnowledgeBasePage = lazy(() =>
  import('./pages/KnowledgeBasePage').then(module => ({ default: module.KnowledgeBasePage }))
);
const AccessManagementPage = lazy(() =>
  import('./pages/AccessManagementPage')
);
const SensoryHubPage = lazy(() =>
  import('./pages/SensoryHubPage').then(module => ({ default: module.SensoryHubPage }))
);

// FIX: Optimized loading fallback for iPad performance
const LoadingFallback = () => {
  console.log('[DEBUG] App: Loading fallback rendered at:', new Date().toISOString());
  return (
    <Box sx={{
      display: 'flex',
      justifyContent: 'center',
      alignItems: 'center',
      height: '100%',
      p: 4,
      // Optimize for iPad rendering
      minHeight: '200px',
      backgroundColor: '#f5f5f5'
    }}>
      <Box sx={{ textAlign: 'center' }}>
        <CircularProgress size={40} />
        <Box sx={{ mt: 2, color: 'text.secondary', fontSize: '0.875rem' }}>
          Loading...
        </Box>
      </Box>
    </Box>
  );
};

const MainLayout: React.FC = () => {
  const handleSignOut = async () => {
    // Implement sign out logic
    return true;
  };

  return (
    <Box sx={{ display: 'flex', height: '100vh' }}>
      <Sidebar onSignOut={handleSignOut} />
      <Box
        component="main"
        sx={{
          flexGrow: 1,
          p: 3,
          overflow: 'auto',
          bgcolor: '#f5f5f5',
        }}
      >
        <Suspense fallback={<LoadingFallback />}>
          <Routes>
            {/* Public routes */}
            <Route path="/" element={<DashboardPage />} />
          <Route path="/changelog" element={<ChangelogPage />} />
          <Route path="/user-guide" element={<UserGuidePageSimple />} />
          
          {/* Feature-protected routes */}
          <Route
            path="/admissions"
            element={
              <ProtectedRoute feature="admissions">
                <AdmissionsPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/new-admission"
            element={
              <ProtectedRoute feature="new-admission">
                <NewAdmissionPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/edit-admission/:id"
            element={
              <ProtectedRoute feature="new-admission">
                <NewAdmissionPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/front-of-house"
            element={
              <ProtectedRoute feature="front-of-house">
                <FrontOfHousePage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/lost-property"
            element={
              <ProtectedRoute feature="lost-property">
                <LostPropertyPage />
              </ProtectedRoute>
            }
          />
          <Route
            path="/sensory-hub"
            element={
              <ProtectedRoute feature="sensory-hub">
                <SensoryHubPage />
              </ProtectedRoute>
            }
          />
          
          {/* Protected routes */}
          <Route
            path="/reports"
            element={
              <ProtectedRoute feature="reports">
                <ReportsPage />
              </ProtectedRoute>
            }
          />
          
          <Route
            path="/shifts/:festivalId"
            element={
              <ProtectedRoute feature="shifts">
                <ShiftsPage />
              </ProtectedRoute>
            }
          />
          
          <Route
            path="/admin/festivals"
            element={
              <ProtectedRoute feature="festival-management">
                <FestivalManagementPage />
              </ProtectedRoute>
            }
          />
          
          <Route
            path="/admin/feedback"
            element={
              <ProtectedRoute feature="feedback-management">
                <FeedbackManagementPage />
              </ProtectedRoute>
            }
          />
          
          <Route
            path="/admin/access"
            element={
              <ProtectedRoute feature="access-management">
                <AccessManagementPage />
              </ProtectedRoute>
            }
          />
          
          <Route
            path="/knowledge-base"
            element={
              <ProtectedRoute feature="knowledge-base-view">
                <KnowledgeBasePage />
              </ProtectedRoute>
            }
          />
          </Routes>
        </Suspense>
        <FeedbackButton />
      </Box>
    </Box>
  );
};

const App: React.FC = () => {
  return (
    <AuthProvider>
      <SiteLocationProvider>
        <MainLayout />
      </SiteLocationProvider>
    </AuthProvider>
  );
};

export default App;
