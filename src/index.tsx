import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { BrowserRouter } from 'react-router-dom';
import { FestivalProvider } from './contexts/FestivalContext';
import { SiteLocationProvider } from './contexts/SiteLocationContext';
import { ThemeProvider } from '@mui/material/styles';
import CssBaseline from '@mui/material/CssBaseline';
import { theme } from './theme/theme';

// DEBUG: Add performance logging for iPad loading issues
console.log('[DEBUG] App initialization started at:', new Date().toISOString());
console.log('[DEBUG] User agent:', navigator.userAgent);
console.log('[DEBUG] Is iPad:', /iPad|iPhone|iPod/.test(navigator.userAgent));
console.log('[DEBUG] Available memory:', (navigator as any).deviceMemory || 'unknown');

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);

// DEBUG: Log render start
console.log('[DEBUG] React render started at:', new Date().toISOString());

// FIX: Remove React.StrictMode for old iPad Airs - it causes double rendering and memory issues
root.render(
  <ThemeProvider theme={theme}>
    <CssBaseline />
    <BrowserRouter>
      <FestivalProvider>
        <SiteLocationProvider>
          <App />
        </SiteLocationProvider>
      </FestivalProvider>
    </BrowserRouter>
  </ThemeProvider>
);

// DEBUG: Log render completion
console.log('[DEBUG] React render completed at:', new Date().toISOString());

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();