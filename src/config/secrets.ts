// Production secrets file - values are loaded from environment variables
// To fix sync issues, you need to provide valid Cloudflare Access credentials
// You can get these from your Cloudflare Access dashboard
// 1. Go to https://dash.cloudflare.com/
// 2. Select your account and domain
// 3. Go to Access > Applications
// 4. Find your application and go to "Service Auth"
// 5. Create a service token and copy the Client ID and Client Secret

export const secrets = {
  database: {
    // Database configuration
    localName: 'ithinc_welfare', // Local database name
    remoteUrl: 'https://couchdb.brisflix.com', // Remote CouchDB URL
    remoteName: 'ithinc_welfare', // Remote database name
    
    // Cloudflare Access credentials (REQUIRED for sync to work)
    cloudflare: {
      clientId: '4407321a127017362955b7aec7346dff.access', // Add your Cloudflare Access Client ID here
      clientSecret: '3c835971357d3c402bd12c0032989fc5d8ac17854164ff77ffae1c20da9a6756' // Add your Cloudflare Access Client Secret here
    },
    
    // Basic authentication fallback (if needed)
    username: 'ithinc',
    password: 'all the welfare cases'
  }
};

// NOTE: If you're seeing sync errors with "Unexpected token '<', "<!DOCTYPE "... is not valid JSON",
// this indicates that Cloudflare Access is returning an HTML login page instead of allowing API access.
// You MUST provide valid Cloudflare Access credentials above for sync to work properly.