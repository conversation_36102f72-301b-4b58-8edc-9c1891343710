// Copy this file to secrets.ts and update with your configuration
export const secrets = {
  database: {
    // Local PouchDB database name
    localName: 'ithinc_welfare',
    
    // Remote CouchDB configuration through Nginx proxy
    remoteUrl: 'https://your-domain.com/db',  // Points to Nginx proxy location /db/
    remoteName: 'ithinc_welfare',
    
    // Cloudflare Access authentication
    // To get these credentials:
    // 1. Go to Cloudflare Zero Trust dashboard > Access > Applications
    // 2. Select your application
    // 3. Go to "Service Auth" tab and create a service token
    // 4. Copy the Client ID and Client Secret
    // Note: The Nginx proxy will forward these credentials to the CouchDB server
    cloudflare: {
      clientId: 'your-client-id.access',      // CF-Access-Client-Id
      clientSecret: 'your-client-secret'      // CF-Access-Client-Secret
    },
    
    // Fallback basic auth credentials (for development/local testing)
    username: 'your-username',
    password: 'your-password',
  }
};
