import { BaseDocument, NewBaseDocument } from './base';

// Item types
export type ItemName =
  | 'Suncream'
  | 'Poncho'
  | 'Water'
  | 'SanitaryProducts'
  | 'Earplugs'
  | 'Condoms'
  | 'ChildrensWristbands'
  | 'GeneralWristbands'
  | 'Charging'
  | 'Sanitizer'
  | 'ToiletRoll'
  | 'GeneralEnqs'
  | 'HotWater'
  | 'RestAndRecuperation'
  | 'Other';

export interface ItemDocument extends BaseDocument {
  documentType: 'item';
  type: 'item';
  festivalId: string;
  siteLocationId?: string; // Reference to FestivalLocation.id
  locationName?: string; // Cached location name for easier querying
  locationType?: string; // Cached location type for easier querying
  Suncream: number;
  Poncho: number;
  Water: number;
  SanitaryProducts: number;
  Earplugs: number;
  Condoms: number;
  ChildrensWristbands: number;
  GeneralWristbands: number;
  Charging: number;
  Sanitizer: number;
  ToiletRoll: number;
  GeneralEnqs: number;
  HotWater: number;
  RestAndRecuperation: number;
  Other: string;
}

// Lost Property types
export type ItemCategory =
  | 'Phone'
  | 'Passport'
  | 'Keys'
  | 'Bag'
  | 'Clothing'
  | 'Jewelry'
  | 'ID/Cards'
  | 'Electronics'
  | 'Wallet'
  | 'Sanity'
  | 'Driving License'
  | 'Medication'
  | 'Glasses'
  | 'Camera'
  | 'Tickets'
  | 'Cuddly Toy'
  | 'Watch/Jewellery'
  | 'Headphone'
  | 'Other';

export interface LostPropertyItem extends BaseDocument {
  type: 'lost_property';
  documentType: 'lost_property';
  festivalId: string;
  siteLocationId?: string; // Reference to FestivalLocation.id
  locationName?: string; // Cached location name for easier querying
  locationType?: string; // Cached location type for easier querying
  category: ItemCategory;
  quickDescription: string;
  timeFound: string;
  foundBy: string;
  whereFound: string;
  description: string;
  attachment?: File;
  itemReturned?: string;
  status: 'claimed' | 'unclaimed';
}

export interface NewLostPropertyItem extends NewBaseDocument {
  type: 'lost_property';
  documentType: 'lost_property';
  festivalId: string;
  siteLocationId?: string; // Reference to FestivalLocation.id
  locationName?: string; // Cached location name for easier querying
  locationType?: string; // Cached location type for easier querying
  category: ItemCategory;
  quickDescription: string;
  timeFound: string;
  foundBy: string;
  whereFound: string;
  description: string;
  attachment?: File;
  itemReturned?: string;
  status?: 'claimed' | 'unclaimed';
}
