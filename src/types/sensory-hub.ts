import { BaseDocument } from './base';

// Sensory Hub Visit Purpose Types
export type VisitPurpose = 'look_around' | 'use_service';

// User Type for Sensory Hub Visits
export type UserType = 'crew' | 'public';

// Sensory Hub Visit Document Interface
export interface SensoryHubVisit extends BaseDocument {
  documentType: 'sensory-hub-visit';
  type: 'sensory-hub-visit';
  festivalId: string;
  siteLocationId?: string;
  visitTimestamp: string;
  purpose: VisitPurpose;
  userType: UserType;
  teamName?: string; // Required if userType === 'crew'
}

// Type for creating new sensory hub visits (omitting BaseDocument fields)
export type NewSensoryHubVisit = Omit<SensoryHubVisit, '_id' | '_rev' | 'syncStatus' | 'timestamp' | 'createdAt' | 'updatedAt' | 'isDeleted' | 'deletedAt'>;