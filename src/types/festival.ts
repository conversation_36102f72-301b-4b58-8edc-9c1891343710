import { BaseDocument, NewBaseDocument } from './base';

export type LocationType = 'arena' | 'campsite' | 'other';

export interface FestivalLocation {
  id: string;
  name: string;
  type: LocationType;
  description?: string;
}

// Festival types
export interface Festival extends BaseDocument {
  type: 'festival' | 'regular_event';
  documentType: 'festival';
  name: string;
  startDate: string;
  endDate: string;
  location: string; // Kept for backward compatibility
  locations: FestivalLocation[]; // New field for multiple locations
  hasMultipleLocations: boolean;
  isActive: boolean;
  mainUrl?: string;
  mapUrl?: string;
  travelInfoUrl?: string;
  faqsUrl?: string;
  showAdmissions: boolean;
  showFrontOfHouse: boolean;
  showLostProperty: boolean;
  showShifts: boolean;
}

export interface NewFestival extends NewBaseDocument {
  type: 'festival' | 'regular_event';
  documentType: 'festival';
  name: string;
  startDate: string;
  endDate: string;
  location: string; // Kept for backward compatibility
  locations: FestivalLocation[]; // New field for multiple locations
  hasMultipleLocations: boolean;
  isActive: boolean;
  mainUrl?: string;
  mapUrl?: string;
  travelInfoUrl?: string;
  faqsUrl?: string;
  showAdmissions: boolean;
  showFrontOfHouse: boolean;
  showLostProperty: boolean;
  showShifts: boolean;
}

export interface FestivalNotes extends BaseDocument {
  type: 'festival_notes';
  documentType: 'festival_notes';
  festivalId: string;
  notes: string;
  lastUpdated: string;
}

export interface NewFestivalNotes extends Omit<FestivalNotes, keyof BaseDocument> {
  type: 'festival_notes';
  documentType: 'festival_notes';
  festivalId: string;
  notes: string;
  lastUpdated: string;
}
