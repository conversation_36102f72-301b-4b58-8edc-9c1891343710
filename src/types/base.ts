import PouchDB from 'pouchdb-browser';

// PouchDB types
export type PouchDocument = PouchDB.Core.ExistingDocument<{
  type: string;
  documentType: string;
  syncStatus: SyncStatus;
}>;

// Common types and enums
export type SyncStatus = 
  | 'local_only'    // Only exists locally
  | 'remote_only'   // Only exists on remote
  | 'synced'        // Exists in both local and remote
  | 'sync_pending'  // Waiting to be synced to remote
  | 'sync_error'    // Error during sync
  | 'syncing'       // Currently syncing
  | 'disconnected'; // Not connected to remote

// Base PouchDB document type
export interface BaseDocument {
  _id: string;
  _rev?: string;
  type: string;
  documentType: string;
  syncStatus: SyncStatus;
  timestamp?: string;
  createdAt?: string;
  updatedAt?: string;
  isDeleted?: boolean;
  deletedAt?: string;
}

// New type for creating documents
export interface NewBaseDocument extends Omit<BaseDocument, '_id' | '_rev'> {
  type: string;
  documentType: string;
  syncStatus: SyncStatus;
}

// Note types
export interface NoteEntry {
  timestamp: string;
  note: string;
  author: string;
}

export interface HistoryEntry {
  timestamp: string;
  action: string;
  details: string;
  author: string;
}

// Type guard functions
export function isDateTimeField(value: string): value is string {
  return /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/.test(value);
}

export function isLongformField(value: string): value is string {
  return value.length > 280;
}

export function isFormulaField(value: number): value is number {
  return !isNaN(value) && isFinite(value);
}
