import { BaseDocument } from './base';

// Base feedback fields without document metadata
interface FeedbackFields {
  name: string;
  page: string;
  feedback: string;
  resolved: boolean;
}

// Full feedback document with all required fields
export interface Feedback extends BaseDocument, FeedbackFields {
  type: 'feedback';
  documentType: 'feedback';
}

// Type for creating new feedback
export type NewFeedback = Omit<Feedback, '_rev'> & {
  _id: string;
  syncStatus: 'sync_pending';
};

// Input type for user-provided feedback data
export type FeedbackInput = Pick<FeedbackFields, 'name' | 'page' | 'feedback'>;