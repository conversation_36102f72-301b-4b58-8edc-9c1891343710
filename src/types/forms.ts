import { 
  NewWelfareAdmission,
  BaysOrChairs,
  Gender,
  Ethnicity,
  SafeguardingCategory,
  SubstanceType
} from './admission';
import { NoteEntry, HistoryEntry, SyncStatus } from './base';
import { SelectChangeEvent } from '@mui/material';

// Base form data with optional _id for editing
export interface AdmissionFormData {
  _id?: string;
  _rev?: string;
  type: 'admission';
  documentType: 'admission';
  status: string;
  FirstName: string;
  Surname: string;
  Age?: number;
  Gender: Gender;
  Attended: string;
  BaysOrChairs: BaysOrChairs;
  InBayNow: boolean;
  ReferredBy: string;
  ReasonCategory: string;
  SubstanceUsed: SubstanceType[];
  AdmissionNotes: string;
  AdditionalNotes: NoteEntry[];
  History: HistoryEntry[];
  DOB: string;
  Pronoun: string;
  Ethnicity: Ethnicity;
  ContactName: string;
  ContactNumber: string;
  DischargeTime: string;
  // Bay location (numeric identifier for the specific bay/chair)
  Location?: number;
  // Site location (reference to festival location - arena/campsite)
  siteLocationId?: string;
  siteLocationName?: string;
  siteLocationType?: string;
  MentalHealth?: string;
  Safeguarding?: string;
  SafeguardingNotes?: string;
  HairColour: string;
  HairStyle: string;
  ClothingTop: string;
  ClothingBottom: string;
  Footwear: string;
  OtherFeatures: string;
  festivalId: string;
  syncStatus: SyncStatus;
  timestamp?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface AdmissionFormState {
  formData: AdmissionFormData;
  isSubmitting: boolean;
  error: Error | null;
}

export interface AdmissionFormHandlers {
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handleSelectChange: (e: SelectChangeEvent<unknown>) => void;
  handleSubmit: () => Promise<void>;
  setFormData: React.Dispatch<React.SetStateAction<AdmissionFormData>>;
  resetForm: (data: AdmissionFormData) => void;
}

export const createInitialFormData = (): AdmissionFormData => ({
  type: 'admission',
  documentType: 'admission',
  status: 'active',
  FirstName: '',
  Surname: '',
  Gender: 'Prefer not to say' as Gender,
  Attended: new Date().toISOString(),
  BaysOrChairs: 'Bay' as BaysOrChairs,
  InBayNow: true,
  ReferredBy: '',
  ReasonCategory: '',
  SubstanceUsed: [] as SubstanceType[],
  AdmissionNotes: '',
  AdditionalNotes: [] as NoteEntry[],
  History: [] as HistoryEntry[],
  DOB: '',
  Pronoun: '',
  Ethnicity: 'Prefer not to say' as Ethnicity,
  ContactName: '',
  ContactNumber: '',
  DischargeTime: '',
  HairColour: '',
  HairStyle: '',
  ClothingTop: '',
  ClothingBottom: '',
  Footwear: '',
  OtherFeatures: '',
  syncStatus: 'local_only',
  festivalId: '', // Will be set when form is submitted
  Location: undefined,
  siteLocationId: undefined,
  siteLocationName: undefined,
  siteLocationType: undefined,
  SafeguardingNotes: '',
  timestamp: new Date().toISOString(),
  createdAt: new Date().toISOString()
});