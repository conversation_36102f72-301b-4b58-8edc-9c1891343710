import { BaseDocument, NewBaseDocument, NoteEntry, HistoryEntry } from './base';
import { FestivalLocation } from './festival';

// Admission Enums
export type Gender = 'Male' | 'Female' | 'Other' | 'Prefer not to say';
export type BaysOrChairs = 'Bay' | 'Chair' | 'Outside';
export type ReferralSource = 'Medical' | 'Security' | 'Friend/Family' | 'Self Referral' | 'Public';
export type ReasonCategory = 
  | 'Substance Use' 
  | 'Mental Health' 
  | 'Safeguarding' 
  | 'Medical' 
  | 'Weather Affected' 
  | 'Other - detail in notes' 
  | 'Rest and Recuperation';

export type SubstanceType = 
  | 'Ecstasy/MDMA' 
  | 'Cocaine' 
  | 'LSD' 
  | 'Mushrooms' 
  | 'Ketamine' 
  | 'Alcohol' 
  | 'GHB' 
  | 'Cannabis' 
  | 'Opiates' 
  | 'Nothing' 
  | 'Other (Please Specify)';

export type MentalHealthCategory = 
  | 'Substance Induced' 
  | 'Anxiety' 
  | 'Paranoia' 
  | 'Suicidal' 
  | 'Depression' 
  | 'Mania' 
  | 'Other';

export type SafeguardingCategory = 
  | 'Sexual Assault' 
  | 'Physical Assault' 
  | 'Emotional Abuse' 
  | 'Other';

export type Ethnicity =
  | 'White'
  | 'Mixed'
  | 'Asian'
  | 'Black'
  | 'Other'
  | 'Prefer not to say';

// Admission types
export interface WelfareAdmission extends BaseDocument {
  type: 'admission';
  documentType: 'admission';
  FirstName: string;
  Surname: string;
  Age?: number;
  Gender: Gender;
  Attended: string;
  BaysOrChairs: BaysOrChairs;
  InBayNow: boolean;
  ReferredBy: string;
  ReasonCategory: string;
  SubstanceUsed: SubstanceType[];
  AdmissionNotes: string;
  AdditionalNotes: NoteEntry[];
  History: HistoryEntry[];
  DOB: string;
  Pronoun: string;
  Ethnicity: Ethnicity;
  ContactName: string;
  ContactNumber: string;
  DischargeTime: string;
  // Bay location (numeric identifier for the specific bay/chair)
  Location?: number;
  // Site location (reference to festival location - arena/campsite)
  siteLocationId?: string;
  siteLocationName?: string;
  siteLocationType?: string;
  MentalHealth?: string;
  Safeguarding?: string;
  HairColour: string;
  HairStyle: string;
  ClothingTop: string;
  ClothingBottom: string;
  Footwear: string;
  OtherFeatures: string;
  festivalId: string;
  status: string;
}

export interface NewWelfareAdmission extends NewBaseDocument {
  type: 'admission';
  documentType: 'admission';
  FirstName: string;
  Surname: string;
  Age?: number;
  Gender: Gender;
  Attended: string;
  BaysOrChairs: BaysOrChairs;
  InBayNow: boolean;
  ReferredBy: string;
  ReasonCategory: string;
  SubstanceUsed: SubstanceType[];
  AdmissionNotes: string;
  AdditionalNotes: NoteEntry[];
  History: HistoryEntry[];
  DOB: string;
  Pronoun: string;
  Ethnicity: Ethnicity;
  ContactName: string;
  ContactNumber: string;
  DischargeTime: string;
  // Bay location (numeric identifier for the specific bay/chair)
  Location?: number;
  // Site location (reference to festival location - arena/campsite)
  siteLocationId?: string;
  siteLocationName?: string;
  siteLocationType?: string;
  MentalHealth?: string;
  Safeguarding?: string;
  HairColour: string;
  HairStyle: string;
  ClothingTop: string;
  ClothingBottom: string;
  Footwear: string;
  OtherFeatures: string;
  festivalId: string;
  status: string;
}
