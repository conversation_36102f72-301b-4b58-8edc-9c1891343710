import PouchDB from 'pouchdb-browser';
import { SyncStatus } from './base';

// Shift Management Types
export interface ShiftConfig extends PouchDB.Core.Document<{
  _id: string;
  _rev?: string;
  type: 'shift_config';
  documentType: 'shift_config';
  festivalId: string;
  shiftsPerDay: number;
  firstShiftStart: string;
  shiftDuration: number;
  maxTeamSize: number;
  syncStatus: SyncStatus;
}> {
  type: 'shift_config';
  documentType: 'shift_config';
  festivalId: string;
  shiftsPerDay: number;
  firstShiftStart: string; // HH:mm format
  shiftDuration: number; // in hours
  maxTeamSize: number;
  syncStatus: SyncStatus;
}

export interface TeamLeader extends PouchDB.Core.Document<{
  _id: string;
  _rev?: string;
  type: 'team_leader';
  documentType: 'team_leader';
  festivalId: string;
  name: string;
  contact?: string;
  teams: string[]; // Array of team letters ('A', 'B', 'C', etc.)
  syncStatus: SyncStatus;
}> {
  type: 'team_leader';
  documentType: 'team_leader';
  festivalId: string;
  name: string;
  contact?: string;
  teams: string[]; // Array of team letters ('A', 'B', 'C', etc.)
  syncStatus: SyncStatus;
}

export interface ShiftAssignment extends PouchDB.Core.Document<{
  _id: string;
  _rev?: string;
  type: 'shift_assignment';
  documentType: 'shift_assignment';
  festivalId: string;
  date: string;
  shiftNumber: number;
  startTime: string;
  endTime: string;
  teamLeaderId: string;
  teamLetter: string;
  teamMembers: string[];
  notes?: string;
  syncStatus: SyncStatus;
}> {
  type: 'shift_assignment';
  documentType: 'shift_assignment';
  festivalId: string;
  date: string;
  shiftNumber: number;
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  teamLeaderId: string;
  teamLetter: string; // 'A', 'B', 'C', etc.
  teamMembers: string[]; // Array of team member names
  notes?: string;
  syncStatus: SyncStatus;
}

export interface NewShiftConfig {
  type: 'shift_config';
  documentType: 'shift_config';
  festivalId: string;
  shiftsPerDay: number;
  firstShiftStart: string;
  shiftDuration: number;
  maxTeamSize: number;
  syncStatus: SyncStatus;
}

export interface NewTeamLeader {
  type: 'team_leader';
  documentType: 'team_leader';
  festivalId: string;
  name: string;
  contact?: string;
  teams: string[]; // Array of team letters ('A', 'B', 'C', etc.)
  syncStatus: SyncStatus;
}

export interface NewShiftAssignment {
  type: 'shift_assignment';
  documentType: 'shift_assignment';
  festivalId: string;
  date: string;
  shiftNumber: number;
  startTime: string;
  endTime: string;
  teamLeaderId: string;
  teamLetter: string;
  teamMembers: string[];
  notes?: string;
  syncStatus: SyncStatus;
}

// Helper function to get team letter from index
export function getTeamLetter(index: number): string {
  return String.fromCharCode(65 + index); // 65 is ASCII for 'A'
}
