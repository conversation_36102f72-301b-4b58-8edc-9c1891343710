import React, { useState, useEffect } from 'react';
import {
  Box,
  Chip,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  List,
  ListItem,
  ListItemText,
  Divider,
  Alert,
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';
import CachedIcon from '@mui/icons-material/Cached';
import ClearIcon from '@mui/icons-material/Clear';
import SyncIcon from '@mui/icons-material/Sync';
import SyncProblemIcon from '@mui/icons-material/SyncProblem';
import OfflineBoltIcon from '@mui/icons-material/OfflineBolt';
import { useCacheControl } from '../../hooks/useSmartData';
import { useSyncStatus } from '../../hooks/useSyncStatus';

interface CacheStatusProps {
  showDetails?: boolean;
}

export const CacheStatus: React.FC<CacheStatusProps> = ({ showDetails = false }) => {
  const { stats, refreshStats, clearCache, isInitialSyncComplete } = useCacheControl();
  const syncStatus = useSyncStatus();
  const [dialogOpen, setDialogOpen] = useState(false);

  useEffect(() => {
    // Refresh stats every 5 seconds
    const interval = setInterval(refreshStats, 5000);
    return () => clearInterval(interval);
  }, [refreshStats]);

  const handleClearCache = () => {
    clearCache();
    setDialogOpen(false);
  };

  const getSyncStatusColor = () => {
    switch (syncStatus.status) {
      case 'synced': return 'success';
      case 'syncing': return 'info';
      case 'auth_error': return 'warning';
      case 'error': return 'error';
      default: return 'default';
    }
  };

  const getSyncStatusIcon = () => {
    switch (syncStatus.status) {
      case 'synced': return <SyncIcon />;
      case 'syncing': return <SyncIcon />;
      case 'auth_error': return <OfflineBoltIcon />;
      case 'error': return <SyncProblemIcon />;
      default: return <SyncProblemIcon />;
    }
  };

  const getSyncStatusLabel = () => {
    switch (syncStatus.status) {
      case 'synced': return 'Online';
      case 'syncing': return 'Syncing';
      case 'auth_error': return 'Offline Mode';
      case 'error': return 'Sync Error';
      default: return 'Disconnected';
    }
  };

  if (!showDetails && stats.size === 0 && syncStatus.status === 'synced') {
    return null;
  }

  return (
    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
      <Chip
        icon={getSyncStatusIcon()}
        label={getSyncStatusLabel()}
        size="small"
        color={getSyncStatusColor()}
        variant="outlined"
      />
      
      {stats.size > 0 && (
        <Chip
          icon={<CachedIcon />}
          label={`${stats.size} cached`}
          size="small"
          color={isInitialSyncComplete ? 'success' : 'warning'}
          variant="outlined"
        />
      )}
      
      {showDetails && (
        <>
          <Tooltip title="Cache Details">
            <IconButton size="small" onClick={() => setDialogOpen(true)}>
              <InfoIcon />
            </IconButton>
          </Tooltip>

          <Dialog open={dialogOpen} onClose={() => setDialogOpen(false)} maxWidth="sm" fullWidth>
            <DialogTitle>
              System Status
            </DialogTitle>
            <DialogContent>
              {/* Sync Status Section */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  Sync Status:
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Status: {getSyncStatusLabel()}
                </Typography>
                {syncStatus.lastSync && (
                  <Typography variant="body2" color="text.secondary">
                    Last Sync: {syncStatus.lastSync.toLocaleTimeString()}
                  </Typography>
                )}
                {syncStatus.pendingChanges > 0 && (
                  <Typography variant="body2" color="text.secondary">
                    Pending Changes: {syncStatus.pendingChanges}
                  </Typography>
                )}
              </Box>

              {/* Auth Error Alert */}
              {syncStatus.authError && (
                <Alert severity="warning" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    Authentication with server failed. App is running in offline mode using cached data.
                    All changes are saved locally and will sync when connection is restored.
                  </Typography>
                </Alert>
              )}

              {/* Error Display */}
              {syncStatus.error && !syncStatus.authError && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  <Typography variant="body2">
                    {syncStatus.error}
                  </Typography>
                </Alert>
              )}

              <Divider sx={{ my: 2 }} />

              {/* Cache Status Section */}
              <Box sx={{ mb: 2 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>
                  Cache Status:
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Initial Sync: {isInitialSyncComplete ? 'Complete' : 'Pending'}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Cached Items: {stats.size}
                </Typography>
              </Box>

              <Divider sx={{ my: 2 }} />

              <Typography variant="subtitle2" sx={{ mb: 1 }}>
                Cache Entries:
              </Typography>
              
              {stats.entries.length > 0 ? (
                <List dense>
                  {stats.entries.map((entry, index) => (
                    <ListItem key={index}>
                      <ListItemText
                        primary={entry}
                        primaryTypographyProps={{ variant: 'body2', fontFamily: 'monospace' }}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No cached entries
                </Typography>
              )}
            </DialogContent>
            <DialogActions>
              <Button
                onClick={handleClearCache}
                startIcon={<ClearIcon />}
                color="warning"
              >
                Clear Cache
              </Button>
              <Button onClick={() => setDialogOpen(false)}>
                Close
              </Button>
            </DialogActions>
          </Dialog>
        </>
      )}
    </Box>
  );
};