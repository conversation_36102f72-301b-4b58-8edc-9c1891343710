import React from 'react';
import {
  Box,
  Typography,
  ListItem,
  ButtonGroup,
  Button,
  Tooltip,
} from '@mui/material';
import { useFestival } from '../../contexts/FestivalContext';
import { useSiteLocation } from '../../contexts/SiteLocationContext';
import { MapPinIcon } from '@heroicons/react/24/outline';

interface SiteLocationSelectorProps {
  isCollapsed?: boolean;
}

export const SiteLocationSelector: React.FC<SiteLocationSelectorProps> = ({ isCollapsed }) => {
  const { activeFestival } = useFestival();
  const { activeSiteLocation, setActiveSiteLocation } = useSiteLocation();

  if (!activeFestival?.hasMultipleLocations) {
    return null;
  }

  const handleLocationSelect = (locationId: string | null) => {
    if (!locationId) {
      setActiveSiteLocation(null);
      return;
    }
    
    const location = activeFestival.locations.find(loc => loc.id === locationId);
    if (location) {
      setActiveSiteLocation(location);
    }
  };

  const iconSx = {
    width: 18,
    height: 18,
    mr: isCollapsed ? 0 : 1.5,
    color: 'black',
  };

  if (isCollapsed) {
    return (
      <ListItem disablePadding>
        <ButtonGroup orientation="vertical" fullWidth size="small">
          <Button
            variant={!activeSiteLocation ? 'contained' : 'outlined'}
            onClick={() => handleLocationSelect(null)}
            sx={{ minWidth: 40 }}
          >
            <Box component={MapPinIcon} sx={iconSx} />
          </Button>
          {activeFestival.locations.map((location) => (
            <Tooltip key={location.id} title={location.name} placement="right">
              <Button
                variant={activeSiteLocation?.id === location.id ? 'contained' : 'outlined'}
                onClick={() => handleLocationSelect(location.id)}
                sx={{ minWidth: 40 }}
              >
                {location.type === 'arena' ? 'A' : 'C'}
              </Button>
            </Tooltip>
          ))}
        </ButtonGroup>
      </ListItem>
    );
  }

  return (
    <ListItem disablePadding>
      <Box sx={{ width: '100%' }}>
        <Typography variant="subtitle2" sx={{ mb: 1, fontWeight: 600, color: 'black' }}>
          Active Location
        </Typography>
        <ButtonGroup fullWidth>
          <Button
            variant={!activeSiteLocation ? 'contained' : 'outlined'}
            onClick={() => handleLocationSelect(null)}
            startIcon={<MapPinIcon style={{ width: 18, height: 18 }} />}
          >
            All
          </Button>
          {activeFestival.locations.map((location) => (
            <Button
              key={location.id}
              variant={activeSiteLocation?.id === location.id ? 'contained' : 'outlined'}
              onClick={() => handleLocationSelect(location.id)}
            >
              {location.type === 'arena' ? 'Arena' : 'Campsite'}
            </Button>
          ))}
        </ButtonGroup>
      </Box>
    </ListItem>
  );
};