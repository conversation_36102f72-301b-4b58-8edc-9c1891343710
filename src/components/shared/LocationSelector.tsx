import React from 'react';
import {
  FormControl,
  InputLabel,
  MenuItem,
  Select,
  SelectChangeEvent,
  Typography,
  Box,
} from '@mui/material';
import { FestivalLocation } from '../../types/festival';

interface LocationSelectorProps {
  locations: FestivalLocation[];
  selectedSiteLocationId?: string;
  onSiteLocationChange: (siteLocationId: string) => void;
  label?: string;
  required?: boolean;
  disabled?: boolean;
  error?: boolean;
  helperText?: string;
}

export const LocationSelector: React.FC<LocationSelectorProps> = ({
  locations,
  selectedSiteLocationId,
  onSiteLocationChange,
  label = 'Location',
  required = false,
  disabled = false,
  error = false,
  helperText,
}) => {
  const handleChange = (event: SelectChangeEvent<string>) => {
    onSiteLocationChange(event.target.value);
  };

  const getLocationTypeLabel = (type: string) => {
    switch (type) {
      case 'arena':
        return 'Arena';
      case 'campsite':
        return 'Campsite';
      default:
        return 'Other';
    }
  };

  return (
    <FormControl fullWidth error={error} disabled={disabled}>
      <InputLabel id="location-select-label" required={required}>
        {label}
      </InputLabel>
      <Select
        labelId="location-select-label"
        id="location-select"
        value={selectedSiteLocationId || ''}
        label={label}
        onChange={handleChange}
        required={required}
      >
        {locations.map((location) => (
          <MenuItem key={location.id} value={location.id}>
            <Box>
              <Typography variant="body1">
                {location.name}
              </Typography>
              <Typography variant="caption" color="textSecondary">
                {getLocationTypeLabel(location.type)}
                {location.description && ` - ${location.description}`}
              </Typography>
            </Box>
          </MenuItem>
        ))}
      </Select>
      {helperText && (
        <Typography
          variant="caption"
          color={error ? 'error' : 'textSecondary'}
          sx={{ mt: 1 }}
        >
          {helperText}
        </Typography>
      )}
    </FormControl>
  );
};