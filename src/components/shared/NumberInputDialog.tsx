import React, { useState, useEffect } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Typography,
  Box
} from '@mui/material';

interface NumberInputDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: (quantity: number) => void;
  itemName: string;
  label: string;
}

/**
 * A dialog component for inputting a numeric quantity.
 * Optimized for touch input on iPad devices.
 */
export const NumberInputDialog: React.FC<NumberInputDialogProps> = ({
  open,
  onClose,
  onConfirm,
  itemName,
  label
}) => {
  const [inputValue, setInputValue] = useState<string>("1");
  const [quantity, setQuantity] = useState<number>(1);
  const [error, setError] = useState<string | null>(null);

  // Reset quantity when dialog opens
  useEffect(() => {
    if (open) {
      setInputValue("1");
      setQuantity(1);
      setError(null);
    }
  }, [open]);

  // Allow empty input during editing but validate before submission
  const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const rawValue = e.target.value;
    setInputValue(rawValue);
    
    // Clear error if field is empty to allow for editing
    if (rawValue === "") {
      setError(null);
      return;
    }
    
    const value = parseInt(rawValue, 10);
    if (isNaN(value)) {
      setError('Please enter a valid number');
    } else if (value < 1) {
      setError('Please enter a number greater than 0');
    } else if (value > 100) {
      setError('Maximum quantity is 100');
    } else {
      setError(null);
      setQuantity(value);
    }
  };

  const handleConfirm = () => {
    // Validate one more time to avoid submitting empty inputs
    if (inputValue === "") {
      setError('Please enter a valid number');
      return;
    }
    
    const finalValue = parseInt(inputValue, 10);
    if (!error && finalValue > 0) {
      onConfirm(finalValue);
      onClose();
    }
  };

  // Handle enter key press
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleConfirm();
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      PaperProps={{
        sx: {
          width: '90%',
          maxWidth: '400px',
          borderRadius: 2
        }
      }}
    >
      <DialogTitle sx={{ pb: 1, color: 'primary.main' }}>
        Add {label} Items
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Enter the number of {label} items to add:
          </Typography>
        </Box>
        <TextField
          autoFocus
          margin="dense"
          label="Quantity"
          type="number"
          fullWidth
          variant="outlined"
          value={inputValue}
          onChange={handleQuantityChange}
          onKeyDown={handleKeyDown}
          error={!!error}
          helperText={error || "Enter the number of items to add"}
          inputProps={{
            inputMode: 'numeric',
            pattern: '[0-9]*',
            sx: {
              fontSize: '1.2rem',
              padding: '12px'
            }
          }}
          // Override default number input spinners for better touch experience
          sx={{
            '& input[type=number]::-webkit-inner-spin-button, & input[type=number]::-webkit-outer-spin-button': {
              '-webkit-appearance': 'none',
              margin: 0,
            },
            '& input[type=number]': {
              '-moz-appearance': 'textfield',
            },
          }}
        />
      </DialogContent>
      <DialogActions sx={{ p: 2, pt: 1 }}>
        <Button 
          onClick={onClose} 
          color="primary"
          variant="outlined"
          sx={{ 
            mr: 1,
            px: 3,
            py: 1
          }}
        >
          Cancel
        </Button>
        <Button 
          onClick={handleConfirm} 
          color="primary" 
          variant="contained"
          disabled={!!error}
          sx={{ 
            px: 3,
            py: 1
          }}
        >
          Add Items
        </Button>
      </DialogActions>
    </Dialog>
  );
};