import React from 'react';
import { ItemName } from '../../types/item';
import { useLongPress } from '../../hooks/useLongPress';
import { Card, Box, Stack, Typography, Chip, CircularProgress } from '@mui/material';

interface ItemButtonProps {
  itemName: ItemName;
  label: string;
  count: number;
  isUpdating: boolean;
  onItemClick: (itemName: ItemName) => void;
  onLongPress: (itemName: ItemName, label: string) => void;
  IconComponent: React.ComponentType<any>;
}

/**
 * Button component for Front of House items that supports both click and long-press events.
 * Optimized for iPad and mobile devices.
 */
export const ItemButton: React.FC<ItemButtonProps> = ({
  itemName,
  label,
  count,
  isUpdating,
  onItemClick,
  onLongPress,
  IconComponent
}) => {
  // Use long press hook
  const longPressHandlers = useLongPress({
    onClick: () => onItemClick(itemName),
    onLongPress: () => onLongPress(itemName, label),
    threshold: 500
  });
  
  return (
    <Card
      {...longPressHandlers}
      sx={{
        width: 180,
        height: 180,
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        cursor: 'pointer',
        opacity: isUpdating ? 0.5 : 1,
        transition: 'all 0.2s',
        '&:hover': {
          boxShadow: 3
        },
        userSelect: 'none', // Prevent text selection during long press
        touchAction: 'none' // Better touch behavior
      }}
    >
      <Stack spacing={2} alignItems="center">
        <Box
          sx={{
            width: 80,
            height: 80,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            border: 1,
            borderColor: 'grey.200',
            borderRadius: 1,
            bgcolor: 'white',
            position: 'relative'
          }}
        >
          <IconComponent sx={{ fontSize: 48, color: '#662D91' }} />
          {isUpdating && (
            <CircularProgress
              size={24}
              sx={{
                position: 'absolute',
                top: -12,
                right: -12,
                color: 'primary.main'
              }}
            />
          )}
        </Box>
        <Stack alignItems="center" spacing={1}>
          <Typography
            variant="body2"
            sx={{
              fontWeight: 500,
              textAlign: 'center',
              whiteSpace: 'pre-line',
              lineHeight: 'tight'
            }}
          >
            {label}
          </Typography>
          <Chip
            label={count}
            sx={{
              bgcolor: 'purple.100',
              color: 'purple.600',
              fontWeight: 'bold',
              fontSize: '0.875rem'
            }}
          />
        </Stack>
      </Stack>
    </Card>
  );
};