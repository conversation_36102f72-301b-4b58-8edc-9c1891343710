import React, { useState, useEffect } from 'react';
import { databaseService } from '../../services/database';
import { ShiftAssignment, TeamLeader, ShiftConfig } from '../../types/shift';
import {
  Box,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Select,
  MenuItem,
  TextField,
  Typography,
  Paper,
  Stack,
  IconButton,
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';
import DeleteIcon from '@mui/icons-material/Delete';
import RefreshIcon from '@mui/icons-material/Refresh';

interface ShiftScheduleTableProps {
  festivalId: string;
  startDate: string;
  endDate: string;
  shiftConfig: ShiftConfig;
  teamLeaders: TeamLeader[];
  assignments: ShiftAssignment[];
  onAssignmentChange: () => void;
}

const ShiftScheduleTable: React.FC<ShiftScheduleTableProps> = ({
  festivalId,
  startDate,
  endDate,
  shiftConfig,
  teamLeaders,
  assignments,
  onAssignmentChange
}) => {
  const [dates, setDates] = useState<string[]>([]);
  const [editingCell, setEditingCell] = useState<string | null>(null);
  const [teamMembers, setTeamMembers] = useState<string>('');

  useEffect(() => {
    const generateDates = () => {
      const dates: string[] = [];
      const start = new Date(startDate);
      const end = new Date(endDate);

      for (let date = start; date <= end; date.setDate(date.getDate() + 1)) {
        dates.push(date.toISOString().split('T')[0]);
      }

      setDates(dates);
    };

    generateDates();
  }, [startDate, endDate]);

  const getAssignmentForDateAndShift = (date: string, shiftNumber: number) => {
    const existing = assignments.find(
      (a) => a.date === date && a.shiftNumber === shiftNumber
    );

    if (existing) {
      return existing;
    }

    const newAssignment: ShiftAssignment = {
      _id: `shift_assignment_${festivalId}_${date}_${shiftNumber}`,
      type: 'shift_assignment',
      documentType: 'shift_assignment',
      festivalId,
      date,
      shiftNumber,
      startTime: calculateShiftTimes(shiftNumber).start,
      endTime: calculateShiftTimes(shiftNumber).end,
      teamLeaderId: '',
      teamLetter: '',
      teamMembers: [],
      syncStatus: 'local_only'
    };

    databaseService.addShiftAssignment(newAssignment)
      .then(() => {
        onAssignmentChange();
      })
      .catch(error => {
        console.error('Error creating shift assignment:', error);
      });

    return newAssignment;
  };

  const handleTeamMembersChange = async (
    assignment: ShiftAssignment,
    members: string[]
  ) => {
    try {
      const updated = { ...assignment, teamMembers: members };
      await databaseService.updateShiftAssignment(updated);
      onAssignmentChange();
      setEditingCell(null);
    } catch (error) {
      console.error('Error updating team members:', error);
    }
  };

  const getTeamLeaderName = (teamLeaderId: string): string => {
    return teamLeaders.find(tl => tl._id === teamLeaderId)?.name || 'Unknown';
  };

  const calculateShiftTimes = (shiftNumber: number): { start: string; end: string } => {
    const [startHour, startMinute] = shiftConfig.firstShiftStart.split(':').map(Number);
    const shiftOffsetHours = (shiftNumber - 1) * shiftConfig.shiftDuration;
    
    const startTime = new Date();
    startTime.setHours(startHour + shiftOffsetHours, startMinute, 0);
    
    const endTime = new Date(startTime);
    endTime.setHours(startTime.getHours() + shiftConfig.shiftDuration);

    return {
      start: startTime.toTimeString().slice(0, 5),
      end: endTime.toTimeString().slice(0, 5)
    };
  };

  const clearAllShifts = async () => {
    for (let shiftNum = 1; shiftNum <= shiftConfig.shiftsPerDay; shiftNum++) {
      for (const date of dates) {
        const assignment = getAssignmentForDateAndShift(date, shiftNum);
        const updated = {
          ...assignment,
          teamLeaderId: '',
          teamLetter: '',
          teamMembers: []
        };

        try {
          await databaseService.updateShiftAssignment(updated);
        } catch (error) {
          console.error('Error clearing assignment:', error);
        }
      }
    }
    onAssignmentChange();
  };

  const getNextTeam = (currentTeam: string): string => {
    const pattern = ['A', 'B', 'C', 'D', 'E'];
    const currentIndex = pattern.indexOf(currentTeam);
    return pattern[(currentIndex + 1) % pattern.length];
  };

  const fillRotatingPattern = async () => {
    let teamAAssignment: ShiftAssignment | null = null;
    let teamADate: string = '';
    let teamAShift: number = 0;

    for (const date of dates) {
      for (let shiftNum = 1; shiftNum <= shiftConfig.shiftsPerDay; shiftNum++) {
        const assignment = getAssignmentForDateAndShift(date, shiftNum);
        if (assignment.teamLetter === 'A') {
          teamAAssignment = assignment;
          teamADate = date;
          teamAShift = shiftNum;
          break;
        }
      }
      if (teamAAssignment) break;
    }

    if (!teamAAssignment) {
      alert('Please assign Team A to a shift first');
      return;
    }

    const teamALeader = teamLeaders.find(tl => tl._id === teamAAssignment!.teamLeaderId);
    if (!teamALeader) {
      alert('Could not find Team A leader');
      return;
    }

    let currentTeam = 'A';
    let currentShift = teamAShift;
    let currentDateIndex = dates.indexOf(teamADate);

    while (currentDateIndex < dates.length) {
      const assignment = getAssignmentForDateAndShift(dates[currentDateIndex], currentShift);
      const teamLeader = teamLeaders.find(tl => tl.teams.includes(currentTeam));

      if (teamLeader) {
        try {
          const updated = {
            ...assignment,
            teamLeaderId: teamLeader._id,
            teamLetter: currentTeam,
            teamMembers: []
          };
          await databaseService.updateShiftAssignment(updated);
        } catch (error) {
          console.error('Error updating assignment:', error);
        }
      }

      currentShift++;
      if (currentShift > shiftConfig.shiftsPerDay) {
        currentShift = 1;
        currentDateIndex++;
      }

      currentTeam = getNextTeam(currentTeam);
    }

    onAssignmentChange();
  };

  return (
    <Box sx={{ overflowX: 'auto' }}>
      <Stack direction="row" spacing={2} sx={{ mb: 3 }}>
        <Button
          variant="contained"
          color="error"
          startIcon={<DeleteIcon />}
          onClick={clearAllShifts}
        >
          Clear All Shifts
        </Button>
        <Button
          variant="contained"
          startIcon={<RefreshIcon />}
          onClick={fillRotatingPattern}
        >
          Fill Rotating Pattern
        </Button>
      </Stack>

      <TableContainer component={Paper} sx={{ mb: 3 }}>
        <Table size="small">
          <TableHead>
            <TableRow>
              <TableCell
                sx={{
                  fontWeight: 'medium',
                  color: 'text.secondary',
                  fontSize: '0.75rem',
                  textTransform: 'uppercase',
                }}
              >
                Shift
              </TableCell>
              {dates.map((date) => {
                const dateObj = new Date(date);
                const dayOfWeek = dateObj.toLocaleDateString('en-US', { weekday: 'short' });
                const dayAndMonth = dateObj.toLocaleDateString('en-US', { 
                  day: 'numeric',
                  month: 'short'
                });
                
                const startDateObj = new Date(startDate);
                const dayDiff = Math.floor((dateObj.getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24));
                const totalDays = Math.floor((new Date(endDate).getTime() - startDateObj.getTime()) / (1000 * 60 * 60 * 24)) + 1;
                const festivalDay = dayDiff + 1;

                return (
                  <TableCell
                    key={date}
                    sx={{
                      fontWeight: 'medium',
                      color: 'text.secondary',
                      fontSize: '0.75rem',
                      textTransform: 'uppercase',
                    }}
                  >
                    <Stack>
                      <Typography variant="caption">{dayOfWeek}</Typography>
                      <Typography variant="body2">{dayAndMonth}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        Day {festivalDay} of {totalDays}
                      </Typography>
                    </Stack>
                  </TableCell>
                );
              })}
            </TableRow>
          </TableHead>
          <TableBody>
            {Array.from({ length: shiftConfig.shiftsPerDay }).map((_, index) => (
              <TableRow key={index + 1}>
                <TableCell>
                  <Stack>
                    <Typography variant="subtitle2">
                      Shift {index + 1}
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {(() => {
                        const times = calculateShiftTimes(index + 1);
                        return `${times.start} - ${times.end}`;
                      })()}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {shiftConfig.shiftDuration} hour shift
                    </Typography>
                  </Stack>
                </TableCell>
                {dates.map((date) => {
                  const assignment = getAssignmentForDateAndShift(date, index + 1);

                  return (
                    <TableCell key={date}>
                      <Stack spacing={1}>
                        <Select
                          size="small"
                          value={assignment.teamLetter || 'no_shift'}
                          onChange={(e) => {
                            const value = e.target.value;
                            if (value === 'no_shift') {
                              const updated = {
                                ...assignment,
                                teamLeaderId: '',
                                teamLetter: '',
                                teamMembers: []
                              };
                              databaseService.updateShiftAssignment(updated)
                                .then(() => onAssignmentChange())
                                .catch(error => console.error('Error updating assignment:', error));
                            } else {
                              const teamLeader = teamLeaders.find(tl => tl.teams.includes(value));
                              if (teamLeader) {
                                const updated = {
                                  ...assignment,
                                  teamLeaderId: teamLeader._id,
                                  teamLetter: value,
                                  teamMembers: []
                                };
                                databaseService.updateShiftAssignment(updated)
                                  .then(() => onAssignmentChange())
                                  .catch(error => console.error('Error updating assignment:', error));
                              }
                            }
                          }}
                          fullWidth
                        >
                          <MenuItem value="no_shift">No Shift</MenuItem>
                          {teamLeaders.flatMap(leader => 
                            leader.teams.map(team => ({
                              team,
                              leaderName: leader.name
                            }))
                          )
                          .sort((a, b) => a.team.localeCompare(b.team))
                          .map(({ team, leaderName }) => (
                            <MenuItem key={`${team}-${leaderName}`} value={team}>
                              Team {team} ({leaderName})
                            </MenuItem>
                          ))}
                        </Select>

                        {editingCell === `${date}-${index + 1}` ? (
                          <Box>
                            <TextField
                              multiline
                              rows={3}
                              value={teamMembers}
                              onChange={(e) => setTeamMembers(e.target.value)}
                              placeholder="Enter team members (one per line)"
                              size="small"
                              fullWidth
                            />
                            <Stack direction="row" spacing={1} justifyContent="flex-end" sx={{ mt: 1 }}>
                              <IconButton
                                size="small"
                                color="primary"
                                onClick={() => {
                                  handleTeamMembersChange(
                                    assignment,
                                    teamMembers.split('\n').filter(Boolean)
                                  );
                                }}
                              >
                                <SaveIcon />
                              </IconButton>
                              <IconButton
                                size="small"
                                onClick={() => setEditingCell(null)}
                              >
                                <CancelIcon />
                              </IconButton>
                            </Stack>
                          </Box>
                        ) : (
                          <Box
                            onClick={() => {
                              if (assignment.teamLeaderId && assignment.teamLetter) {
                                setEditingCell(`${date}-${index + 1}`);
                                setTeamMembers(
                                  assignment.teamMembers.join('\n') || ''
                                );
                              }
                            }}
                            sx={{
                              cursor: assignment.teamLeaderId ? 'pointer' : 'default',
                              '&:hover': {
                                bgcolor: assignment.teamLeaderId ? 'action.hover' : 'transparent',
                              },
                              p: 1,
                              borderRadius: 1,
                            }}
                          >
                            {assignment.teamLetter ? (
                              <Stack spacing={0.5}>
                                <Typography variant="subtitle2">
                                  Team {assignment.teamLetter} ({getTeamLeaderName(assignment.teamLeaderId)})
                                </Typography>
                                {assignment.teamMembers.length > 0 ? (
                                  <Box component="ul" sx={{ pl: 2, m: 0 }}>
                                    {assignment.teamMembers.map((member, i) => (
                                      <Typography
                                        key={i}
                                        component="li"
                                        variant="body2"
                                        color="text.secondary"
                                      >
                                        {member}
                                      </Typography>
                                    ))}
                                  </Box>
                                ) : (
                                  <Typography
                                    variant="body2"
                                    color="text.secondary"
                                    sx={{ fontStyle: 'italic' }}
                                  >
                                    Click to add team members
                                  </Typography>
                                )}
                              </Stack>
                            ) : (
                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{ fontStyle: 'italic' }}
                              >
                                No Shift
                              </Typography>
                            )}
                          </Box>
                        )}
                      </Stack>
                    </TableCell>
                  );
                })}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>
    </Box>
  );
};

export default ShiftScheduleTable;
