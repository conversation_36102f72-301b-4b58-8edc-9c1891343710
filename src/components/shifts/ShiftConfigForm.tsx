import React, { useState } from 'react';
import { databaseService } from '../../services/database';
import { ShiftConfig, NewShiftConfig } from '../../types/shift';
import {
  Stack,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Box,
  Button,
} from '@mui/material';
import SaveIcon from '@mui/icons-material/Save';

interface ShiftConfigFormProps {
  festivalId: string;
  existingConfig: ShiftConfig | null;
  onConfigSaved: (config: ShiftConfig) => void;
}

const ShiftConfigForm: React.FC<ShiftConfigFormProps> = ({
  festivalId,
  existingConfig,
  onConfigSaved
}) => {
  const [shiftsPerDay, setShiftsPerDay] = useState(existingConfig?.shiftsPerDay || 4);
  const [firstShiftStart, setFirstShiftStart] = useState(existingConfig?.firstShiftStart || '06:00');
  const [shiftDuration, setShiftDuration] = useState(existingConfig?.shiftDuration || 6);
  const [maxTeamSize, setMaxTeamSize] = useState(existingConfig?.maxTeamSize || 6);
  const [saving, setSaving] = useState(false);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      const newConfig: NewShiftConfig = {
        type: 'shift_config',
        documentType: 'shift_config',
        festivalId,
        shiftsPerDay,
        firstShiftStart,
        shiftDuration,
        maxTeamSize,
        syncStatus: 'local_only'
      };

      const savedConfig = await databaseService.saveShiftConfig(newConfig);
      onConfigSaved(savedConfig);
    } catch (error) {
      console.error('Error saving shift configuration:', error);
    } finally {
      setSaving(false);
    }
  };

  const generateTimeOptions = () => {
    const options = [];
    for (let hour = 0; hour < 24; hour++) {
      const hourStr = hour.toString().padStart(2, '0');
      options.push(`${hourStr}:00`);
    }
    return options;
  };

  return (
    <Box component="form" onSubmit={handleSubmit} sx={{ maxWidth: 'sm' }}>
      <Stack spacing={3}>
        <TextField
          label="Number of Shifts per Day"
          type="number"
          value={shiftsPerDay}
          onChange={(e) => setShiftsPerDay(parseInt(e.target.value))}
          inputProps={{
            min: 1,
            max: 8,
          }}
          fullWidth
          required
        />

        <FormControl fullWidth required>
          <InputLabel>First Shift Start Time</InputLabel>
          <Select
            value={firstShiftStart}
            onChange={(e) => setFirstShiftStart(e.target.value)}
            label="First Shift Start Time"
          >
            {generateTimeOptions().map((time) => (
              <MenuItem key={time} value={time}>
                {time}
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        <TextField
          label="Shift Duration (hours)"
          type="number"
          value={shiftDuration}
          onChange={(e) => setShiftDuration(parseInt(e.target.value))}
          inputProps={{
            min: 1,
            max: 12,
          }}
          fullWidth
          required
        />

        <TextField
          label="Maximum Team Size"
          type="number"
          value={maxTeamSize}
          onChange={(e) => setMaxTeamSize(parseInt(e.target.value))}
          inputProps={{
            min: 1,
            max: 20,
          }}
          fullWidth
          required
        />

        <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
          <Button
            type="submit"
            variant="contained"
            disabled={saving}
            startIcon={<SaveIcon />}
            sx={{
              bgcolor: 'primary.main',
              '&:hover': {
                bgcolor: 'primary.dark',
              },
            }}
          >
            {saving ? 'Saving...' : 'Save Configuration'}
          </Button>
        </Box>
      </Stack>
    </Box>
  );
};

export default ShiftConfigForm;
