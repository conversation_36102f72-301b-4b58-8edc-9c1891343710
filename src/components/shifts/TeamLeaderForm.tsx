import React, { useState } from 'react';
import { databaseService } from '../../services/database';
import { <PERSON><PERSON><PERSON><PERSON>, NewTeamLeader } from '../../types/shift';
import { getTeamLetter } from '../../types/shift';
import {
  Stack,
  TextField,
  Select,
  MenuItem,
  Paper,
  Typography,
  IconButton,
  Box,
  FormControl,
  InputLabel,
  OutlinedInput,
  Button,
} from '@mui/material';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import SaveIcon from '@mui/icons-material/Save';
import CancelIcon from '@mui/icons-material/Cancel';

interface TeamLeaderFormProps {
  festivalId: string;
  teamLeaders: TeamLeader[];
  onTeamLeaderChange: () => void;
}

const TeamLeaderForm: React.FC<TeamLeaderFormProps> = ({
  festivalId,
  teamLeaders,
  onTeamLeaderChange
}) => {
  const [name, setName] = useState('');
  const [contact, setContact] = useState('');
  const [selectedTeams, setSelectedTeams] = useState<string[]>([]);
  const [saving, setSaving] = useState(false);
  const [editingId, setEditingId] = useState<string | null>(null);

  // Generate available team letters (A-Z)
  const availableTeams = Array.from({ length: 26 }, (_, i) => getTeamLetter(i));

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSaving(true);

    try {
      const newLeader: NewTeamLeader = {
        type: 'team_leader',
        documentType: 'team_leader',
        festivalId,
        name,
        contact: contact || undefined,
        teams: selectedTeams,
        syncStatus: 'local_only'
      };

      await databaseService.addTeamLeader(newLeader);
      onTeamLeaderChange();
      setName('');
      setContact('');
      setSelectedTeams([]);
    } catch (error) {
      console.error('Error saving team leader:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleUpdate = async (leader: TeamLeader) => {
    setSaving(true);
    try {
      await databaseService.updateTeamLeader(leader);
      onTeamLeaderChange();
      setEditingId(null);
    } catch (error) {
      console.error('Error updating team leader:', error);
    } finally {
      setSaving(false);
    }
  };

  const handleDelete = async (id: string) => {
    if (!window.confirm('Are you sure you want to remove this team leader?')) {
      return;
    }

    try {
      await databaseService.deleteTeamLeader(id);
      onTeamLeaderChange();
    } catch (error) {
      console.error('Error deleting team leader:', error);
    }
  };

  return (
    <Stack spacing={4}>
      {/* Add New Team Leader Form */}
      <Box component="form" onSubmit={handleSubmit} sx={{ maxWidth: 'sm' }}>
        <Stack spacing={3}>
          <TextField
            label="Name"
            value={name}
            onChange={(e) => setName(e.target.value)}
            required
            fullWidth
          />

          <TextField
            label="Contact (optional)"
            value={contact}
            onChange={(e) => setContact(e.target.value)}
            fullWidth
          />

          <FormControl fullWidth>
            <InputLabel>Assigned Teams</InputLabel>
            <Select
              multiple
              value={selectedTeams}
              onChange={(e) => {
                const teams = typeof e.target.value === 'string' ? e.target.value.split(',') : e.target.value;
                setSelectedTeams(teams);
              }}
              input={<OutlinedInput label="Assigned Teams" />}
              renderValue={(selected) => selected.map(team => `Team ${team}`).join(', ')}
            >
              {availableTeams.map(team => (
                <MenuItem key={team} value={team}>
                  Team {team}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
            <Button
              type="submit"
              variant="contained"
              disabled={saving}
              startIcon={<SaveIcon />}
            >
              {saving ? 'Saving...' : 'Add Team Leader'}
            </Button>
          </Box>
        </Stack>
      </Box>

      {/* Team Leaders List */}
      <Box>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Team Leaders
        </Typography>
        <Stack spacing={2}>
          {teamLeaders.map((leader) => (
            <Paper
              key={leader._id}
              sx={{
                p: 2,
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
              }}
            >
              {editingId === leader._id ? (
                <Stack spacing={2} sx={{ flex: 1, mr: 2 }}>
                  <TextField
                    value={leader.name}
                    onChange={(e) =>
                      handleUpdate({ ...leader, name: e.target.value })
                    }
                    size="small"
                    fullWidth
                  />
                  <TextField
                    value={leader.contact || ''}
                    onChange={(e) =>
                      handleUpdate({ ...leader, contact: e.target.value })
                    }
                    placeholder="Contact (optional)"
                    size="small"
                    fullWidth
                  />
                  <FormControl fullWidth size="small">
                    <InputLabel>Teams</InputLabel>
                    <Select
                      multiple
                      value={leader.teams}
                      onChange={(e) => {
                        const teams = typeof e.target.value === 'string' ? e.target.value.split(',') : e.target.value;
                        handleUpdate({ ...leader, teams });
                      }}
                      input={<OutlinedInput label="Teams" />}
                      renderValue={(selected) => selected.map(team => `Team ${team}`).join(', ')}
                    >
                      {availableTeams.map(team => (
                        <MenuItem key={team} value={team}>
                          Team {team}
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Stack>
              ) : (
                <Box sx={{ flex: 1 }}>
                  <Typography variant="subtitle1" sx={{ fontWeight: 500 }}>
                    {leader.name}
                  </Typography>
                  {leader.contact && (
                    <Typography variant="body2" color="text.secondary">
                      {leader.contact}
                    </Typography>
                  )}
                  <Typography variant="body2" color="text.secondary" sx={{ mt: 0.5 }}>
                    Teams: {leader.teams.map(team => `Team ${team}`).join(', ') || 'None assigned'}
                  </Typography>
                </Box>
              )}
              <Stack direction="row" spacing={1}>
                <IconButton
                  onClick={() =>
                    editingId === leader._id
                      ? setEditingId(null)
                      : setEditingId(leader._id)
                  }
                  color="primary"
                  size="small"
                >
                  {editingId === leader._id ? <CancelIcon /> : <EditIcon />}
                </IconButton>
                <IconButton
                  onClick={() => handleDelete(leader._id)}
                  color="error"
                  size="small"
                >
                  <DeleteIcon />
                </IconButton>
              </Stack>
            </Paper>
          ))}
        </Stack>
      </Box>
    </Stack>
  );
};

export default TeamLeaderForm;
