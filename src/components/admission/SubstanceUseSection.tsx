import React from 'react';
import {
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Checkbox,
  ListItemText,
  OutlinedInput,
  SelectChangeEvent,
} from '@mui/material';
import { Section, SectionTitle } from './StyledComponents';
import { SubstanceType } from '../../types/admission';

const SUBSTANCE_OPTIONS: SubstanceType[] = [
  'Alcohol',
  'Cannabis',
  'Ecstasy/MDMA',
  'Ketamine',
  'Cocaine',
  'LSD',
  'Mushrooms',
  'GHB',
  'Opiates',
  'Nothing',
  'Other (Please Specify)'
];

interface SubstanceUseSectionProps {
  substancesUsed: SubstanceType[];
  onSelectChange: (e: SelectChangeEvent<unknown>) => void;
}

export const SubstanceUseSection: React.FC<SubstanceUseSectionProps> = ({
  substancesUsed,
  onSelectChange,
}) => {
  return (
    <Section>
      <SectionTitle variant="h6">Substance Use</SectionTitle>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12 }}>
          <FormControl fullWidth>
            <InputLabel>Substances Used</InputLabel>
            <Select
              multiple
              name="SubstanceUsed"
              value={substancesUsed}
              onChange={onSelectChange}
              input={<OutlinedInput label="Substances Used" />}
              renderValue={(selected) => (selected as string[]).join(', ')}
            >
              {SUBSTANCE_OPTIONS.map((substance) => (
                <MenuItem key={substance} value={substance}>
                  <Checkbox checked={substancesUsed.indexOf(substance) > -1} />
                  <ListItemText primary={substance} />
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </Section>
  );
};