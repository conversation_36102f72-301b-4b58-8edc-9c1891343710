import React from 'react';
import {
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  SelectChangeEvent,
} from '@mui/material';
import { Section, SectionTitle } from './StyledComponents';
import { SafeguardingCategory } from '../../types/admission';

const SAFEGUARDING_CATEGORIES: SafeguardingCategory[] = [
  'Sexual Assault',
  'Physical Assault',
  'Emotional Abuse',
  'Other'
];

interface SafeguardingSectionProps {
  category: SafeguardingCategory | undefined;
  notes: string;
  reasonCategory: string;
  onSelectChange: (e: SelectChangeEvent<unknown>) => void;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export const SafeguardingSection: React.FC<SafeguardingSectionProps> = ({
  category,
  notes,
  reasonCategory,
  onSelectChange,
  onInputChange,
}) => {
  const isSafeguarding = reasonCategory === 'Safeguarding';

  if (!isSafeguarding) {
    return null;
  }

  return (
    <Section>
      <SectionTitle variant="h6">Safeguarding Information</SectionTitle>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 6 }}>
          <FormControl 
            fullWidth
            sx={{
              '& .MuiOutlinedInput-root': {
                border: '2px solid #f50057',
                borderRadius: 1,
              }
            }}
          >
            <InputLabel 
              sx={{
                color: '#f50057',
                fontWeight: 'bold'
              }}
            >
              Safeguarding Category (Required)
            </InputLabel>
            <Select
              name="Safeguarding"
              value={category || ''}
              onChange={onSelectChange}
              label="Safeguarding Category (Required)"
              required
            >
              {SAFEGUARDING_CATEGORIES.map(category => (
                <MenuItem key={category} value={category}>
                  {category}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid size={{ xs: 12, md: 6 }}>
          <TextField
            fullWidth
            multiline
            rows={4}
            label="Safeguarding Notes (Required)"
            name="SafeguardingNotes"
            value={notes || ''}
            onChange={onInputChange}
            required
            sx={{
              '& .MuiOutlinedInput-root': {
                border: '2px solid #f50057',
                borderRadius: 1,
              },
              '& .MuiInputLabel-root': {
                color: '#f50057',
                fontWeight: 'bold'
              }
            }}
          />
        </Grid>
      </Grid>
    </Section>
  );
};