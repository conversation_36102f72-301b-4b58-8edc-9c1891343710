import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Box,
  Typography,
  Alert,
} from '@mui/material';

interface AdmissionModalsProps {
  showReAdmitModal: boolean;
  showConfirmReAdmit: boolean;
  showConfirmDischarge: boolean;
  reAdmitLocation: number;
  reAdmitNotes: string;
  dischargeNotes: string;
  dischargeTime: string;
  bayStatus: { isOccupied: boolean; message: string } | null;
  isSubmitting: boolean;
  onReAdmitLocationChange: (value: number) => void;
  onReAdmitNotesChange: (value: string) => void;
  onDischargeNotesChange: (value: string) => void;
  onDischargeTimeChange: (value: string) => void;
  onCloseReAdmit: () => void;
  onCloseConfirmReAdmit: () => void;
  onCloseConfirmDischarge: () => void;
  onConfirmReAdmit: () => void;
  onConfirmDischarge: () => void;
  onShowConfirmReAdmit: () => void;
}

export const AdmissionModals: React.FC<AdmissionModalsProps> = ({
  showReAdmitModal,
  showConfirmReAdmit,
  showConfirmDischarge,
  reAdmitLocation,
  reAdmitNotes,
  dischargeNotes,
  dischargeTime,
  bayStatus,
  isSubmitting,
  onReAdmitLocationChange,
  onReAdmitNotesChange,
  onDischargeNotesChange,
  onDischargeTimeChange,
  onCloseReAdmit,
  onCloseConfirmReAdmit,
  onCloseConfirmDischarge,
  onConfirmReAdmit,
  onConfirmDischarge,
  onShowConfirmReAdmit,
}) => {
  return (
    <>
      {/* Re-Admit Modal */}
      <Dialog open={showReAdmitModal} onClose={onCloseReAdmit}>
        <DialogTitle>Re-Admit Patient</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2, width: '100%', minWidth: 300 }}>
            <TextField
              fullWidth
              type="number"
              label="Location (Bay Number)"
              value={reAdmitLocation}
              onChange={(e) => onReAdmitLocationChange(Number(e.target.value))}
              sx={{ mb: 2 }}
            />
            {bayStatus && (
              <Alert 
                severity={bayStatus.isOccupied ? 'error' : 'success'}
                sx={{ mb: 2 }}
              >
                {bayStatus.message}
              </Alert>
            )}
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Additional Notes"
              value={reAdmitNotes}
              onChange={(e) => onReAdmitNotesChange(e.target.value)}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={onCloseReAdmit}>
            Cancel
          </Button>
          <Button
            onClick={onShowConfirmReAdmit}
            variant="contained"
            disabled={!reAdmitLocation || (bayStatus?.isOccupied ?? false)}
          >
            Re-Admit
          </Button>
        </DialogActions>
      </Dialog>

      {/* Confirm Re-Admit Modal */}
      <Dialog open={showConfirmReAdmit} onClose={onCloseConfirmReAdmit}>
        <DialogTitle>Confirm Re-Admission</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to re-admit this patient?
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={onCloseConfirmReAdmit}>
            Cancel
          </Button>
          <Button
            onClick={onConfirmReAdmit}
            variant="contained"
            color="primary"
          >
            Confirm
          </Button>
        </DialogActions>
      </Dialog>

      {/* Discharge Modal */}
      <Dialog open={showConfirmDischarge} onClose={onCloseConfirmDischarge}>
        <DialogTitle>Discharge Patient</DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 2, width: '100%', minWidth: 300 }}>
            <TextField
              fullWidth
              type="datetime-local"
              label="Discharge Time"
              value={dischargeTime}
              onChange={(e) => onDischargeTimeChange(e.target.value)}
              sx={{ mb: 2 }}
              InputLabelProps={{
                shrink: true,
              }}
            />
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Discharge Notes"
              value={dischargeNotes}
              onChange={(e) => onDischargeNotesChange(e.target.value)}
              required
              error={!dischargeNotes}
              helperText={!dischargeNotes ? "Discharge notes are required" : ""}
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={onCloseConfirmDischarge}>
            Cancel
          </Button>
          <Button
            onClick={onConfirmDischarge}
            variant="contained"
            color="primary"
            disabled={isSubmitting || !dischargeNotes}
          >
            {isSubmitting ? 'Discharging...' : 'Confirm'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};