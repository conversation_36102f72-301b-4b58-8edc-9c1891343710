import React from 'react';
import {
  Grid,
  TextField,
  Typography,
  Box,
} from '@mui/material';
import { Section, SectionTitle } from './StyledComponents';
import { NoteEntry } from '../../types/base';

interface AdmissionNotesSectionProps {
  isEdit: boolean;
  admissionNotes: string;
  additionalNotes: NoteEntry[];
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  onNewNoteChange?: (e: React.ChangeEvent<HTMLInputElement>) => void; // Handler for new note field
  newNoteValue?: string; // Add prop for new note value
}

export const AdmissionNotesSection: React.FC<AdmissionNotesSectionProps> = ({
  isEdit,
  admissionNotes,
  additionalNotes,
  onInputChange,
  onNewNoteChange,
  newNoteValue = '', // Default to empty string if not provided
}) => {
  // Display existing notes in a read-only format
  const existingNotesText = additionalNotes
    .map(note => `${new Date(note.timestamp).toLocaleString()} - ${note.author}: ${note.note}`)
    .join('\n');

  return (
    <Section>
      <SectionTitle variant="h6">Admission Details</SectionTitle>
      <Grid container spacing={3}>
        {!isEdit && (
          <Grid size={{ xs: 12 }}>
            <TextField
              fullWidth
              multiline
              rows={4}
              label="Admission Notes"
              name="AdmissionNotes"
              value={admissionNotes}
              onChange={onInputChange}
            />
          </Grid>
        )}
        
        {isEdit && (
          <>
            {/* Display original admission notes if they exist */}
            {admissionNotes && (
              <Grid size={{ xs: 12 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>Original Admission Notes:</Typography>
                <Box
                  sx={{
                    p: 2,
                    mb: 2,
                    bgcolor: 'background.paper',
                    border: '1px solid #ddd',
                    borderRadius: 1,
                    maxHeight: '200px',
                    overflowY: 'auto',
                    whiteSpace: 'pre-line'
                  }}
                >
                  {admissionNotes}
                </Box>
              </Grid>
            )}
            
            {/* Display additional notes if they exist */}
            {additionalNotes.length > 0 && (
              <Grid size={{ xs: 12 }}>
                <Typography variant="subtitle2" sx={{ mb: 1 }}>Additional Notes:</Typography>
                <Box
                  sx={{
                    p: 2,
                    mb: 2,
                    bgcolor: 'background.paper',
                    border: '1px solid #ddd',
                    borderRadius: 1,
                    maxHeight: '200px',
                    overflowY: 'auto',
                    whiteSpace: 'pre-line'
                  }}
                >
                  {existingNotesText}
                </Box>
              </Grid>
            )}
            
            {/* Add new note field - controlled input */}
            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Add New Note"
                name="newNote"
                value={newNoteValue}
                onChange={onNewNoteChange || onInputChange}
                placeholder="Enter new note here..."
                inputProps={{
                  autoComplete: 'off' // Disable autocomplete to prevent browser interference
                }}
              />
            </Grid>
          </>
        )}
      </Grid>
    </Section>
  );
};