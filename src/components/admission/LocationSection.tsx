import React from 'react';
import {
  Grid,
  TextField,
  FormControlLabel,
  Switch,
  MenuItem,
  Typography,
  FormControl,
  InputLabel,
  Select,
  Box,
} from '@mui/material';
import { Section, SectionTitle } from './StyledComponents';
import { BaysOrChairs } from '../../types/admission';
import { useSiteLocation } from '../../contexts/SiteLocationContext';

interface LocationSectionProps {
  location: number | undefined;
  baysOrChairs: BaysOrChairs;
  inBayNow: boolean;
  bayStatus: { isOccupied: boolean; message: string } | null;
  onLocationChange: (value: number | undefined) => void;
  onBaysOrChairsChange: (value: BaysOrChairs) => void;
  onInBayNowChange: (value: boolean) => void;
}

export const LocationSection: React.FC<LocationSectionProps> = ({
  location,
  baysOrChairs,
  inBayNow,
  bayStatus,
  onLocationChange,
  onBaysOrChairsChange,
  onInBayNowChange,
}) => {
  const { activeSiteLocation } = useSiteLocation();

  return (
    <Section>
      <SectionTitle variant="h6">Location Details</SectionTitle>
      <Grid container spacing={3}>
        {activeSiteLocation && (
          <Grid size={{ xs: 12 }}>
            <Typography variant="body1" color="textSecondary">
              Site Location: {activeSiteLocation.type === 'arena' ? 'Arena' : 'Campsite'}
            </Typography>
          </Grid>
        )}
        <Grid size={{ xs: 12, sm: 4 }}>
          <TextField
            fullWidth
            type="number"
            label="Bay/Chair Number"
            value={location || ''}
            onChange={(e) => onLocationChange(e.target.value ? Number(e.target.value) : undefined)}
          />
          {baysOrChairs === 'Bay' && location && bayStatus && (
            <Typography
              sx={{
                mt: 1,
                color: bayStatus.isOccupied ? 'error.main' : 'success.main',
                fontSize: '0.875rem',
              }}
            >
              {bayStatus.message}
            </Typography>
          )}
        </Grid>
        <Grid size={{ xs: 12, sm: 4 }}>
          <FormControl fullWidth>
            <InputLabel>Type</InputLabel>
            <Select
              value={baysOrChairs}
              label="Type"
              onChange={(e) => onBaysOrChairsChange(e.target.value as BaysOrChairs)}
            >
              <MenuItem value="Bay">Bay</MenuItem>
              <MenuItem value="Chair">Chair</MenuItem>
              <MenuItem value="Outside">Outside</MenuItem>
            </Select>
          </FormControl>
        </Grid>
        <Grid size={{ xs: 12, sm: 4 }}>
          <Box sx={{ mt: 2 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={inBayNow}
                  onChange={(e) => onInBayNowChange(e.target.checked)}
                />
              }
              label={`Currently in ${baysOrChairs}`}
            />
          </Box>
        </Grid>
      </Grid>
    </Section>
  );
};