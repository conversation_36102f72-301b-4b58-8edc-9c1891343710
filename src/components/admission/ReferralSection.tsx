import React from 'react';
import {
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import { Section, SectionTitle } from './StyledComponents';

const REFERRAL_OPTIONS = [
  'Self Referral',
  'Medical',
  'Security',
  'Police',
  'Friend',
  'Staff',
  'Other'
];

interface ReferralSectionProps {
  referredBy: string;
  reasonCategory: string;
  onSelectChange: (e: SelectChangeEvent<unknown>) => void;
}

export const ReferralSection: React.FC<ReferralSectionProps> = ({
  referredBy,
  reasonCategory,
  onSelectChange,
}) => {
  return (
    <Section>
      <SectionTitle variant="h6">Referral Information</SectionTitle>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 6 }}>
          <FormControl fullWidth>
            <InputLabel>Referred By</InputLabel>
            <Select
              name="ReferredBy"
              value={referredBy}
              onChange={onSelectChange}
              label="Referred By"
            >
              {REFERRAL_OPTIONS.map(option => (
                <MenuItem key={option} value={option}>
                  {option}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Grid>
        <Grid size={{ xs: 12, md: 6 }}>
          <FormControl fullWidth>
            <InputLabel>Reason Category</InputLabel>
            <Select
              name="ReasonCategory"
              value={reasonCategory}
              onChange={onSelectChange}
              label="Reason Category"
            >
              <MenuItem value="Substance Use">Substance Use</MenuItem>
              <MenuItem value="Mental Health">Mental Health</MenuItem>
              <MenuItem value="Physical Health">Physical Health</MenuItem>
              <MenuItem value="Safeguarding">Safeguarding</MenuItem>
              <MenuItem value="Other">Other</MenuItem>
            </Select>
          </FormControl>
        </Grid>
      </Grid>
    </Section>
  );
};