import React from 'react';
import {
  Grid,
  TextField,
} from '@mui/material';
import { Section, SectionTitle } from './StyledComponents';

interface PhysicalDescriptionSectionProps {
  hairColour: string;
  hairStyle: string;
  clothingTop: string;
  clothingBottom: string;
  footwear: string;
  otherFeatures: string;
  onInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

export const PhysicalDescriptionSection: React.FC<PhysicalDescriptionSectionProps> = ({
  hairColour,
  hairStyle,
  clothingTop,
  clothingBottom,
  footwear,
  otherFeatures,
  onInputChange,
}) => {
  return (
    <Section>
      <SectionTitle variant="h6">Physical Description</SectionTitle>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <TextField
            fullWidth
            label="Hair Colour"
            name="HairColour"
            value={hairColour}
            onChange={onInputChange}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <TextField
            fullWidth
            label="Hair Style"
            name="HairStyle"
            value={hairStyle}
            onChange={onInputChange}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <TextField
            fullWidth
            label="Clothing Top"
            name="ClothingTop"
            value={clothingTop}
            onChange={onInputChange}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <TextField
            fullWidth
            label="Clothing Bottom"
            name="ClothingBottom"
            value={clothingBottom}
            onChange={onInputChange}
          />
        </Grid>
        <Grid size={{ xs: 12, sm: 6, md: 4 }}>
          <TextField
            fullWidth
            label="Footwear"
            name="Footwear"
            value={footwear}
            onChange={onInputChange}
          />
        </Grid>
        <Grid size={{ xs: 12 }}>
          <TextField
            fullWidth
            multiline
            rows={2}
            label="Other Features"
            name="OtherFeatures"
            value={otherFeatures}
            onChange={onInputChange}
          />
        </Grid>
      </Grid>
    </Section>
  );
};