import React, { useState, useEffect, useMemo } from 'react';
import { ItemCategory, LostPropertyItem, NewLostPropertyItem } from '../types/item';
import { databaseService } from '../services/database/index';
import { useFestival } from '../contexts/FestivalContext';
import { useSmartLostProperty } from '../hooks/useSmartData';
import {
  Container,
  Grid,
  Paper,
  Typography,
  TextField,
  RadioGroup,
  Radio,
  FormControlLabel,
  Select,
  MenuItem,
  Button,
  Box,
  Stack,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Alert,
  InputAdornment,
  Divider,
  Chip,
} from '@mui/material';
import { styled } from '@mui/material/styles';
import SearchIcon from '@mui/icons-material/Search';
import CachedIcon from '@mui/icons-material/Cached';
import {
  DataGrid,
  GridColDef,
  GridToolbar,
} from '@mui/x-data-grid';
import { RecordDetailsModal } from './reports/shared/RecordDetailsModal';

const categories: ItemCategory[] = [
  'Phone',
  'Passport',
  'Keys',
  'Bag',
  'Sanity',
  'Driving License',
  'Medication',
  'ID/Cards',
  'Glasses',
  'Camera',
  'Tickets',
  'Cuddly Toy',
  'Watch/Jewellery',
  'Headphone',
  'Wallet',
  'Other'
];

// Maximum height for the items list to make it scrollable
const MAX_LIST_HEIGHT = 655;

export const LostPropertyPage: React.FC = () => {
  const { activeFestival } = useFestival();
  const [selectedCategory, setSelectedCategory] = useState<ItemCategory | null>(null);
  const [quickDescription, setQuickDescription] = useState('');
  const [timeFoundDate, setTimeFoundDate] = useState(new Date().toISOString().split('T')[0]);
  const [timeFoundTime, setTimeFoundTime] = useState(new Date().toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' }));
  const [foundBy, setFoundBy] = useState('');
  const [whereFound, setWhereFound] = useState('');
  const [description, setDescription] = useState('');
  const [itemReturned, setItemReturned] = useState<string | null>(null);
  const [currentEditingItem, setCurrentEditingItem] = useState<LostPropertyItem | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<LostPropertyItem | null>(null);

  // Use smart data hook for caching
  const lostPropertyResult = useSmartLostProperty({
    onError: (error) => console.error('Error loading lost property items:', error)
  });

  // Derived state from smart data hook
  const currentItems = lostPropertyResult.data || [];
  const isLoading = lostPropertyResult.isLoading;
  const error = lostPropertyResult.error;
  const isFromCache = lostPropertyResult.isFromCache;

  const handleRefresh = async () => {
    await lostPropertyResult.refetch();
  };

  useEffect(() => {
    if (activeFestival) {
      console.log('Festival changed, clearing form...');
      clearForm();
    }
  }, [activeFestival?._id]);

  const loadItemDetails = (item: LostPropertyItem) => {
    try {
      setSelectedCategory(item.category as ItemCategory);
      setQuickDescription(item.quickDescription);
      const timeFound = new Date(item.timeFound);
      setTimeFoundDate(timeFound.toISOString().split('T')[0]);
      setTimeFoundTime(timeFound.toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' }));
      setFoundBy(item.foundBy);
      setWhereFound(item.whereFound);
      setDescription(item.description);
      setItemReturned(item.itemReturned || '');
      setCurrentEditingItem(item);
    } catch (error) {
      console.error('Error loading item details:', error);
    }
  };

  const handleItemReturn = async () => {
    if (!currentEditingItem) return;

    try {
      const now = new Date().toISOString();
      const updatedItem: LostPropertyItem = {
        ...currentEditingItem,
        itemReturned: now,
        status: 'claimed'
      };

      await databaseService.updateLostPropertyItem(updatedItem);
      await handleRefresh();
      
      // Update the form field with the new date
      setItemReturned(now);
      
      // Don't clear form or reset currentEditingItem so user can see the updated state
      // and potentially unmark if needed
    } catch (error) {
      console.error('Error updating item return status:', error);
    }
  };

  const handleItemUnreturn = async () => {
    if (!currentEditingItem) return;

    try {
      const updatedItem: LostPropertyItem = {
        ...currentEditingItem,
        itemReturned: undefined,
        status: 'unclaimed'
      };

      await databaseService.updateLostPropertyItem(updatedItem);
      await handleRefresh();
      
      // Update the form field
      setItemReturned(null);
      
      // Don't clear form or reset currentEditingItem
    } catch (error) {
      console.error('Error unmarking item return status:', error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!selectedCategory || !activeFestival) return;

    try {
      const timeFound = new Date(`${timeFoundDate}T${timeFoundTime}`).toISOString();

      const newItem: Omit<LostPropertyItem, '_id' | '_rev'> = {
        type: 'lost_property',
        documentType: 'lost_property',
        category: selectedCategory,
        quickDescription,
        timeFound,
        foundBy,
        whereFound,
        description,
        itemReturned: undefined,
        festivalId: activeFestival?._id || '',
        syncStatus: 'sync_pending',
        status: 'unclaimed'
      };

      if (currentEditingItem) {
        await databaseService.updateLostPropertyItem({
          ...currentEditingItem,
          ...newItem
        });
      } else {
        await databaseService.addLostPropertyItem(newItem);
      }
      
      clearForm();
      await handleRefresh();

      try {
        await databaseService.manualSync();
      } catch (error) {
        console.error('Error syncing after save:', error);
      }
    } catch (error) {
      console.error('Error saving lost property item:', error);
    }
  };

  const clearForm = () => {
    setSelectedCategory(null);
    setQuickDescription('');
    setTimeFoundDate(new Date().toISOString().split('T')[0]);
    setTimeFoundTime(new Date().toLocaleTimeString('en-GB', { hour: '2-digit', minute: '2-digit' }));
    setFoundBy('');
    setWhereFound('');
    setDescription('');
    setItemReturned(null);
    setCurrentEditingItem(null);
  };

  const handleRowClick = (record: LostPropertyItem) => {
    setSelectedRecord(record);
    setDetailsModalOpen(true);
  };

  const handleCloseModal = () => {
    setDetailsModalOpen(false);
  };

  const filteredItems = useMemo(() => {
    if (!searchQuery.trim()) return currentItems;

    const query = searchQuery.toLowerCase().trim();
    return currentItems.filter(item => {
      const searchableFields = [
        item.category,
        item.quickDescription,
        item.description,
        item.whereFound,
        item.foundBy
      ].map(field => (field || '').toLowerCase());

      return searchableFields.some(field => field.includes(query));
    });
  }, [currentItems, searchQuery]);

  if (!activeFestival) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <Typography color="text.secondary">
          Please select a festival to manage lost property items.
        </Typography>
      </Box>
    );
  }

  // Define columns for the DataGrid
  const columns: GridColDef[] = [
    {
      field: 'category',
      headerName: 'Category',
      flex: 1,
      sortable: true,
      filterable: true,
    },
    {
      field: 'quickDescription',
      headerName: 'Description',
      flex: 2,
      sortable: true,
      filterable: true,
    },
    {
      field: 'timeFound',
      headerName: 'Time Found',
      flex: 1,
      sortable: true,
      filterable: true,
      type: 'dateTime',
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object' || !('timeFound' in params.row)) return null;
        return params.row.timeFound ? new Date(params.row.timeFound) : null;
      },
      renderCell: (params) => {
        if (!params || !params.row || typeof params.row !== 'object' || !('timeFound' in params.row)) return 'N/A';
        return params.row.timeFound ? new Date(params.row.timeFound).toLocaleString() : 'N/A';
      },
    },
    {
      field: 'foundBy',
      headerName: 'Found By',
      flex: 1,
      sortable: true,
      filterable: true,
    },
    {
      field: 'whereFound',
      headerName: 'Where Found',
      flex: 1,
      sortable: true,
      filterable: true,
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
      sortable: true,
      filterable: true,
      renderCell: (params) => {
        return params.row.status === 'claimed' ? 'Claimed' : 'Unclaimed';
      },
    },
  ];

  return (
    <Container maxWidth={false} sx={{ p: 3 }}>
      <Stack spacing={4}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <Box>
            <Typography variant="h4" sx={{ mb: 1 }}>
              Lost Property - {activeFestival.name}
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              {isFromCache && (
                <Chip
                  icon={<CachedIcon />}
                  label="Cached Data"
                  size="small"
                  color="info"
                  variant="outlined"
                />
              )}
              <Typography variant="body2" color="text.secondary">
                {new Date(activeFestival.startDate).toLocaleDateString()} - {new Date(activeFestival.endDate).toLocaleDateString()}
              </Typography>
            </Box>
          </Box>
          <Button
            variant="outlined"
            onClick={handleRefresh}
            disabled={isLoading}
            startIcon={<CachedIcon />}
          >
            Refresh
          </Button>
        </Box>

        {error && (
          <Alert severity="error">
            {error}
          </Alert>
        )}

        <Grid container spacing={3}>
          <Grid size={{ xs: 12, md: 8 }}>
            <Paper elevation={1} sx={{ p: 3 }}>
              <form onSubmit={handleSubmit}>
                <Stack spacing={3}>
                  <Box>
                    <Typography variant="subtitle2" sx={{ mb: 1 }}>
                      Category
                    </Typography>
                    <RadioGroup
                      row
                      value={selectedCategory || ''}
                      onChange={(e) => setSelectedCategory(e.target.value as ItemCategory)}
                    >
                      <Grid container spacing={1}>
                        {categories.map((category) => (
                          <Grid size={{ xs: 6, sm: 4 }} key={category}>
                            <FormControlLabel
                              value={category}
                              control={<Radio />}
                              label={<Typography variant="body2">{category}</Typography>}
                            />
                          </Grid>
                        ))}
                      </Grid>
                    </RadioGroup>
                  </Box>

                  <Grid container spacing={2}>
                    <Grid size={{ xs: 12, md: 6 }}>
                      <TextField
                        fullWidth
                        label="Quick Description"
                        value={quickDescription}
                        onChange={(e) => setQuickDescription(e.target.value)}
                        placeholder="Quick Description of item"
                      />
                    </Grid>

                    <Grid size={{ xs: 6, md: 3 }}>
                      <TextField
                        fullWidth
                        type="date"
                        label="Date Found"
                        value={timeFoundDate}
                        onChange={(e) => setTimeFoundDate(e.target.value)}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>

                    <Grid size={{ xs: 6, md: 3 }}>
                      <TextField
                        fullWidth
                        type="time"
                        label="Time Found"
                        value={timeFoundTime}
                        onChange={(e) => setTimeFoundTime(e.target.value)}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>

                    <Grid size={{ xs: 12, md: 6 }}>
                      <FormControl fullWidth>
                        <InputLabel>Found By</InputLabel>
                        <Select
                          value={foundBy}
                          onChange={(e) => setFoundBy(e.target.value)}
                          label="Found By"
                        >
                          <MenuItem value="">Select who found it</MenuItem>
                          <MenuItem value="CREW">CREW</MenuItem>
                          <MenuItem value="SECURITY">SECURITY</MenuItem>
                          <MenuItem value="PUBLIC">PUBLIC</MenuItem>
                        </Select>
                      </FormControl>
                    </Grid>

                    <Grid size={{ xs: 12, md: 6 }}>
                      <TextField
                        fullWidth
                        label="Where Found"
                        value={whereFound}
                        onChange={(e) => setWhereFound(e.target.value)}
                        placeholder="Where was the item found?"
                      />
                    </Grid>
                  </Grid>

                  <TextField
                    fullWidth
                    multiline
                    rows={4}
                    label="Description"
                    value={description}
                    onChange={(e) => setDescription(e.target.value)}
                    placeholder="Please add further details here"
                  />

                  <Grid container spacing={2}>
                    <Grid size={{ xs: 12 }}>
                      <TextField
                        fullWidth
                        type="datetime-local"
                        label="Item Returned"
                        value={itemReturned || ''}
                        onChange={(e) => setItemReturned(e.target.value || null)}
                        InputLabelProps={{ shrink: true }}
                      />
                    </Grid>
                  </Grid>

                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'space-between' }}>
                    <Button
                      type="submit"
                      variant="contained"
                      color="primary"
                    >
                      {currentEditingItem ? 'Update Item' : 'Add Item'}
                    </Button>
                    {currentEditingItem && (
                      <>
                        <Button
                          variant="outlined"
                          color="inherit"
                          onClick={() => {
                            clearForm();
                            setCurrentEditingItem(null);
                          }}
                        >
                          Cancel Edit
                        </Button>
                        {currentEditingItem.itemReturned ? (
                          <Button
                            variant="contained"
                            color="warning"
                            onClick={handleItemUnreturn}
                          >
                            Unmark as Returned
                          </Button>
                        ) : (
                          <Button
                            variant="contained"
                            color="success"
                            onClick={handleItemReturn}
                          >
                            Mark as Returned
                          </Button>
                        )}
                      </>
                    )}
                  </Box>
                </Stack>
              </form>
            </Paper>
          </Grid>

          <Grid size={{ xs: 12, md: 4 }}>
            <Paper elevation={1} sx={{ p: 3 }}>
              <Box sx={{ mb: 3 }}>
                <Typography variant="h6" sx={{ mb: 2 }}>
                  {filteredItems.length} Current items
                </Typography>
                <TextField
                  fullWidth
                  placeholder="Search items..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  InputProps={{
                    startAdornment: (
                      <InputAdornment position="start">
                        <SearchIcon />
                      </InputAdornment>
                    ),
                  }}
                  sx={{ mb: 2 }}
                />
              </Box>
              <Box sx={{ height: MAX_LIST_HEIGHT, overflowY: 'auto', pr: 1 }}>
                <Stack spacing={2}>
                {filteredItems.map((item) => (
                  <Card
                    key={item._id}
                    variant="outlined"
                    onClick={() => loadItemDetails(item)}
                    sx={{
                      cursor: 'pointer',
                      '&:hover': {
                        bgcolor: 'action.hover',
                      },
                      ...(currentEditingItem?._id === item._id && {
                        borderColor: 'primary.main',
                        bgcolor: 'primary.lighter',
                      }),
                    }}
                  >
                    <CardContent>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start' }}>
                        <Stack spacing={0.5}>
                          <Typography variant="subtitle2">
                            {item.category} - {new Date(item.timeFound).toLocaleDateString('en-GB', { hour: '2-digit', minute: '2-digit' })}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            {item.quickDescription}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Found by: {item.foundBy}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            Location: {item.whereFound}
                          </Typography>
                        </Stack>
                      </Box>
                      {item.itemReturned && (
                        <Typography
                          variant="body2"
                          color="success.main"
                          sx={{ mt: 1 }}
                        >
                          Returned: {new Date(item.itemReturned).toLocaleDateString()}
                        </Typography>
                      )}
                    </CardContent>
                  </Card>
                ))}
              </Stack>
            </Box>
          </Paper>
          </Grid>
        </Grid>

        {/* Data Table Section */}
        <Paper elevation={1} sx={{ p: 3, mt: 4 }}>
          <Box sx={{ mb: 3 }}>
            <Typography variant="h5" sx={{ mb: 1 }}>
              Lost Property Items Table
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Search, filter, and sort all lost property items
            </Typography>
          </Box>
          <div style={{ height: 500, width: '100%' }}>
            <DataGrid
              rows={currentItems}
              columns={columns}
              getRowId={(row) => row._id}
              density="standard"
              slots={{ toolbar: GridToolbar }}
              slotProps={{
                toolbar: {
                  showQuickFilter: true,
                  quickFilterProps: { debounceMs: 300 },
                },
              }}
              initialState={{
                sorting: {
                  sortModel: [{ field: 'timeFound', sort: 'desc' }],
                },
                pagination: {
                  paginationModel: { pageSize: 10 },
                },
              }}
              pageSizeOptions={[10, 25, 50, 100]}
              onRowClick={(params) => handleRowClick(params.row)}
              sx={{
                '& .MuiDataGrid-cell': {
                  fontSize: '0.875rem',
                },
                '& .MuiDataGrid-toolbarContainer': {
                  padding: '8px 24px',
                },
                '& .MuiDataGrid-toolbar': {
                  '& .MuiFormControl-root': {
                    width: '100%',
                    maxWidth: '300px',
                  },
                },
                '& .MuiDataGrid-row': {
                  cursor: 'pointer',
                },
                border: 'none',
              }}
            />
          </div>
        </Paper>
      </Stack>

      {/* Details Modal */}
      <RecordDetailsModal
        open={detailsModalOpen}
        onClose={handleCloseModal}
        record={selectedRecord}
        recordType="lostProperty"
      />
    </Container>
  );
};
