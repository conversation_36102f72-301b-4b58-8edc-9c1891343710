import React, { Component, ErrorInfo, ReactNode } from 'react';
import {
  Paper,
  Typography,
  Button,
  Alert,
  Box,
} from '@mui/material';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

export class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false
  };

  public static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Uncaught error:', error, errorInfo);
  }

  public render() {
    if (this.state.hasError) {
      return (
        <Paper
          elevation={0}
          sx={{
            p: 3,
            bgcolor: 'error.light',
            border: 1,
            borderColor: 'error.main',
            borderRadius: 1,
          }}
        >
          <Typography
            variant="h6"
            color="error.dark"
            sx={{ mb: 1, fontWeight: 600 }}
          >
            Something went wrong
          </Typography>
          
          <Alert severity="error" sx={{ mb: 2 }}>
            {this.state.error?.message}
          </Alert>

          <Box sx={{ mt: 2 }}>
            <Button
              variant="contained"
              color="error"
              onClick={() => this.setState({ hasError: false })}
              sx={{
                '&:hover': {
                  bgcolor: 'error.dark',
                },
              }}
            >
              Try again
            </Button>
          </Box>
        </Paper>
      );
    }

    return this.props.children;
  }
}
