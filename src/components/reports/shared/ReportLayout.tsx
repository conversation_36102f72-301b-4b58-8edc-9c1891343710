import React, { useState } from 'react';
import { 
  Container, 
  Typography, 
  Box, 
  CircularProgress, 
  Alert,
  Paper,
  IconButton,
  Tooltip
} from '@mui/material';
import PictureAsPdfIcon from '@mui/icons-material/PictureAsPdf';
import DownloadIcon from '@mui/icons-material/Download';
import { Festival } from '../../../types/festival';
import { DaySelector } from '../shared/DaySelector';
import { TabSelector, ReportTab } from '../shared/TabSelector';
import { PDFReport, generatePDF } from './PDFReport';
import { WelfareAdmission } from '../../../types/admission';
import { ItemDocument, LostPropertyItem } from '../../../types/item';
import { SensoryHubVisit } from '../../../types/sensory-hub';

interface ReportLayoutProps {
  festival: Festival;
  selectedDay: string;
  onDayChange: (day: string) => void;
  activeTab: ReportTab;
  onTabChange: (tab: ReportTab) => void;
  loading?: boolean;
  error?: string | null;
  children: React.ReactNode;
  data?: {
    admissions: WelfareAdmission[];
    itemCounts: ItemDocument[];
    lostPropertyItems: LostPropertyItem[];
    sensoryHubVisits: SensoryHubVisit[];
  };
}

export const ReportLayout: React.FC<ReportLayoutProps> = ({
  festival,
  selectedDay,
  onDayChange,
  activeTab,
  onTabChange,
  loading,
  error,
  children,
  data
}) => {
  const [pdfPreviewOpen, setPdfPreviewOpen] = useState(false);

  const handleGeneratePDF = async () => {
    if (!data) return;
    await generatePDF(
      data.admissions,
      data.itemCounts,
      data.lostPropertyItems,
      data.sensoryHubVisits
    );
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={2}>
        <Alert severity="error">Error: {error}</Alert>
      </Box>
    );
  }

  return (
    <Paper 
      elevation={0}
      sx={{ 
        minHeight: '100vh',
        bgcolor: 'background.paper',
        backdropFilter: 'blur(8px)',
        py: 4
      }}
    >
      <Container maxWidth="xl">
        <Box mb={4}>
          <Typography variant="h4" color="text.primary" gutterBottom>
            Reports for {festival.name}
          </Typography>
          <Box display="flex" alignItems="center" gap={2} mt={1}>
            <Typography variant="body2" color="text.secondary">
              {new Date(festival.startDate).toLocaleDateString()} - {new Date(festival.endDate).toLocaleDateString()}
            </Typography>
            <DaySelector
              startDate={festival.startDate}
              endDate={festival.endDate}
              selectedDay={selectedDay}
              onChange={onDayChange}
            />
          </Box>
        </Box>

        <Box 
          display="flex" 
          justifyContent="space-between" 
          alignItems="center" 
          mb={3}
        >
          <Typography variant="h5" color="primary">
            Reports
          </Typography>
          <Box display="flex" alignItems="center" gap={2}>
            <TabSelector activeTab={activeTab} onTabChange={onTabChange} />
            <Box>
              <Tooltip title="Preview PDF">
                <IconButton 
                  color="primary"
                  onClick={() => setPdfPreviewOpen(true)}
                  disabled={!data}
                >
                  <PictureAsPdfIcon />
                </IconButton>
              </Tooltip>
              <Tooltip title="Download PDF">
                <IconButton 
                  color="primary"
                  onClick={handleGeneratePDF}
                  disabled={!data}
                >
                  <DownloadIcon />
                </IconButton>
              </Tooltip>
            </Box>
          </Box>
        </Box>

        {children}

        {data && (
          <PDFReport
            admissions={data.admissions}
            itemCounts={data.itemCounts}
            lostPropertyItems={data.lostPropertyItems}
            sensoryHubVisits={data.sensoryHubVisits}
            open={pdfPreviewOpen}
            onClose={() => setPdfPreviewOpen(false)}
          />
        )}
      </Container>
    </Paper>
  );
};
