import React from 'react';
import { Select, MenuItem, FormControl, SelectChangeEvent } from '@mui/material';
import { eachDayOfInterval, parseISO, format } from 'date-fns';

interface DaySelectorProps {
  startDate: string;
  endDate: string;
  selectedDay: string;
  onChange: (day: string) => void;
}

export const DaySelector: React.FC<DaySelectorProps> = ({
  startDate,
  endDate,
  selectedDay,
  onChange
}) => {
  const handleChange = (event: SelectChangeEvent<string>) => {
    onChange(event.target.value);
  };

  return (
    <FormControl 
      size="small"
      sx={{ 
        minWidth: 200,
        ml: 2,
        '& .MuiOutlinedInput-root': {
          bgcolor: 'background.paper',
          '&:hover .MuiOutlinedInput-notchedOutline': {
            borderColor: 'primary.main',
          },
        },
        '& .MuiSelect-select': {
          py: 1,
        },
      }}
    >
      <Select
        value={selectedDay}
        onChange={handleChange}
        displayEmpty
        variant="outlined"
      >
        <MenuItem value="all">All Days</MenuItem>
        {eachDayOfInterval({
          start: parseISO(startDate),
          end: parseISO(endDate)
        }).map((day, index) => (
          <MenuItem 
            key={day.toISOString()} 
            value={day.toISOString()}
          >
            Day {index + 1} - {format(day, 'MMM d, yyyy')}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};
