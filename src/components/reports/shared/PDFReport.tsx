import React from 'react';
import {
  Document,
  Page,
  Text,
  View,
  StyleSheet,
  PDFViewer,
  pdf,
  Image,
} from '@react-pdf/renderer';
import { WelfareAdmission } from '../../../types/admission';
import { ItemDocument } from '../../../types/item';
import { LostPropertyItem } from '../../../types/item';
import { SensoryHubVisit } from '../../../types/sensory-hub';
import {
  getAdmissionsByReason,
  getAgeDistribution,
  getItemCountsByType,
  getLostPropertyByStatus,
  getSensoryHubVisitsByPurpose,
  getSensoryHubVisitsByUserType,
  getSensoryHubVisitsByTeam,
} from '../../../utils/reportDataProcessing';
import { Modal, Box } from '@mui/material';

interface PDFReportProps {
  admissions: WelfareAdmission[];
  itemCounts: ItemDocument[];
  lostPropertyItems: LostPropertyItem[];
  sensoryHubVisits: SensoryHubVisit[];
  open: boolean;
  onClose: () => void;
}

const styles = StyleSheet.create({
  page: {
    padding: 30,
  },
  header: {
    marginBottom: 30,
    borderBottom: '2px solid #662D91',
    paddingBottom: 10,
    alignItems: 'center',
  },
  logo: {
    width: 200,
    marginBottom: 10,
  },
  section: {
    marginBottom: 20,
  },
  title: {
    fontSize: 18,
    marginBottom: 10,
    color: '#662D91',
  },
  sectionSubtitle: {
    fontSize: 14,
    marginBottom: 5,
    color: '#662D91',
  },
  row: {
    flexDirection: 'row',
    paddingVertical: 3,
  },
  cell: {
    flex: 1,
  },
  text: {
    fontSize: 12,
  },
  footer: {
    position: 'absolute',
    bottom: 30,
    left: 30,
    right: 30,
    textAlign: 'center',
    color: '#662D91',
    fontSize: 10,
    borderTop: '1px solid #662D91',
    paddingTop: 10,
  },
});

const PDFDocument: React.FC<Omit<PDFReportProps, 'open' | 'onClose'>> = ({
  admissions,
  itemCounts,
  lostPropertyItems,
  sensoryHubVisits,
}) => {
  const admissionReasons = getAdmissionsByReason(admissions);
  const ageDistribution = getAgeDistribution(admissions);
  const itemsByType = getItemCountsByType(itemCounts);
  const lostPropertyStatus = getLostPropertyByStatus(lostPropertyItems);
  const sensoryHubPurpose = getSensoryHubVisitsByPurpose(sensoryHubVisits);
  const sensoryHubUserType = getSensoryHubVisitsByUserType(sensoryHubVisits);
  const sensoryHubTeams = getSensoryHubVisitsByTeam(sensoryHubVisits);
  const currentDate = new Date().toLocaleDateString();

  return (
    <Document>
      <Page size="A4" style={styles.page}>
        {/* Header with iTHINK logo */}
        <View style={styles.header}>
          <Image 
            src="/ithinklogo.png"
            style={styles.logo}
          />
        </View>

        {/* Admissions Section */}
        <View style={styles.section}>
          <Text style={styles.title}>Admissions Report</Text>
          
          <Text style={styles.sectionSubtitle}>Admission Reasons</Text>
          {admissionReasons.map((item, index) => (
            <View style={styles.row} key={index}>
              <Text style={[styles.cell, styles.text]}>{item.reason}</Text>
              <Text style={[styles.cell, styles.text]}>{item.count}</Text>
            </View>
          ))}

          <Text style={[styles.sectionSubtitle, { marginTop: 10 }]}>Age Distribution</Text>
          {ageDistribution.map((item, index) => (
            <View style={styles.row} key={index}>
              <Text style={[styles.cell, styles.text]}>{item.ageGroup}</Text>
              <Text style={[styles.cell, styles.text]}>{item.count}</Text>
            </View>
          ))}
        </View>

        {/* Front of House Section */}
        <View style={styles.section}>
          <Text style={styles.title}>Front of House Report</Text>
          {itemsByType.map((item, index) => (
            <View style={styles.row} key={index}>
              <Text style={[styles.cell, styles.text]}>{item.type}</Text>
              <Text style={[styles.cell, styles.text]}>{item.count}</Text>
            </View>
          ))}
        </View>

        {/* Lost Property Section */}
        <View style={styles.section}>
          <Text style={styles.title}>Lost Property Report</Text>
          {lostPropertyStatus.map((item, index) => (
            <View style={styles.row} key={index}>
              <Text style={[styles.cell, styles.text]}>{item.status}</Text>
              <Text style={[styles.cell, styles.text]}>{item.count}</Text>
            </View>
          ))}
        </View>

        {/* Sensory Hub Section */}
        <View style={styles.section}>
          <Text style={styles.title}>Sensory Hub Report</Text>
          
          <Text style={styles.sectionSubtitle}>Visit Purpose</Text>
          {sensoryHubPurpose.map((item, index) => (
            <View style={styles.row} key={index}>
              <Text style={[styles.cell, styles.text]}>{item.purpose}</Text>
              <Text style={[styles.cell, styles.text]}>{item.count}</Text>
            </View>
          ))}

          <Text style={[styles.sectionSubtitle, { marginTop: 10 }]}>User Type</Text>
          {sensoryHubUserType.map((item, index) => (
            <View style={styles.row} key={index}>
              <Text style={[styles.cell, styles.text]}>{item.userType}</Text>
              <Text style={[styles.cell, styles.text]}>{item.count}</Text>
            </View>
          ))}

          {sensoryHubTeams.length > 0 && (
            <>
              <Text style={[styles.sectionSubtitle, { marginTop: 10 }]}>Crew Teams</Text>
              {sensoryHubTeams.map((item, index) => (
                <View style={styles.row} key={index}>
                  <Text style={[styles.cell, styles.text]}>{item.teamName}</Text>
                  <Text style={[styles.cell, styles.text]}>{item.count}</Text>
                </View>
              ))}
            </>
          )}

          <Text style={[styles.sectionSubtitle, { marginTop: 10 }]}>Total Visits: {sensoryHubVisits.length}</Text>
        </View>

        {/* Footer */}
        <Text style={styles.footer}>
          Generated on {currentDate} • iTHINK Welfare System
        </Text>
      </Page>
    </Document>
  );
};

export const PDFReport: React.FC<PDFReportProps> = ({
  admissions,
  itemCounts,
  lostPropertyItems,
  sensoryHubVisits,
  open,
  onClose,
}) => {
  return (
    <Modal
      open={open}
      onClose={onClose}
      aria-labelledby="pdf-preview-modal"
      sx={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <Box sx={{ width: '90%', height: '90%', bgcolor: 'background.paper' }}>
        <PDFViewer style={{ width: '100%', height: '100%' }}>
          <PDFDocument
            admissions={admissions}
            itemCounts={itemCounts}
            lostPropertyItems={lostPropertyItems}
            sensoryHubVisits={sensoryHubVisits}
          />
        </PDFViewer>
      </Box>
    </Modal>
  );
};

// Helper function to generate and download PDF
export const generatePDF = async (
  admissions: WelfareAdmission[],
  itemCounts: ItemDocument[],
  lostPropertyItems: LostPropertyItem[],
  sensoryHubVisits: SensoryHubVisit[] = []
) => {
  const doc = (
    <PDFDocument
      admissions={admissions}
      itemCounts={itemCounts}
      lostPropertyItems={lostPropertyItems}
      sensoryHubVisits={sensoryHubVisits}
    />
  );
  
  const blob = await pdf(doc).toBlob();
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `welfare-report-${new Date().toISOString().split('T')[0]}.pdf`;
  link.click();
  URL.revokeObjectURL(url);
};