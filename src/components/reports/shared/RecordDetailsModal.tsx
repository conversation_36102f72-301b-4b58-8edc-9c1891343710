import React from 'react';
import { 
  Dialog, 
  DialogTitle, 
  DialogContent, 
  DialogActions, 
  Button, 
  Typography, 
  Grid, 
  Chip, 
  Divider,
  Box
} from '@mui/material';
import { WelfareAdmission } from '../../../types/admission';
import { ItemDocument, LostPropertyItem } from '../../../types/item';

interface RecordDetailsModalProps {
  open: boolean;
  onClose: () => void;
  record: WelfareAdmission | ItemDocument | LostPropertyItem | null;
  recordType: 'admission' | 'itemCount' | 'lostProperty';
}

export const RecordDetailsModal: React.FC<RecordDetailsModalProps> = ({
  open,
  onClose,
  record,
  recordType
}) => {
  if (!record) return null;

  const renderAdmissionDetails = (admission: WelfareAdmission) => (
    <>
      <DialogTitle>
        <Typography variant="h5" fontWeight="bold">
          {`${admission.FirstName || ''} ${admission.Surname || ''}`.trim() || 'Unnamed Patient'}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Admitted: {admission.Attended ? new Date(admission.Attended).toLocaleString() : 'Unknown'}
        </Typography>
      </DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3}>
          <Grid size={{ xs: 12, md: 6 }}>
            <Typography variant="h6" gutterBottom>Personal Information</Typography>
            <Grid container spacing={2}>
              <Grid size={{ xs: 6 }}>
                <Typography variant="subtitle2">Age</Typography>
                <Typography>{admission.Age || 'N/A'}</Typography>
              </Grid>
              <Grid size={{ xs: 6 }}>
                <Typography variant="subtitle2">Gender</Typography>
                <Typography>{admission.Gender || 'N/A'}</Typography>
              </Grid>
              <Grid size={{ xs: 12 }}>
                <Typography variant="subtitle2">Physical Description</Typography>
                <Box>
                  {admission.HairColour && (
                    <Typography>Hair: {admission.HairColour}, {admission.HairStyle}</Typography>
                  )}
                  {admission.ClothingTop && (
                    <Typography>Clothes: {admission.ClothingTop} (top), {admission.ClothingBottom} (bottom)</Typography>
                  )}
                  {admission.Footwear && (
                    <Typography>Footwear: {admission.Footwear}</Typography>
                  )}
                  {admission.OtherFeatures && (
                    <Typography>Other features: {admission.OtherFeatures}</Typography>
                  )}
                </Box>
              </Grid>
            </Grid>
            
            <Divider sx={{ my: 2 }} />
            
            <Typography variant="h6" gutterBottom>Incident Details</Typography>
            <Grid container spacing={2}>
              <Grid size={{ xs: 6 }}>
                <Typography variant="subtitle2">Reason</Typography>
                <Typography>{admission.ReasonCategory || 'N/A'}</Typography>
              </Grid>
              <Grid size={{ xs: 6 }}>
                <Typography variant="subtitle2">Referred By</Typography>
                <Typography>{admission.ReferredBy || 'N/A'}</Typography>
              </Grid>
              <Grid size={{ xs: 12 }}>
                <Typography variant="subtitle2">Bay/Chair</Typography>
                <Typography>{admission.BaysOrChairs || 'N/A'}</Typography>
              </Grid>
              <Grid size={{ xs: 12 }}>
                <Typography variant="subtitle2">Location</Typography>
                <Typography>{admission.Location || 'N/A'}</Typography>
              </Grid>
            </Grid>
          </Grid>
          
          <Grid size={{ xs: 12, md: 6 }}>
            <Typography variant="h6" gutterBottom>Substance Use</Typography>
            <Box sx={{ mb: 2 }}>
              {admission.SubstanceUsed && admission.SubstanceUsed.length > 0 ? (
                admission.SubstanceUsed.map((substance, index) => (
                  <Chip
                    key={index}
                    label={substance}
                    sx={{ m: 0.5 }}
                    color="primary"
                    variant="outlined"
                  />
                ))
              ) : (
                <Typography>None reported</Typography>
              )}
            </Box>
            
            <Divider sx={{ my: 2 }} />
            
            <Typography variant="h6" gutterBottom>Notes</Typography>
            {admission.AdditionalNotes && admission.AdditionalNotes.length > 0 ? (
              admission.AdditionalNotes.map((note, index) => (
                <Box key={index} sx={{ mb: 2 }}>
                  <Typography variant="body2" color="text.secondary">
                    {note.timestamp ? new Date(note.timestamp).toLocaleString() : 'No timestamp'}
                  </Typography>
                  <Typography variant="body1">{note.note}</Typography>
                </Box>
              ))
            ) : (
              <Typography>No additional notes</Typography>
            )}
            
            {admission.Safeguarding && (
              <>
                <Divider sx={{ my: 2 }} />
                <Typography variant="h6" gutterBottom>Safeguarding</Typography>
                <Typography variant="body1">
                  {admission.Safeguarding || 'No details provided'}
                </Typography>
              </>
            )}
          </Grid>
        </Grid>
      </DialogContent>
    </>
  );

  const renderItemCountDetails = (itemCount: ItemDocument) => {
    const ITEM_TYPES = [
      'Sanitizer',
      'ToiletRoll',
      'Suncream',
      'Poncho',
      'Earplugs',
      'Condoms',
      'ChildrensWristbands',
      'GeneralWristbands',
      'Water',
      'Charging',
      'SanitaryProducts',
      'GeneralEnqs'
    ];
    
    const totalCount = ITEM_TYPES.reduce((acc, type) => 
      acc + ((itemCount[type as keyof ItemDocument] as number) || 0), 0
    );

    return (
      <>
        <DialogTitle>
          <Typography variant="h5" fontWeight="bold">
            Item Distribution Record
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Recorded: {itemCount.createdAt ? new Date(itemCount.createdAt).toLocaleString() : 'Unknown'}
          </Typography>
        </DialogTitle>
        <DialogContent dividers>
          <Grid container spacing={3}>
            <Grid size={{ xs: 12 }}>
              <Typography variant="h6" gutterBottom>Distribution Summary</Typography>
              <Typography variant="subtitle1">
                Total Items Distributed: {totalCount}
              </Typography>
              <Typography variant="subtitle1">
                Location: {itemCount.locationName || itemCount.locationType || 'N/A'}
              </Typography>
              
              <Divider sx={{ my: 2 }} />
              
              <Typography variant="h6" gutterBottom>Items Distributed</Typography>
              <Grid container spacing={2}>
                {ITEM_TYPES.map(type => (
                  itemCount[type as keyof ItemDocument] ? (
                    <Grid size={{ xs: 6, sm: 4, md: 3 }} key={type}>
                      <Typography variant="subtitle2">
                        {type.replace(/([A-Z])/g, ' $1').trim()}
                      </Typography>
                      <Typography>
                        {itemCount[type as keyof ItemDocument] || 0}
                      </Typography>
                    </Grid>
                  ) : null
                ))}
              </Grid>
            </Grid>
          </Grid>
        </DialogContent>
      </>
    );
  };

  const renderLostPropertyDetails = (item: LostPropertyItem) => (
    <>
      <DialogTitle>
        <Typography variant="h5" fontWeight="bold">
          {item.category || 'Lost Item'}
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          Status: {item.status === 'claimed' ? 'Claimed' : 'Unclaimed'}
        </Typography>
      </DialogTitle>
      <DialogContent dividers>
        <Grid container spacing={3}>
          <Grid size={{ xs: 12, md: 6 }}>
            <Typography variant="h6" gutterBottom>Item Details</Typography>
            <Grid container spacing={2}>
              <Grid size={{ xs: 12 }}>
                <Typography variant="subtitle2">Description</Typography>
                <Typography>{item.description || 'No description provided'}</Typography>
              </Grid>
              <Grid size={{ xs: 6 }}>
                <Typography variant="subtitle2">Category</Typography>
                <Typography>{item.category || 'N/A'}</Typography>
              </Grid>
              <Grid size={{ xs: 6 }}>
                <Typography variant="subtitle2">Time Found</Typography>
                <Typography>
                  {item.timeFound ? new Date(item.timeFound).toLocaleString() : 'N/A'}
                </Typography>
              </Grid>
              <Grid size={{ xs: 12 }}>
                <Typography variant="subtitle2">Where Found</Typography>
                <Typography>{item.whereFound || 'N/A'}</Typography>
              </Grid>
            </Grid>
          </Grid>
          
          {item.status === 'claimed' && (
            <Grid size={{ xs: 12, md: 6 }}>
              <Typography variant="h6" gutterBottom>Claim Information</Typography>
              <Grid container spacing={2}>
                <Grid size={{ xs: 12 }}>
                  <Typography variant="subtitle2">Item Returned To</Typography>
                  <Typography>{item.itemReturned || 'N/A'}</Typography>
                </Grid>
                <Grid size={{ xs: 12 }}>
                  <Typography variant="subtitle2">Additional Information</Typography>
                  <Typography>
                    This item has been claimed and is no longer available in lost property.
                  </Typography>
                </Grid>
              </Grid>
            </Grid>
          )}
        </Grid>
      </DialogContent>
    </>
  );

  const renderContent = () => {
    switch (recordType) {
      case 'admission':
        return renderAdmissionDetails(record as WelfareAdmission);
      case 'itemCount':
        return renderItemCountDetails(record as ItemDocument);
      case 'lostProperty':
        return renderLostPropertyDetails(record as LostPropertyItem);
      default:
        return <DialogContent>No details available</DialogContent>;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          bgcolor: 'background.paper',
        }
      }}
    >
      {renderContent()}
      <DialogActions>
        <Button onClick={onClose} variant="contained">
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};