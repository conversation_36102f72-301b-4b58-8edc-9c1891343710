import React from 'react';
import { Tabs, Tab, Box } from '@mui/material';

export type ReportTab = 'admissions' | 'frontOfHouse' | 'lostProperty' | 'sensoryHub';

interface TabSelectorProps {
  activeTab: ReportTab;
  onTabChange: (tab: ReportTab) => void;
}

export const TabSelector: React.FC<TabSelectorProps> = ({ activeTab, onTabChange }) => {
  const handleChange = (_event: React.SyntheticEvent, newValue: ReportTab) => {
    onTabChange(newValue);
  };

  return (
    <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
      <Tabs 
        value={activeTab}
        onChange={handleChange}
        aria-label="report tabs"
        textColor="primary"
        indicatorColor="primary"
        sx={{
          '& .MuiTab-root': {
            textTransform: 'none',
            minWidth: 120,
            fontSize: '0.875rem',
            fontWeight: 500,
            color: 'text.secondary',
            '&.Mui-selected': {
              color: 'primary.main',
            },
          },
        }}
      >
        <Tab 
          label="Admissions" 
          value="admissions"
          sx={{ 
            borderRadius: '8px 8px 0 0',
          }}
        />
        <Tab 
          label="Front of House" 
          value="frontOfHouse"
          sx={{ 
            borderRadius: '8px 8px 0 0',
          }}
        />
        <Tab 
          label="Lost Property" 
          value="lostProperty"
          sx={{ 
            borderRadius: '8px 8px 0 0',
          }}
        />
        <Tab
          label="Sensory Hub"
          value="sensoryHub"
          sx={{
            borderRadius: '8px 8px 0 0',
          }}
        />
      </Tabs>
    </Box>
  );
};
