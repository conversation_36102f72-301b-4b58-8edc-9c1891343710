import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON><PERSON>,
  Pie,
  XAxis,
  YAxis,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import {
  Paper,
  Typography,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  Divider,
  Box
} from '@mui/material';
import { WelfareAdmission } from '../../../types/admission';
import {
  COLORS,
  getAdmissionsByTimeOfDay,
  getAdmissionsByReason,
  getAgeDistribution
} from '../../../utils/reportDataProcessing';
import { getHours, parseISO } from 'date-fns';
import { RecordDetailsModal } from '../shared/RecordDetailsModal';

interface AdmissionsChartsProps {
  admissions: WelfareAdmission[];
}

export const AdmissionsCharts: React.FC<AdmissionsChartsProps> = ({ admissions }) => {
  const [selectedHour, setSelectedHour] = useState<number | null>(null);
  const [selectedAdmission, setSelectedAdmission] = useState<WelfareAdmission | null>(null);
  const [showHourlyModal, setShowHourlyModal] = useState(false);
  const [showPatientModal, setShowPatientModal] = useState(false);

  // Function to handle clicking on a bar
  const handleBarClick = (data: any) => {
    setSelectedHour(data.hour);
    setShowHourlyModal(true);
  };

  // Function to filter admissions for the selected hour
  const getAdmissionsForHour = (hour: number): WelfareAdmission[] => {
    return admissions.filter(admission => {
      if (!admission.Attended) return false;
      return getHours(parseISO(admission.Attended)) === hour;
    });
  };

  // Handle clicking on a specific patient
  const handlePatientClick = (admission: WelfareAdmission) => {
    setSelectedAdmission(admission);
    setShowPatientModal(true);
  };

  // Get admissions for the selected hour
  const hourlyAdmissions = selectedHour !== null ? getAdmissionsForHour(selectedHour) : [];
  return (
    <>
    <Grid container spacing={3}>
      {/* Admissions by Time of Day */}
      <Grid size={{ xs: 12 }}>
        <Paper
          elevation={3}
          sx={{
            p: 3,
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
            borderRadius: 2
          }}
        >
          <Typography variant="h6" color="primary" gutterBottom>
            Admissions by Time of Day
          </Typography>
          <div style={{ height: 300, width: '100%' }}>
            <ResponsiveContainer>
              <BarChart data={getAdmissionsByTimeOfDay(admissions)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="label" interval={2} />
                <YAxis />
                <Tooltip formatter={(value: number) => [value, 'Admissions']} />
                <Bar
                  dataKey="count"
                  fill="#662D91"
                  name="Admissions"
                  onClick={handleBarClick} // Add click handler
                  cursor="pointer" // Change cursor to pointer to indicate clickable
                >
                  {getAdmissionsByTimeOfDay(admissions).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.count > 0 ? '#662D91' : '#E0E0E0'} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Paper>
      </Grid>

      {/* Admissions by Reason */}
      <Grid size={{ xs: 12, md: 6 }}>
        <Paper
          elevation={3}
          sx={{
            p: 3,
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
            borderRadius: 2,
            height: '100%'
          }}
        >
          <Typography variant="h6" color="primary" gutterBottom>
            Admissions by Reason
          </Typography>
          <div style={{ height: 300, width: '100%' }}>
            <ResponsiveContainer>
              <BarChart data={getAdmissionsByReason(admissions)}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="reason" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" name="Count">
                  {getAdmissionsByReason(admissions).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Paper>
      </Grid>

      {/* Age Distribution */}
      <Grid size={{ xs: 12, md: 6 }}>
        <Paper
          elevation={3}
          sx={{
            p: 3,
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
            borderRadius: 2,
            height: '100%'
          }}
        >
          <Typography variant="h6" color="primary" gutterBottom>
            Age Distribution
          </Typography>
          <div style={{ height: 300, width: '100%' }}>
            <ResponsiveContainer>
              <PieChart>
                <Pie
                  data={getAgeDistribution(admissions)}
                  dataKey="count"
                  nameKey="ageGroup"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  label
                >
                  {getAgeDistribution(admissions).map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Paper>
      </Grid>
    </Grid>
    
    {/* Modal for showing all patients in the selected hour */}
    <Dialog
      open={showHourlyModal}
      onClose={() => setShowHourlyModal(false)}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 2,
          bgcolor: 'background.paper',
        }
      }}
    >
      <DialogTitle>
        <Typography variant="h5" fontWeight="bold">
          {selectedHour !== null
            ? `Patients Admitted at ${getAdmissionsByTimeOfDay(admissions).find(d => d.hour === selectedHour)?.label || ''}`
            : 'Patients'
          }
        </Typography>
        <Typography variant="subtitle1" color="text.secondary">
          {hourlyAdmissions.length} {hourlyAdmissions.length === 1 ? 'patient' : 'patients'} admitted
        </Typography>
      </DialogTitle>
      <DialogContent dividers>
        {hourlyAdmissions.length > 0 ? (
          <List>
            {hourlyAdmissions.map((admission, index) => (
              <React.Fragment key={admission._id || index}>
                <ListItem
                  component="button"
                  onClick={() => handlePatientClick(admission)}
                  sx={{
                    borderRadius: 1,
                    '&:hover': { bgcolor: 'action.hover' },
                    transition: 'background-color 0.2s'
                  }}
                >
                  <ListItemText
                    primary={
                      <Typography variant="subtitle1" fontWeight="medium">
                        {`${admission.FirstName || ''} ${admission.Surname || ''}`.trim() || 'Unnamed Patient'}
                      </Typography>
                    }
                    secondary={
                      <Box>
                        <Typography variant="body2" component="span">
                          {admission.Gender || 'Unknown gender'}{admission.Age ? `, ${admission.Age} years` : ''}
                        </Typography>
                        <Typography variant="body2" component="div" color="text.secondary">
                          {admission.ReasonCategory || 'No reason specified'}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
                {index < hourlyAdmissions.length - 1 && <Divider component="li" />}
              </React.Fragment>
            ))}
          </List>
        ) : (
          <Typography variant="body1" align="center" sx={{ py: 2 }}>
            No patient details available for this time period.
          </Typography>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={() => setShowHourlyModal(false)} variant="contained">
          Close
        </Button>
      </DialogActions>
    </Dialog>

    {/* Modal for showing detailed patient information */}
    <RecordDetailsModal
      open={showPatientModal}
      onClose={() => setShowPatientModal(false)}
      record={selectedAdmission}
      recordType="admission"
    />
    </>
  );
};
