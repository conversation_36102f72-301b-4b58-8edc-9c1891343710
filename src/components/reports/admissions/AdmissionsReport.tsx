import React, { useState } from 'react';
import Grid from '@mui/material/Grid';
import { WelfareAdmission } from '../../../types/admission';
import { AdmissionsCharts } from './AdmissionsCharts';
import { AdmissionsTable } from './AdmissionsTable';
import { AdmissionModals } from '../../admission/AdmissionModals';

interface AdmissionsReportProps {
  admissions: WelfareAdmission[];
  selectedItems: string[];
  onSelectAll: (items: WelfareAdmission[]) => void;
  onSelectItem: (id: string) => void;
  onDelete: () => void;
  onUpdateAdmission?: (admission: WelfareAdmission) => Promise<void>;
}

export const AdmissionsReport: React.FC<AdmissionsReportProps> = ({
  admissions,
  selectedItems,
  onSelectAll,
  onSelectItem,
  onDelete,
  onUpdateAdmission,
}) => {
  const [showDischargeModal, setShowDischargeModal] = useState(false);
  const [selectedAdmission, setSelectedAdmission] = useState<WelfareAdmission | null>(null);
  const [dischargeNotes, setDischargeNotes] = useState('');
  const [dischargeTime, setDischargeTime] = useState(new Date().toISOString().slice(0, 16));
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleDischarge = (admission: WelfareAdmission) => {
    setSelectedAdmission(admission);
    setDischargeTime(new Date().toISOString().slice(0, 16));
    setDischargeNotes('');
    setShowDischargeModal(true);
  };

  const handleConfirmDischarge = async () => {
    if (!selectedAdmission || !onUpdateAdmission || !dischargeNotes.trim()) return;

    setIsSubmitting(true);
    try {
      const updatedAdmission: WelfareAdmission = {
        ...selectedAdmission,
        DischargeTime: dischargeTime,
        AdditionalNotes: [
          ...selectedAdmission.AdditionalNotes,
          {
            timestamp: dischargeTime,
            note: `Discharge Notes: ${dischargeNotes}`,
            author: 'Staff'
          }
        ],
        History: [
          ...selectedAdmission.History,
          {
            timestamp: dischargeTime,
            action: 'Discharged',
            details: dischargeNotes,
            author: 'Staff'
          }
        ],
        InBayNow: false,
        status: 'discharged'
      };

      await onUpdateAdmission(updatedAdmission);
      handleCloseDischargeModal();
    } catch (error) {
      console.error('Error discharging patient:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCloseDischargeModal = () => {
    setShowDischargeModal(false);
    setSelectedAdmission(null);
    setDischargeNotes('');
    setDischargeTime(new Date().toISOString().slice(0, 16));
  };

  return (
    <>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12 }}>
          <AdmissionsCharts admissions={admissions} />
        </Grid>
        <Grid size={{ xs: 12 }}>
          <AdmissionsTable
            admissions={admissions}
            selectedItems={selectedItems}
            onSelectAll={onSelectAll}
            onSelectItem={onSelectItem}
            onDelete={onDelete}
            onDischarge={handleDischarge}
          />
        </Grid>
      </Grid>

      <AdmissionModals
        showReAdmitModal={false}
        showConfirmReAdmit={false}
        showConfirmDischarge={showDischargeModal}
        reAdmitLocation={0}
        reAdmitNotes=""
        dischargeNotes={dischargeNotes}
        dischargeTime={dischargeTime}
        bayStatus={null}
        isSubmitting={isSubmitting}
        onReAdmitLocationChange={() => {}}
        onReAdmitNotesChange={() => {}}
        onDischargeNotesChange={setDischargeNotes}
        onDischargeTimeChange={setDischargeTime}
        onCloseReAdmit={() => {}}
        onCloseConfirmReAdmit={() => {}}
        onCloseConfirmDischarge={handleCloseDischargeModal}
        onConfirmReAdmit={() => {}}
        onConfirmDischarge={handleConfirmDischarge}
        onShowConfirmReAdmit={() => {}}
      />
    </>
  );
};
