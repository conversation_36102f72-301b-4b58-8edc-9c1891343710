import React, { useState } from 'react';
import {
  DataGrid,
  GridColDef,
  GridRowSelectionModel,
  GridRenderCellParams,
  GridToolbar,
  GridValueGetter,
} from '@mui/x-data-grid';
import {
  Paper,
  Typography,
  Button,
  Box
} from '@mui/material';
import { WelfareAdmission } from '../../../types/admission';
import { RecordDetailsModal } from '../shared/RecordDetailsModal';

interface AdmissionsTableProps {
  admissions: WelfareAdmission[];
  selectedItems: string[];
  onSelectAll: (items: WelfareAdmission[]) => void;
  onSelectItem: (id: string) => void;
  onDelete: () => void;
  onDischarge?: (admission: WelfareAdmission) => void;
}

export const AdmissionsTable: React.FC<AdmissionsTableProps> = ({
  admissions,
  selectedItems,
  onSelectAll,
  onSelectItem,
  onDelete,
  onDischarge,
}) => {
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<WelfareAdmission | null>(null);

  const handleRowClick = (record: WelfareAdmission) => {
    setSelectedRecord(record);
    setDetailsModalOpen(true);
  };

  const handleCloseModal = () => {
    setDetailsModalOpen(false);
  };
  
  const columns: GridColDef<WelfareAdmission>[] = [
    {
      field: 'name',
      headerName: 'Name',
      flex: 1,
      sortable: true,
      filterable: true,
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        const admission = params.row;
        return `${admission.FirstName || ''} ${admission.Surname || ''}`.trim() || 'N/A';
      },
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object') return 'n/a';
        const row = params.row;
        return `${row.FirstName || ''} ${row.Surname || ''}`.trim().toLowerCase() || 'n/a';
      },
    },
    {
      field: 'Age',
      headerName: 'Age',
      width: 100,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        // Return undefined if params/row are invalid
        if (!params || !params.row || typeof params.row !== 'object') return undefined;
        const age = params.row.Age;
        // Return the number if it exists (including 0), otherwise undefined
        return typeof age === 'number' ? age : undefined;
      },
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        const admission = params.row;
        // Check if Age is a number (including 0) before defaulting
        return typeof admission.Age === 'number' ? admission.Age : 'N/A';
      },
    },
    {
      field: 'ReferredBy',
      headerName: 'Referred By',
      flex: 1,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object') return 'n/a';
        return params.row.ReferredBy || 'n/a';
      },
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        const admission = params.row;
        return admission.ReferredBy || 'N/A';
      },
    },
    {
      field: 'ReasonCategory',
      headerName: 'Reason',
      flex: 1,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object') return 'n/a';
        return params.row.ReasonCategory || 'n/a';
      },
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        const admission = params.row;
        return admission.ReasonCategory || 'N/A';
      },
    },
    {
      field: 'SubstanceUsed',
      headerName: 'Substances',
      flex: 1,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object') return 'n/a';
        return params.row.SubstanceUsed?.join(', ').toLowerCase() || 'n/a';
      },
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        const admission = params.row;
        return admission.SubstanceUsed?.join(', ') || 'N/A';
      },
    },
    {
      field: 'Attended',
      headerName: 'Admitted',
      flex: 1,
      sortable: true,
      filterable: true,
      type: 'dateTime',
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object' || !('Attended' in params.row)) return null;
        return params.row.Attended ? new Date(params.row.Attended) : null;
      },
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        if (!params || !params.row || typeof params.row !== 'object' || !('Attended' in params.row)) return 'N/A';
        const admission = params.row;
        return admission.Attended ? new Date(admission.Attended).toLocaleString() : 'N/A';
      },
    },
    {
      field: 'BaysOrChairs',
      headerName: 'Bay/Chair',
      width: 100,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object') return 'n/a';
        return params.row.BaysOrChairs || 'n/a';
      },
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        const admission = params.row;
        return admission.BaysOrChairs || 'N/A';
      },
    },
    {
      field: 'Location',
      headerName: 'Location',
      width: 100,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object') return 'n/a';
        return params.row.Location || 'n/a';
      },
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        const admission = params.row;
        return admission.Location || 'N/A';
      },
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 120,
      sortable: false,
      filterable: false,
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        const admission = params.row;
        const isAdmitted = admission.InBayNow;
        
        if (!isAdmitted || !onDischarge) return null;

        return (
          <Button
            variant="contained"
            color="primary"
            size="small"
            onClick={(e) => {
              e.stopPropagation();
              onDischarge(admission);
            }}
          >
            Discharge
          </Button>
        );
      },
    },
  ];

  const handleSelectionChange = (newSelection: GridRowSelectionModel) => {
    // Convert Set to Array for comparison
    const selectedIds = Array.from(newSelection.ids).map(id => id.toString());
    
    // Add newly selected items
    selectedIds.forEach((id: string) => {
      if (!selectedItems.includes(id)) {
        onSelectItem(id);
      }
    });
    
    // Remove deselected items
    selectedItems.forEach((id: string) => {
      if (!selectedIds.includes(id)) {
        onSelectItem(id);
      }
    });
  };

  return (
    <Paper 
      elevation={3}
      sx={{ 
        bgcolor: 'background.paper',
        backdropFilter: 'blur(8px)',
        borderRadius: 2,
        p: 3
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" color="primary">
          Raw Data
        </Typography>
        {selectedItems.length > 0 && (
          <Button
            variant="contained"
            color="error"
            onClick={onDelete}
          >
            Delete Selected ({selectedItems.length})
          </Button>
        )}
      </Box>
      <div style={{ height: 400, width: '100%' }}>
        <DataGrid<WelfareAdmission>
          rows={admissions || []}
          columns={columns}
          getRowId={(row) => row._id}
          checkboxSelection
          disableRowSelectionOnClick
          rowSelectionModel={{ type: 'include', ids: new Set(selectedItems) }}
          onRowSelectionModelChange={handleSelectionChange}
          density="compact"
          slots={{ toolbar: GridToolbar }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
              quickFilterProps: { debounceMs: 300 },
            },
          }}
          initialState={{
            sorting: {
              sortModel: [{ field: 'Attended', sort: 'desc' }],
            },
          }}
          onRowClick={(params) => handleRowClick(params.row)}
          sx={{
            '& .MuiDataGrid-cell': {
              fontSize: '0.875rem',
            },
            '& .MuiDataGrid-toolbarContainer': {
              padding: '8px 24px',
            },
            '& .MuiDataGrid-toolbar': {
              '& .MuiFormControl-root': {
                width: '100%',
                maxWidth: '300px',
              },
            },
            '& .MuiDataGrid-row': {
              cursor: 'pointer',
            },
            border: 'none',
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
          }}
        />
      </div>
      
      <RecordDetailsModal
        open={detailsModalOpen}
        onClose={handleCloseModal}
        record={selectedRecord}
        recordType="admission"
      />
    </Paper>
  );
};