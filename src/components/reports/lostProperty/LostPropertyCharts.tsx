import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>Axis,
  <PERSON><PERSON><PERSON>s,
  CartesianGrid,
  <PERSON>lt<PERSON>,
  Legend,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { Paper, Typography, Grid } from '@mui/material';
import { LostPropertyItem } from '../../../types/item';
import { 
  COLORS, 
  getLostPropertyByTimeOfDay,
  getLostPropertyByCategory,
  getLostPropertyByStatus
} from '../../../utils/reportDataProcessing';

interface LostPropertyChartsProps {
  items: LostPropertyItem[];
}

export const LostPropertyCharts: React.FC<LostPropertyChartsProps> = ({ items }) => {
  const timeOfDayData = getLostPropertyByTimeOfDay(items);
  const categoryData = getLostPropertyByCategory(items);
  const statusData = getLostPropertyByStatus(items);

  return (
    <Grid container spacing={3}>
      {/* Lost Property by Time of Day */}
      <Grid size={{ xs: 12 }}>
        <Paper 
          elevation={3}
          sx={{ 
            p: 3,
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
            borderRadius: 2
          }}
        >
          <Typography variant="h6" color="primary" gutterBottom>
            Lost Property by Time of Day
          </Typography>
          <div style={{ height: 300, width: '100%' }}>
            <ResponsiveContainer>
              <BarChart data={timeOfDayData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="label" />
                <YAxis />
                <Tooltip formatter={(value: number) => [value, 'Items']} />
                <Bar dataKey="count" fill="#662D91" name="Items">
                  {timeOfDayData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.count > 0 ? '#662D91' : '#E0E0E0'} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Paper>
      </Grid>

      {/* Lost Property by Category */}
      <Grid size={{ xs: 12, md: 6 }}>
        <Paper 
          elevation={3}
          sx={{ 
            p: 3,
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
            borderRadius: 2,
            height: '100%'
          }}
        >
          <Typography variant="h6" color="primary" gutterBottom>
            Lost Property by Category
          </Typography>
          <div style={{ height: 300, width: '100%' }}>
            <ResponsiveContainer>
              <BarChart data={categoryData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="category" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" name="Count">
                  {categoryData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Paper>
      </Grid>

      {/* Lost Property Status */}
      <Grid size={{ xs: 12, md: 6 }}>
        <Paper 
          elevation={3}
          sx={{ 
            p: 3,
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
            borderRadius: 2,
            height: '100%'
          }}
        >
          <Typography variant="h6" color="primary" gutterBottom>
            Lost Property Status
          </Typography>
          <div style={{ height: 300, width: '100%' }}>
            <ResponsiveContainer>
              <PieChart>
                <Pie
                  data={statusData}
                  dataKey="count"
                  nameKey="status"
                  cx="50%"
                  cy="50%"
                  outerRadius={100}
                  label
                >
                  {statusData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </Paper>
      </Grid>
    </Grid>
  );
};
