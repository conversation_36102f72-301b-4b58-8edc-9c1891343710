import React, { useState } from 'react';
import { 
  DataGrid, 
  GridColDef,
  GridRowSelectionModel,
  GridToolbar,
} from '@mui/x-data-grid';
import { 
  Paper,
  Typography,
  Button,
  Box
} from '@mui/material';
import { LostPropertyItem } from '../../../types/item';
import { RecordDetailsModal } from '../shared/RecordDetailsModal';

interface LostPropertyTableProps {
  items: LostPropertyItem[];
  selectedItems: string[];
  onSelectAll: (items: LostPropertyItem[]) => void;
  onSelectItem: (id: string) => void;
  onDelete: () => void;
}

export const LostPropertyTable: React.FC<LostPropertyTableProps> = ({
  items,
  selectedItems,
  onSelectAll,
  onSelectItem,
  onDelete,
}) => {
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<LostPropertyItem | null>(null);

  const handleRowClick = (record: LostPropertyItem) => {
    setSelectedRecord(record);
    setDetailsModalOpen(true);
  };

  const handleCloseModal = () => {
    setDetailsModalOpen(false);
  };
  
  type Row = LostPropertyItem;

  const columns: GridColDef<Row>[] = [
    {
      field: 'description',
      headerName: 'Description',
      flex: 2,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row) return '';
        const row = params.row;
        return (row.description || '').toLowerCase();
      },
      renderCell: ({ row }: { row: Row }) => {
        return row.description || 'N/A';
      },
    },
    {
      field: 'category',
      headerName: 'Category',
      flex: 1,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row) return '';
        const row = params.row;
        return (row.category || '').toLowerCase();
      },
      renderCell: ({ row }: { row: Row }) => {
        return row.category || 'N/A';
      },
    },
    {
      field: 'status',
      headerName: 'Status',
      flex: 1,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row) return 'unclaimed';
        const row = params.row;
        return row.status === 'claimed' ? 'claimed' : 'unclaimed';
      },
      renderCell: ({ row }: { row: Row }) => {
        return row.status === 'claimed' ? 'Claimed' : 'Unclaimed';
      },
    },
    {
      field: 'timeFound',
      headerName: 'Time Found',
      flex: 1,
      sortable: true,
      filterable: true,
      type: 'dateTime',
      valueGetter: (params: any) => {
        if (
          !params ||
          !params.row ||
          typeof params.row !== 'object' ||
          !('timeFound' in params.row)
        ) return null;
        const row = params.row;
        return row.timeFound ? new Date(row.timeFound) : null;
      },
      renderCell: ({ row }: { row: Row }) => {
        return row.timeFound ? new Date(row.timeFound).toLocaleString() : 'N/A';
      },
    },
    {
      field: 'whereFound',
      headerName: 'Where Found',
      flex: 1,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row) return '';
        const row = params.row;
        return (row.whereFound || '').toLowerCase();
      },
      renderCell: ({ row }: { row: Row }) => {
        return row.whereFound || 'N/A';
      },
    },
  ];

  const handleSelectionChange = (newSelection: GridRowSelectionModel) => {
    // Convert Set to Array for comparison
    const selectedIds = Array.from(newSelection.ids).map(id => id.toString());
    
    // Add newly selected items
    selectedIds.forEach((id: string) => {
      if (!selectedItems.includes(id)) {
        onSelectItem(id);
      }
    });
    
    // Remove deselected items
    selectedItems.forEach((id: string) => {
      if (!selectedIds.includes(id)) {
        onSelectItem(id);
      }
    });
  };

  return (
    <Paper 
      elevation={3}
      sx={{ 
        bgcolor: 'background.paper',
        backdropFilter: 'blur(8px)',
        borderRadius: 2,
        p: 3
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" color="primary">
          Raw Data
        </Typography>
        {selectedItems.length > 0 && (
          <Button
            variant="contained"
            color="error"
            onClick={onDelete}
          >
            Delete Selected ({selectedItems.length})
          </Button>
        )}
      </Box>
      <div style={{ height: 400, width: '100%' }}>
        <DataGrid<Row>
          rows={(items || []).filter(Boolean)}
          columns={columns}
          getRowId={(row) => row._id}
          checkboxSelection
          disableRowSelectionOnClick
          rowSelectionModel={{ type: 'include', ids: new Set(selectedItems) }}
          onRowSelectionModelChange={handleSelectionChange}
          density="compact"
          slots={{ toolbar: GridToolbar }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
              quickFilterProps: { debounceMs: 300 },
            },
          }}
          initialState={{
            sorting: {
              sortModel: [{ field: 'timeFound', sort: 'desc' }],
            },
          }}
          onRowClick={(params) => handleRowClick(params.row)}
          sx={{
            '& .MuiDataGrid-cell': {
              fontSize: '0.875rem',
            },
            '& .MuiDataGrid-toolbarContainer': {
              padding: '8px 24px',
            },
            '& .MuiDataGrid-toolbar': {
              '& .MuiFormControl-root': {
                width: '100%',
                maxWidth: '300px',
              },
            },
            '& .MuiDataGrid-row': {
              cursor: 'pointer',
            },
            border: 'none',
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
          }}
        />
      </div>

      <RecordDetailsModal
        open={detailsModalOpen}
        onClose={handleCloseModal}
        record={selectedRecord}
        recordType="lostProperty"
      />
    </Paper>
  );
};
