import React from 'react';
import Grid from '@mui/material/Grid';
import { LostPropertyItem } from '../../../types/item';
import { LostPropertyCharts } from './LostPropertyCharts';
import { LostPropertyTable } from './LostPropertyTable';

interface LostPropertyReportProps {
  items: LostPropertyItem[];
  selectedItems: string[];
  onSelectAll: (items: LostPropertyItem[]) => void;
  onSelectItem: (id: string) => void;
  onDelete: () => void;
}

export const LostPropertyReport: React.FC<LostPropertyReportProps> = ({
  items,
  selectedItems,
  onSelectAll,
  onSelectItem,
  onDelete,
}) => {
  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }}>
        <LostPropertyCharts items={items} />
      </Grid>
      <Grid size={{ xs: 12 }}>
        <LostPropertyTable
          items={items}
          selectedItems={selectedItems}
          onSelectAll={onSelectAll}
          onSelectItem={onSelectItem}
          onDelete={onDelete}
        />
      </Grid>
    </Grid>
  );
};
