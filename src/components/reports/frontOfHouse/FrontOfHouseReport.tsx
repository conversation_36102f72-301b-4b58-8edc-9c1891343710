import React from 'react';
import Grid from '@mui/material/Grid';
import { ItemDocument } from '../../../types/item';
import { FrontOfHouseCharts } from './FrontOfHouseCharts';
import { FrontOfHouseTable } from './FrontOfHouseTable';

interface FrontOfHouseReportProps {
  itemCounts: ItemDocument[];
  selectedItems: string[];
  onSelectAll: (items: ItemDocument[]) => void;
  onSelectItem: (id: string) => void;
  onDelete: () => void;
}

export const FrontOfHouseReport: React.FC<FrontOfHouseReportProps> = ({
  itemCounts,
  selectedItems,
  onSelectAll,
  onSelectItem,
  onDelete,
}) => {
  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }}>
        <FrontOfHouseCharts itemCounts={itemCounts} />
      </Grid>
      <Grid size={{ xs: 12 }}>
        <FrontOfHouseTable
          itemCounts={itemCounts}
          selectedItems={selectedItems}
          onSelectAll={onSelectAll}
          onSelectItem={onSelectItem}
          onDelete={onDelete}
        />
      </Grid>
    </Grid>
  );
};
