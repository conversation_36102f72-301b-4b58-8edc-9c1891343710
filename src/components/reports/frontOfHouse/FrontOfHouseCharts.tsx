import React from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  Legend,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import { Paper, Typography, Grid } from '@mui/material';
import { ItemDocument } from '../../../types/item';
import { 
  COLORS, 
  getItemCountsByType 
} from '../../../utils/reportDataProcessing';

interface ChartData {
  count: number;
  [key: string]: string | number;
}

interface FrontOfHouseChartsProps {
  itemCounts: ItemDocument[];
}

export const FrontOfHouseCharts: React.FC<FrontOfHouseChartsProps> = ({ itemCounts }) => {
  const typeData = getItemCountsByType(itemCounts);

  return (
    <Grid container spacing={3}>
      {/* Items by Type */}
      <Grid size={{ xs: 12 }}>
        <Paper 
          elevation={3}
          sx={{ 
            p: 3,
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
            borderRadius: 2
          }}
        >
          <Typography variant="h6" color="primary" gutterBottom>
            Items by Type
          </Typography>
          <div style={{ height: 300, width: '100%' }}>
            <ResponsiveContainer>
              <BarChart data={typeData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="type" />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="count" name="Count">
                  {typeData.map((entry: ChartData, index: number) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </div>
        </Paper>
      </Grid>
    </Grid>
  );
};
