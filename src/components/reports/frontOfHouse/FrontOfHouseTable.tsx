import React, { useState } from 'react';
import {
  DataGrid,
  GridColDef,
  GridRowSelectionModel,
  GridRenderCellParams,
  GridToolbar
} from '@mui/x-data-grid';
import { 
  Paper,
  Typography,
  Button,
  Box
} from '@mui/material';
import { ItemDocument } from '../../../types/item';
import { RecordDetailsModal } from '../shared/RecordDetailsModal';

const ITEM_TYPES = [
  'Sanitizer',
  'ToiletRoll',
  'Suncream',
  'Poncho',
  'Earplugs',
  'Condoms',
  'ChildrensWristbands',
  'GeneralWristbands',
  'Water',
  'Charging',
  'SanitaryProducts',
  'GeneralEnqs'
];

interface FrontOfHouseTableProps {
  itemCounts: ItemDocument[];
  selectedItems: string[];
  onSelectAll: (items: ItemDocument[]) => void;
  onSelectItem: (id: string) => void;
  onDelete: () => void;
}

export const FrontOfHouseTable: React.FC<FrontOfHouseTableProps> = ({
  itemCounts,
  selectedItems,
  onSelectAll,
  onSelectItem,
  onDelete,
}) => {
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<ItemDocument | null>(null);

  const handleRowClick = (record: ItemDocument) => {
    setSelectedRecord(record);
    setDetailsModalOpen(true);
  };

  const handleCloseModal = () => {
    setDetailsModalOpen(false);
  };

  type Row = ItemDocument;

  const columns: GridColDef<Row>[] = [
    {
      field: 'items',
      headerName: 'Items',
      flex: 2,
      renderCell: (params: GridRenderCellParams<Row>) => {
        const row = params.row;
        if (!row) return '';
        return ITEM_TYPES
          .filter(type => row[type as keyof ItemDocument])
          .map(type => `${type}: ${row[type as keyof ItemDocument]}`)
          .join(', ');
      },
    },
    {
      field: 'totalCount',
      headerName: 'Total Count',
      flex: 1,
      renderCell: (params: GridRenderCellParams<Row>) => {
        const row = params.row;
        if (!row) return 0;
        return ITEM_TYPES.reduce((acc, type) => 
          acc + (row[type as keyof ItemDocument] as number || 0), 0
        );
      },
    },
    {
      field: 'createdAt',
      headerName: 'Timestamp',
      flex: 1,
      renderCell: (params: GridRenderCellParams<Row>) => {
        const row = params.row;
        return row.createdAt ? new Date(row.createdAt).toLocaleString() : 'N/A';
      },
    },
  ];

  const handleSelectionChange = (newSelection: GridRowSelectionModel) => {
    // Convert Set to Array for comparison
    const selectedIds = Array.from(newSelection.ids).map(id => id.toString());
    
    // Add newly selected items
    selectedIds.forEach((id: string) => {
      if (!selectedItems.includes(id)) {
        onSelectItem(id);
      }
    });
    
    // Remove deselected items
    selectedItems.forEach((id: string) => {
      if (!selectedIds.includes(id)) {
        onSelectItem(id);
      }
    });
  };

  return (
    <Paper 
      elevation={3}
      sx={{ 
        bgcolor: 'background.paper',
        backdropFilter: 'blur(8px)',
        borderRadius: 2,
        p: 3
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" color="primary">
          Raw Data
        </Typography>
        {selectedItems.length > 0 && (
          <Button
            variant="contained"
            color="error"
            onClick={onDelete}
          >
            Delete Selected ({selectedItems.length})
          </Button>
        )}
      </Box>
      <div style={{ height: 400, width: '100%' }}>
        <DataGrid<Row>
          rows={itemCounts || []}
          columns={columns}
          getRowId={(row) => row._id}
          checkboxSelection
          disableRowSelectionOnClick
          rowSelectionModel={{ type: 'include', ids: new Set(selectedItems) }}
          onRowSelectionModelChange={handleSelectionChange}
          density="compact"
          slots={{ toolbar: GridToolbar }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
              quickFilterProps: { debounceMs: 300 },
            },
          }}
          initialState={{
            sorting: {
              sortModel: [{ field: 'createdAt', sort: 'desc' }],
            },
          }}
          onRowClick={(params) => handleRowClick(params.row)}
          sx={{
            '& .MuiDataGrid-cell': {
              fontSize: '0.875rem',
            },
            '& .MuiDataGrid-toolbarContainer': {
              padding: '8px 24px',
            },
            '& .MuiDataGrid-toolbar': {
              '& .MuiFormControl-root': {
                width: '100%',
                maxWidth: '300px',
              },
            },
            '& .MuiDataGrid-row': {
              cursor: 'pointer',
            },
            border: 'none',
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
          }}
        />
      </div>

      <RecordDetailsModal
        open={detailsModalOpen}
        onClose={handleCloseModal}
        record={selectedRecord}
        recordType="itemCount"
      />
    </Paper>
  );
};
