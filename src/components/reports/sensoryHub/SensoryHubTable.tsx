import React, { useState } from 'react';
import {
  DataGrid,
  GridColDef,
  GridRowSelectionModel,
  GridRenderCellParams,
  GridToolbar,
} from '@mui/x-data-grid';
import {
  Paper,
  Typography,
  Button,
  Box,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Chip
} from '@mui/material';
import { SensoryHubVisit } from '../../../types/sensory-hub';

interface SensoryHubTableProps {
  visits: SensoryHubVisit[];
  selectedItems: string[];
  onSelectAll: (items: SensoryHubVisit[]) => void;
  onSelectItem: (id: string) => void;
  onDelete: () => void;
}

export const SensoryHubTable: React.FC<SensoryHubTableProps> = ({
  visits,
  selectedItems,
  onSelectAll,
  onSelectItem,
  onDelete,
}) => {
  const [detailsModalOpen, setDetailsModalOpen] = useState(false);
  const [selectedRecord, setSelectedRecord] = useState<SensoryHubVisit | null>(null);

  const handleRowClick = (record: SensoryHubVisit) => {
    setSelectedRecord(record);
    setDetailsModalOpen(true);
  };

  const handleCloseModal = () => {
    setDetailsModalOpen(false);
  };
  
  const columns: GridColDef<SensoryHubVisit>[] = [
    {
      field: 'visitTimestamp',
      headerName: 'Visit Time',
      flex: 1,
      sortable: true,
      filterable: true,
      type: 'dateTime',
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object' || !('visitTimestamp' in params.row)) return null;
        return params.row.visitTimestamp ? new Date(params.row.visitTimestamp) : null;
      },
      renderCell: (params: GridRenderCellParams<SensoryHubVisit>) => {
        if (!params || !params.row || typeof params.row !== 'object' || !('visitTimestamp' in params.row)) return 'N/A';
        const visit = params.row;
        return visit.visitTimestamp ? new Date(visit.visitTimestamp).toLocaleString() : 'N/A';
      },
    },
    {
      field: 'purpose',
      headerName: 'Purpose',
      width: 150,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object') return 'n/a';
        return params.row.purpose || 'n/a';
      },
      renderCell: (params: GridRenderCellParams<SensoryHubVisit>) => {
        const visit = params.row;
        const purpose = visit.purpose === 'look_around' ? 'Look Around' : 'Use Service';
        return (
          <Chip
            label={purpose}
            color={visit.purpose === 'use_service' ? 'primary' : 'default'}
            variant="outlined"
            size="small"
          />
        );
      },
    },
    {
      field: 'userType',
      headerName: 'User Type',
      width: 120,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object') return 'n/a';
        return params.row.userType || 'n/a';
      },
      renderCell: (params: GridRenderCellParams<SensoryHubVisit>) => {
        const visit = params.row;
        const userType = visit.userType === 'crew' ? 'Crew' : 'Public';
        return (
          <Chip
            label={userType}
            color={visit.userType === 'crew' ? 'secondary' : 'default'}
            variant="filled"
            size="small"
          />
        );
      },
    },
    {
      field: 'teamName',
      headerName: 'Team',
      flex: 1,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object') return 'n/a';
        return params.row.teamName || 'n/a';
      },
      renderCell: (params: GridRenderCellParams<SensoryHubVisit>) => {
        const visit = params.row;
        return visit.teamName || (visit.userType === 'crew' ? 'No Team' : 'N/A');
      },
    },
    {
      field: 'festivalId',
      headerName: 'Festival',
      width: 120,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object') return 'n/a';
        return params.row.festivalId || 'n/a';
      },
      renderCell: (params: GridRenderCellParams<SensoryHubVisit>) => {
        const visit = params.row;
        // Show just the last part of the festival ID for readability
        const festivalDisplay = visit.festivalId ? visit.festivalId.split('_').pop() || visit.festivalId : 'N/A';
        return festivalDisplay;
      },
    },
    {
      field: 'siteLocationId',
      headerName: 'Site',
      width: 100,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row || typeof params.row !== 'object') return 'n/a';
        return params.row.siteLocationId || 'n/a';
      },
      renderCell: (params: GridRenderCellParams<SensoryHubVisit>) => {
        const visit = params.row;
        return visit.siteLocationId || 'N/A';
      },
    },
  ];

  const handleSelectionChange = (newSelection: GridRowSelectionModel) => {
    // Convert Set to Array for comparison
    const selectedIds = Array.from(newSelection.ids).map(id => id.toString());
    
    // Add newly selected items
    selectedIds.forEach((id: string) => {
      if (!selectedItems.includes(id)) {
        onSelectItem(id);
      }
    });
    
    // Remove deselected items
    selectedItems.forEach((id: string) => {
      if (!selectedIds.includes(id)) {
        onSelectItem(id);
      }
    });
  };

  return (
    <Paper 
      elevation={3}
      sx={{ 
        bgcolor: 'background.paper',
        backdropFilter: 'blur(8px)',
        borderRadius: 2,
        p: 3
      }}
    >
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6" color="primary">
          Raw Data
        </Typography>
        {selectedItems.length > 0 && (
          <Button
            variant="contained"
            color="error"
            onClick={onDelete}
          >
            Delete Selected ({selectedItems.length})
          </Button>
        )}
      </Box>
      <div style={{ height: 400, width: '100%' }}>
        <DataGrid<SensoryHubVisit>
          rows={visits || []}
          columns={columns}
          getRowId={(row) => row._id}
          checkboxSelection
          disableRowSelectionOnClick
          rowSelectionModel={{ type: 'include', ids: new Set(selectedItems) }}
          onRowSelectionModelChange={handleSelectionChange}
          density="compact"
          slots={{ toolbar: GridToolbar }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
              quickFilterProps: { debounceMs: 300 },
            },
          }}
          initialState={{
            sorting: {
              sortModel: [{ field: 'visitTimestamp', sort: 'desc' }],
            },
          }}
          onRowClick={(params) => handleRowClick(params.row)}
          sx={{
            '& .MuiDataGrid-cell': {
              fontSize: '0.875rem',
            },
            '& .MuiDataGrid-toolbarContainer': {
              padding: '8px 24px',
            },
            '& .MuiDataGrid-toolbar': {
              '& .MuiFormControl-root': {
                width: '100%',
                maxWidth: '300px',
              },
            },
            '& .MuiDataGrid-row': {
              cursor: 'pointer',
            },
            border: 'none',
            bgcolor: 'background.paper',
            backdropFilter: 'blur(8px)',
          }}
        />
      </div>
      
      {/* Visit Details Modal */}
      <Dialog
        open={detailsModalOpen}
        onClose={handleCloseModal}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            bgcolor: 'background.paper',
          }
        }}
      >
        <DialogTitle>
          <Typography variant="h5" fontWeight="bold">
            Sensory Hub Visit Details
          </Typography>
        </DialogTitle>
        <DialogContent dividers>
          {selectedRecord && (
            <Box>
              <Typography variant="subtitle1" gutterBottom>
                <strong>Purpose:</strong> {selectedRecord.purpose === 'look_around' ? 'Look Around' : 'Use Service'}
              </Typography>
              <Typography variant="subtitle1" gutterBottom>
                <strong>User Type:</strong> {selectedRecord.userType === 'crew' ? 'Crew' : 'Public'}
              </Typography>
              {selectedRecord.teamName && (
                <Typography variant="subtitle1" gutterBottom>
                  <strong>Team:</strong> {selectedRecord.teamName}
                </Typography>
              )}
              <Typography variant="subtitle1" gutterBottom>
                <strong>Visit Time:</strong> {new Date(selectedRecord.visitTimestamp).toLocaleString()}
              </Typography>
              <Typography variant="subtitle1" gutterBottom>
                <strong>Festival ID:</strong> {selectedRecord.festivalId}
              </Typography>
              {selectedRecord.siteLocationId && (
                <Typography variant="subtitle1" gutterBottom>
                  <strong>Site Location:</strong> {selectedRecord.siteLocationId}
                </Typography>
              )}
              <Typography variant="subtitle1" gutterBottom>
                <strong>Record ID:</strong> {selectedRecord._id}
              </Typography>
              {selectedRecord.createdAt && (
                <Typography variant="subtitle1" gutterBottom>
                  <strong>Created:</strong> {new Date(selectedRecord.createdAt).toLocaleString()}
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseModal} variant="contained">
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </Paper>
  );
};