import React from 'react';
import Grid from '@mui/material/Grid';
import { SensoryHubVisit } from '../../../types/sensory-hub';
import { SensoryHubCharts } from './SensoryHubCharts';
import { SensoryHubTable } from './SensoryHubTable';

interface SensoryHubReportProps {
  visits: SensoryHubVisit[];
  selectedItems: string[];
  onSelectAll: (items: SensoryHubVisit[]) => void;
  onSelectItem: (id: string) => void;
  onDelete: () => void;
}

export const SensoryHubReport: React.FC<SensoryHubReportProps> = ({
  visits,
  selectedItems,
  onSelectAll,
  onSelectItem,
  onDelete,
}) => {
  return (
    <Grid container spacing={3}>
      <Grid size={{ xs: 12 }}>
        <SensoryHubCharts visits={visits} />
      </Grid>
      <Grid size={{ xs: 12 }}>
        <SensoryHubTable
          visits={visits}
          selectedItems={selectedItems}
          onSelectAll={onSelectAll}
          onSelectItem={onSelectItem}
          onDelete={onDelete}
        />
      </Grid>
    </Grid>
  );
};