import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  Bar,
  <PERSON>hart,
  Pie,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  Cell,
} from 'recharts';
import {
  Paper,
  Typography,
  Grid,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  List,
  ListItem,
  ListItemText,
  Divider,
  Box
} from '@mui/material';
import { SensoryHubVisit } from '../../../types/sensory-hub';
import { COLORS } from '../../../utils/reportDataProcessing';
import { getHours, parseISO } from 'date-fns';

interface SensoryHubChartsProps {
  visits: SensoryHubVisit[];
}

// Helper functions for data processing
const getVisitsByTimeOfDay = (visits: SensoryHubVisit[]) => {
  const hourCounts = Array.from({ length: 24 }, (_, i) => ({
    hour: i,
    label: `${i.toString().padStart(2, '0')}:00`,
    count: 0
  }));

  visits.forEach(visit => {
    if (visit.visitTimestamp) {
      const hour = getHours(parseISO(visit.visitTimestamp));
      hourCounts[hour].count++;
    }
  });

  return hourCounts;
};

const getVisitsByPurpose = (visits: SensoryHubVisit[]) => {
  const purposeCounts = visits.reduce((acc, visit) => {
    const purpose = visit.purpose === 'look_around' ? 'Look Around' : 'Use Service';
    acc[purpose] = (acc[purpose] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(purposeCounts).map(([purpose, count]) => ({
    purpose,
    count
  }));
};

const getVisitsByUserType = (visits: SensoryHubVisit[]) => {
  const userTypeCounts = visits.reduce((acc, visit) => {
    const userType = visit.userType === 'crew' ? 'Crew' : 'Public';
    acc[userType] = (acc[userType] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(userTypeCounts).map(([userType, count]) => ({
    userType,
    count
  }));
};

const getVisitsByTeam = (visits: SensoryHubVisit[]) => {
  const crewVisits = visits.filter(visit => visit.userType === 'crew' && visit.teamName);
  const teamCounts = crewVisits.reduce((acc, visit) => {
    const team = visit.teamName || 'Unknown Team';
    acc[team] = (acc[team] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  return Object.entries(teamCounts).map(([team, count]) => ({
    team,
    count
  }));
};

export const SensoryHubCharts: React.FC<SensoryHubChartsProps> = ({ visits }) => {
  const [selectedHour, setSelectedHour] = useState<number | null>(null);
  const [selectedVisit, setSelectedVisit] = useState<SensoryHubVisit | null>(null);
  const [showHourlyModal, setShowHourlyModal] = useState(false);
  const [showVisitModal, setShowVisitModal] = useState(false);

  // Function to handle clicking on a bar
  const handleBarClick = (data: any) => {
    setSelectedHour(data.hour);
    setShowHourlyModal(true);
  };

  // Function to filter visits for the selected hour
  const getVisitsForHour = (hour: number): SensoryHubVisit[] => {
    return visits.filter(visit => {
      if (!visit.visitTimestamp) return false;
      return getHours(parseISO(visit.visitTimestamp)) === hour;
    });
  };

  // Handle clicking on a specific visit
  const handleVisitClick = (visit: SensoryHubVisit) => {
    setSelectedVisit(visit);
    setShowVisitModal(true);
  };

  // Get visits for the selected hour
  const hourlyVisits = selectedHour !== null ? getVisitsForHour(selectedHour) : [];

  return (
    <>
      <Grid container spacing={3}>
        {/* Visits by Time of Day */}
        <Grid size={{ xs: 12 }}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              bgcolor: 'background.paper',
              backdropFilter: 'blur(8px)',
              borderRadius: 2
            }}
          >
            <Typography variant="h6" color="primary" gutterBottom>
              Visits by Time of Day
            </Typography>
            <div style={{ height: 300, width: '100%' }}>
              <ResponsiveContainer>
                <BarChart data={getVisitsByTimeOfDay(visits)}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="label" interval={2} />
                  <YAxis />
                  <Tooltip formatter={(value: number) => [value, 'Visits']} />
                  <Bar
                    dataKey="count"
                    fill="#662D91"
                    name="Visits"
                    onClick={handleBarClick}
                    cursor="pointer"
                  >
                    {getVisitsByTimeOfDay(visits).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.count > 0 ? '#662D91' : '#E0E0E0'} />
                    ))}
                  </Bar>
                </BarChart>
              </ResponsiveContainer>
            </div>
          </Paper>
        </Grid>

        {/* Visits by Purpose */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              bgcolor: 'background.paper',
              backdropFilter: 'blur(8px)',
              borderRadius: 2,
              height: '100%'
            }}
          >
            <Typography variant="h6" color="primary" gutterBottom>
              Visits by Purpose
            </Typography>
            <div style={{ height: 300, width: '100%' }}>
              <ResponsiveContainer>
                <PieChart>
                  <Pie
                    data={getVisitsByPurpose(visits)}
                    dataKey="count"
                    nameKey="purpose"
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    label
                  >
                    {getVisitsByPurpose(visits).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Paper>
        </Grid>

        {/* Visits by User Type */}
        <Grid size={{ xs: 12, md: 6 }}>
          <Paper
            elevation={3}
            sx={{
              p: 3,
              bgcolor: 'background.paper',
              backdropFilter: 'blur(8px)',
              borderRadius: 2,
              height: '100%'
            }}
          >
            <Typography variant="h6" color="primary" gutterBottom>
              Visits by User Type
            </Typography>
            <div style={{ height: 300, width: '100%' }}>
              <ResponsiveContainer>
                <PieChart>
                  <Pie
                    data={getVisitsByUserType(visits)}
                    dataKey="count"
                    nameKey="userType"
                    cx="50%"
                    cy="50%"
                    outerRadius={100}
                    label
                  >
                    {getVisitsByUserType(visits).map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            </div>
          </Paper>
        </Grid>

        {/* Team Breakdown (only show if there are crew visits) */}
        {getVisitsByTeam(visits).length > 0 && (
          <Grid size={{ xs: 12 }}>
            <Paper
              elevation={3}
              sx={{
                p: 3,
                bgcolor: 'background.paper',
                backdropFilter: 'blur(8px)',
                borderRadius: 2
              }}
            >
              <Typography variant="h6" color="primary" gutterBottom>
                Crew Visits by Team
              </Typography>
              <div style={{ height: 300, width: '100%' }}>
                <ResponsiveContainer>
                  <BarChart data={getVisitsByTeam(visits)}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="team" />
                    <YAxis />
                    <Tooltip />
                    <Legend />
                    <Bar dataKey="count" name="Visits">
                      {getVisitsByTeam(visits).map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                      ))}
                    </Bar>
                  </BarChart>
                </ResponsiveContainer>
              </div>
            </Paper>
          </Grid>
        )}
      </Grid>
      
      {/* Modal for showing all visits in the selected hour */}
      <Dialog
        open={showHourlyModal}
        onClose={() => setShowHourlyModal(false)}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            bgcolor: 'background.paper',
          }
        }}
      >
        <DialogTitle>
          <Typography variant="h5" fontWeight="bold">
            {selectedHour !== null
              ? `Visits at ${getVisitsByTimeOfDay(visits).find(d => d.hour === selectedHour)?.label || ''}`
              : 'Visits'
            }
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            {hourlyVisits.length} {hourlyVisits.length === 1 ? 'visit' : 'visits'} recorded
          </Typography>
        </DialogTitle>
        <DialogContent dividers>
          {hourlyVisits.length > 0 ? (
            <List>
              {hourlyVisits.map((visit, index) => (
                <React.Fragment key={visit._id || index}>
                  <ListItem
                    component="button"
                    onClick={() => handleVisitClick(visit)}
                    sx={{
                      borderRadius: 1,
                      '&:hover': { bgcolor: 'action.hover' },
                      transition: 'background-color 0.2s'
                    }}
                  >
                    <ListItemText
                      primary={
                        <Typography variant="subtitle1" fontWeight="medium">
                          {visit.purpose === 'look_around' ? 'Look Around' : 'Use Service'}
                        </Typography>
                      }
                      secondary={
                        <Box>
                          <Typography variant="body2" component="span">
                            {visit.userType === 'crew' ? 'Crew' : 'Public'}
                            {visit.teamName && ` - ${visit.teamName}`}
                          </Typography>
                          <Typography variant="body2" component="div" color="text.secondary">
                            {new Date(visit.visitTimestamp).toLocaleString()}
                          </Typography>
                        </Box>
                      }
                    />
                  </ListItem>
                  {index < hourlyVisits.length - 1 && <Divider component="li" />}
                </React.Fragment>
              ))}
            </List>
          ) : (
            <Typography variant="body1" align="center" sx={{ py: 2 }}>
              No visit details available for this time period.
            </Typography>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowHourlyModal(false)} variant="contained">
            Close
          </Button>
        </DialogActions>
      </Dialog>

      {/* Modal for showing detailed visit information */}
      <Dialog
        open={showVisitModal}
        onClose={() => setShowVisitModal(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            bgcolor: 'background.paper',
          }
        }}
      >
        <DialogTitle>
          <Typography variant="h5" fontWeight="bold">
            Visit Details
          </Typography>
        </DialogTitle>
        <DialogContent dividers>
          {selectedVisit && (
            <Box>
              <Typography variant="subtitle1" gutterBottom>
                <strong>Purpose:</strong> {selectedVisit.purpose === 'look_around' ? 'Look Around' : 'Use Service'}
              </Typography>
              <Typography variant="subtitle1" gutterBottom>
                <strong>User Type:</strong> {selectedVisit.userType === 'crew' ? 'Crew' : 'Public'}
              </Typography>
              {selectedVisit.teamName && (
                <Typography variant="subtitle1" gutterBottom>
                  <strong>Team:</strong> {selectedVisit.teamName}
                </Typography>
              )}
              <Typography variant="subtitle1" gutterBottom>
                <strong>Visit Time:</strong> {new Date(selectedVisit.visitTimestamp).toLocaleString()}
              </Typography>
              <Typography variant="subtitle1" gutterBottom>
                <strong>Festival ID:</strong> {selectedVisit.festivalId}
              </Typography>
              {selectedVisit.siteLocationId && (
                <Typography variant="subtitle1" gutterBottom>
                  <strong>Site Location:</strong> {selectedVisit.siteLocationId}
                </Typography>
              )}
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowVisitModal(false)} variant="contained">
            Close
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};