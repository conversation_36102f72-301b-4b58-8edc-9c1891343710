import React, { useState, useEffect } from 'react';
import { databaseService } from '../services/database/index';
import { WelfareAdmission } from '../types/admission';
import { ItemDocument, LostPropertyItem } from '../types/item';
import { ShiftAssignment } from '../types/shift';
import { Feedback } from '../types/feedback';
import {
  Typography,
  Button,
  Stack,
  CircularProgress,
  Alert,
  Box,
  alpha,
} from '@mui/material';
import DownloadIcon from '@mui/icons-material/Download';
import DeleteIcon from '@mui/icons-material/Delete';
import { FeedbackAdminSection } from './feedback/FeedbackAdminSection';

const downloadCSV = (data: any[], filename: string) => {
  // Convert object to CSV
  const headers = Object.keys(data[0] || {}).filter(key => !key.startsWith('_'));
  const csvContent = [
    headers.join(','),
    ...data.map(item => 
      headers.map(header => 
        JSON.stringify(item[header] || '')
      ).join(',')
    )
  ].join('\n');

  // Create and trigger download
  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

const clearAllData = async (type: string, festivalId: string) => {
  if (!window.confirm(`Are you sure you want to clear all ${type} data for this festival? This action cannot be undone.`)) {
    return;
  }

  try {
    let items;
    switch (type) {
      case 'admissions':
        items = await databaseService.getAdmissionsByFestival(festivalId);
        for (const item of items) {
          await databaseService.deleteAdmission(item._id);
        }
        break;
      case 'items':
        items = await databaseService.getItemCountsByFestival(festivalId);
        for (const item of items) {
          await databaseService.deleteItemCount(item._id);
        }
        break;
      case 'lostProperty':
        items = await databaseService.getLostPropertyItems();
        for (const item of items) {
          await databaseService.deleteLostPropertyItem(item._id);
        }
        break;
      case 'shifts':
        items = await databaseService.getShiftAssignments(festivalId);
        for (const item of items) {
          await databaseService.deleteShiftAssignment(item._id);
        }
        break;
      case 'feedback':
        items = await databaseService.getAllFeedback();
        for (const item of items) {
          await databaseService.deleteFeedback(item._id);
        }
        break;
    }
    alert(`All ${type} data has been cleared successfully.`);
  } catch (error) {
    console.error('Error clearing data:', error);
    alert(`Failed to clear ${type} data. Please try again.`);
  }
};

interface AdminPanelProps {
  festivalId: string | null;
  type: 'admissions' | 'items' | 'lostProperty' | 'shifts' | 'feedback';
}

export const AdminPanel: React.FC<AdminPanelProps> = ({ festivalId, type }) => {
  const [data, setData] = useState<(WelfareAdmission | ItemDocument | LostPropertyItem | ShiftAssignment | Feedback)[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      if (!festivalId && type !== 'feedback') {
        setData([]);
        setLoading(false);
        return;
      }

      setLoading(true);
      setError(null);

      try {
        let result;
        switch (type) {
          case 'admissions':
            result = await databaseService.getAdmissionsByFestival(festivalId!);
            break;
          case 'items':
            result = await databaseService.getItemCountsByFestival(festivalId!);
            break;
          case 'lostProperty':
            result = await databaseService.getLostPropertyItems();
            break;
          case 'shifts':
            result = await databaseService.getShiftAssignments(festivalId!);
            break;
          case 'feedback':
            result = await databaseService.getAllFeedback();
            break;
          default:
            throw new Error('Invalid type specified');
        }
        setData(result);
      } catch (err) {
        console.error('Error fetching data:', err);
        setError('Failed to load data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [festivalId, type]);

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  if (!festivalId && type !== 'feedback') {
    return (
      <Alert severity="info">
        Please select a festival first
      </Alert>
    );
  }

  if (type === 'feedback') {
    return <FeedbackAdminSection />;
  }

  return (
    <Stack spacing={2}>
      <Typography variant="h5" component="h2" sx={{ 
        fontWeight: 'bold',
        color: 'ithink.purple'
      }}>
        {type.charAt(0).toUpperCase() + type.slice(1)} Management
      </Typography>

      <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ width: '100%' }}>
        <Button
          variant="outlined"
          size="small"
          sx={{
            color: 'ithink.purple',
            borderColor: 'ithink.purple',
            '&:hover': {
              backgroundColor: theme => alpha(theme.palette.ithink.purple, 0.04),
              borderColor: 'ithink.purple',
            },
            flex: 1,
            minHeight: '64px',
            borderRadius: '12px',
            display: 'flex',
            flexDirection: 'column',
            gap: '4px',
            textAlign: 'center',
            lineHeight: 1.2,
            px: 3,
            py: 2
          }}
          onClick={() => downloadCSV(data, `${type}-${festivalId}`)}
        >
          <DownloadIcon sx={{ mb: 0.5 }} />
          <Box component="span" sx={{ fontSize: '0.875rem' }}>
            Download<br />{type} CSV
          </Box>
        </Button>
        <Button
          variant="outlined"
          size="small"
          sx={{
            color: 'error.main',
            borderColor: 'error.main',
            '&:hover': {
              backgroundColor: theme => alpha(theme.palette.error.main, 0.04),
              borderColor: 'error.main',
            },
            flex: 1,
            minHeight: '64px',
            borderRadius: '12px',
            display: 'flex',
            flexDirection: 'column',
            gap: '4px',
            textAlign: 'center',
            lineHeight: 1.2,
            px: 3,
            py: 2
          }}
          onClick={() => clearAllData(type, festivalId!)}
        >
          <DeleteIcon sx={{ mb: 0.5 }} />
          <Box component="span" sx={{ fontSize: '0.875rem' }}>
            Clear All<br />{type} Data
          </Box>
        </Button>
      </Stack>
    </Stack>
  );
};
