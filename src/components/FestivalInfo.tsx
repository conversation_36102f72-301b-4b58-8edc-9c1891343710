import React, { useState } from 'react';
import { useFestival } from '../contexts/FestivalContext';
import {
  Box,
  IconButton,
  Menu,
  MenuItem,
  Link,
} from '@mui/material';
import InfoIcon from '@mui/icons-material/Info';

export const FestivalInfo: React.FC = () => {
  const { activeFestival } = useFestival();
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  if (!activeFestival) {
    return null;
  }

  const hasLinks = activeFestival.mainUrl || activeFestival.mapUrl || 
                  activeFestival.travelInfoUrl || activeFestival.faqsUrl;

  if (!hasLinks) {
    return null;
  }

  return (
    <Box>
      <IconButton
        onClick={handleClick}
        aria-controls={open ? 'festival-info-menu' : undefined}
        aria-haspopup="true"
        aria-expanded={open ? 'true' : undefined}
        sx={{
          color: 'text.secondary',
          '&:hover': {
            color: 'text.primary',
            bgcolor: 'action.hover',
          },
        }}
      >
        <InfoIcon />
      </IconButton>

      <Menu
        id="festival-info-menu"
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        MenuListProps={{
          'aria-labelledby': 'festival-info-button',
        }}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
      >
        {activeFestival.mainUrl && (
          <MenuItem onClick={handleClose}>
            <Link
              href={activeFestival.mainUrl}
              target="_blank"
              rel="noopener noreferrer"
              underline="none"
              color="inherit"
              sx={{ width: '100%' }}
            >
              Main Website
            </Link>
          </MenuItem>
        )}
        {activeFestival.mapUrl && (
          <MenuItem onClick={handleClose}>
            <Link
              href={activeFestival.mapUrl}
              target="_blank"
              rel="noopener noreferrer"
              underline="none"
              color="inherit"
              sx={{ width: '100%' }}
            >
              Festival Map
            </Link>
          </MenuItem>
        )}
        {activeFestival.travelInfoUrl && (
          <MenuItem onClick={handleClose}>
            <Link
              href={activeFestival.travelInfoUrl}
              target="_blank"
              rel="noopener noreferrer"
              underline="none"
              color="inherit"
              sx={{ width: '100%' }}
            >
              Travel Information
            </Link>
          </MenuItem>
        )}
        {activeFestival.faqsUrl && (
          <MenuItem onClick={handleClose}>
            <Link
              href={activeFestival.faqsUrl}
              target="_blank"
              rel="noopener noreferrer"
              underline="none"
              color="inherit"
              sx={{ width: '100%' }}
            >
              FAQs
            </Link>
          </MenuItem>
        )}
      </Menu>
    </Box>
  );
};
