import React, { useState, useEffect, useRef } from 'react';
import { databaseService } from '../services/database/index';
import { useFestival } from '../contexts/FestivalContext';
import {
  Drawer,
  Box,
  Typography,
  IconButton,
  TextField,
  Alert,
  Paper,
  Icon,
} from '@mui/material';

interface ShiftNotesProps {
  onClose: () => void;
}

export const ShiftNotes: React.FC<ShiftNotesProps> = ({ onClose }) => {
  const [notes, setNotes] = useState('');
  const [isSaving, setIsSaving] = useState(false);
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const { activeFestival } = useFestival();
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Save notes function
  const saveNotes = async () => {
    if (activeFestival) {
      try {
        setIsSaving(true);
        await databaseService.updateFestivalNotes(activeFestival._id, notes);
        setError('');
      } catch (error) {
        console.error('Error saving notes:', error);
        setError('Failed to save notes');
      } finally {
        setIsSaving(false);
      }
    }
  };

  // Handle close with save
  const handleClose = async () => {
    await saveNotes();
    onClose();
  };

  useEffect(() => {
    const loadNotes = async () => {
      if (!activeFestival) {
        setNotes('');
        return;
      }

      try {
        setIsLoading(true);
        const festivalNotes = await databaseService.getFestivalNotes(activeFestival._id);
        setNotes(festivalNotes || '');
      } catch (error) {
        console.error('Error loading notes:', error);
        setError('Failed to load notes');
        setNotes('');
      } finally {
        setIsLoading(false);
      }
    };

    loadNotes();
  }, [activeFestival]);

  // Disable textarea if no active festival
  const isDisabled = !activeFestival;

  // Auto-focus textarea when opened
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.focus();
    }
  }, []);

  return (
    <Drawer
      anchor="right"
      open={true}
      onClose={handleClose}
      sx={{
        '& .MuiDrawer-paper': {
          width: '100%',
          maxWidth: '32rem',
        },
      }}
    >
      <Paper 
        elevation={0} 
        sx={{ 
          display: 'flex', 
          flexDirection: 'column', 
          height: '100%',
          borderRadius: 0,
        }}
      >
        {/* Header */}
        <Box sx={{ 
          px: 3, 
          py: 2, 
          display: 'flex', 
          alignItems: 'center', 
          justifyContent: 'space-between',
          borderBottom: 1,
          borderColor: 'divider',
        }}>
          <Typography variant="h6" component="h2">
            Notes for Next Shift
          </Typography>
          <IconButton
            onClick={handleClose}
            edge="end"
            aria-label="close"
            size="small"
          >
            <Icon>close</Icon>
          </IconButton>
        </Box>

        {/* Notes textarea */}
        <Box sx={{ 
          flex: 1, 
          p: 3,
          display: 'flex',
          flexDirection: 'column',
        }}>
          <TextField
            inputRef={textareaRef}
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            onBlur={saveNotes}
            multiline
            fullWidth
            variant="outlined"
            placeholder={isDisabled ? "Select a festival to add notes" : "Enter notes for the next shift..."}
            disabled={isDisabled}
            sx={{
              flex: 1,
              '& .MuiInputBase-root': {
                height: '100%',
              },
              '& .MuiInputBase-input': {
                height: '100% !important',
                overflow: 'auto !important',
              },
            }}
          />
        </Box>

        {/* Status messages */}
        <Box sx={{ borderTop: 1, borderColor: 'divider' }}>
          {isSaving && (
            <Alert severity="info" sx={{ borderRadius: 0 }}>
              Saving...
            </Alert>
          )}
          {error && (
            <Alert severity="error" sx={{ borderRadius: 0 }}>
              {error}
            </Alert>
          )}
          {isLoading && (
            <Alert severity="info" sx={{ borderRadius: 0 }}>
              Loading...
            </Alert>
          )}
        </Box>
      </Paper>
    </Drawer>
  );
};
