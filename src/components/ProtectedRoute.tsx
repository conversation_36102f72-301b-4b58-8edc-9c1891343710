import React from 'react';
import { Navigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface ProtectedRouteProps {
  feature: string;
  children: React.ReactNode;
  fallbackPath?: string;
}

/**
 * A component that restricts access to routes based on user permissions.
 * If the user doesn't have access to the specified feature, they are redirected to the fallback path.
 * 
 * @param feature - The feature name to check access for
 * @param children - The components to render if access is granted
 * @param fallbackPath - The path to redirect to if access is denied (defaults to '/')
 */
export const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  feature,
  children,
  fallbackPath = '/',
}) => {
  const { hasAccess } = useAuth();
  
  if (!hasAccess(feature)) {
    return <Navigate to={fallbackPath} replace />;
  }
  
  return <>{children}</>;
};