import React from 'react';
import { FestivalSelector } from '../FestivalSelector';
import {
  Paper,
  Box,
  Button,
  Stack,
} from '@mui/material';

interface FestivalHeaderProps {
  onEditClick: () => void;
  onNotesClick: () => void;
  onSyncClick: () => void;
}

export const FestivalHeader: React.FC<FestivalHeaderProps> = ({
  onEditClick,
  onNotesClick,
  onSyncClick,
}) => {
  return (
    <Paper
      elevation={1}
      sx={{
        bgcolor: 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(8px)',
        p: { xs: 2, sm: 3 },
        mb: 3,
        borderRadius: 1,
      }}
    >
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <Box sx={{ flex: 1 }}>
          <FestivalSelector />
        </Box>
        <Stack direction="row" spacing={1}>
          <Button
            variant="outlined"
            onClick={onEditClick}
            size="medium"
            sx={{
              color: 'text.primary',
              borderColor: 'divider',
              '&:hover': {
                bgcolor: 'action.hover',
                borderColor: 'divider',
              },
            }}
          >
            Edit Festival
          </Button>
          <Button
            variant="outlined"
            onClick={onNotesClick}
            size="medium"
            sx={{
              color: 'text.primary',
              borderColor: 'divider',
              '&:hover': {
                bgcolor: 'action.hover',
                borderColor: 'divider',
              },
            }}
          >
            Notes
          </Button>
          <Button
            variant="contained"
            onClick={onSyncClick}
            size="medium"
            sx={{
              bgcolor: 'ithink.pink',
              '&:hover': {
                bgcolor: 'ithink.pinkLight',
              },
            }}
          >
            Sync Now
          </Button>
        </Stack>
      </Box>
    </Paper>
  );
};
