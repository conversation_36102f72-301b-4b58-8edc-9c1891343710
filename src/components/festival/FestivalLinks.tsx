import React, { useState } from 'react';
import { Festival } from '../../types/festival';
import { LinkIcon } from '@heroicons/react/24/outline';
import {
  IconButton,
  Menu,
  MenuItem,
  Box,
  Typography,
} from '@mui/material';

interface FestivalLinksProps {
  festival: Festival;
}

export const FestivalLinks: React.FC<FestivalLinksProps> = ({ festival }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  const links = [
    { name: 'Main Website', url: festival.mainUrl },
    { name: 'Map', url: festival.mapUrl },
    { name: 'Travel Info', url: festival.travelInfoUrl },
    { name: 'FAQs', url: festival.faqsUrl },
  ].filter(link => link.url);

  if (links.length === 0) return null;

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    event.stopPropagation(); // Prevent event bubbling to parent
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  return (
    <Box>
      <IconButton
        onClick={handleClick}
        title="Festival Links"
        size="small"
        sx={{
          color: 'black',
          '&:hover': {
            bgcolor: 'rgba(0, 0, 0, 0.1)',
          },
        }}
      >
        <Box
          component={LinkIcon}
          sx={{
            width: 20,
            height: 20,
          }}
        />
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'top',
          horizontal: 'right',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        sx={{
          '& .MuiPaper-root': {
            width: 200,
            bgcolor: 'background.paper',
            boxShadow: 3,
            borderRadius: 1,
            mt: 1,
          },
        }}
      >
        {links.map(({ name, url }) => (
          <MenuItem
            key={name}
            onClick={handleClose}
            component="a"
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            sx={{
              py: 1,
              '&:hover': {
                bgcolor: 'action.hover',
              },
            }}
          >
            <Typography variant="body2" color="text.primary">
              {name}
            </Typography>
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};
