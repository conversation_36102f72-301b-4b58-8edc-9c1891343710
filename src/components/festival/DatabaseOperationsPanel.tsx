import React, { useState } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>ack,
  CircularProgress,
  Alert,
  Box,
  alpha,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
} from '@mui/material';
import StorageIcon from '@mui/icons-material/Storage';
import CleaningServicesIcon from '@mui/icons-material/CleaningServices';
import DownloadIcon from '@mui/icons-material/Download';
import { databaseService } from '../../services/database/index';

interface DatabaseOperationsPanelProps {
  festivalId: string | null;
}

type OperationType = 'clear' | 'cleanup' | 'export' | null;

export const DatabaseOperationsPanel: React.FC<DatabaseOperationsPanelProps> = ({ festivalId }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [currentOperation, setCurrentOperation] = useState<OperationType>(null);

  const handleClearDatabase = async () => {
    setConfirmDialogOpen(false);
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      await databaseService.resetDatabaseWithFreshSync();
      setSuccess('Database cleared successfully. Fresh data will be synced.');
    } catch (err) {
      console.error('Error clearing database:', err);
      setError(err instanceof Error ? err.message : 'Failed to clear database. Please try again.');
    } finally {
      setLoading(false);
      setCurrentOperation(null);
    }
  };

  const handleCleanupDatabase = async () => {
    setConfirmDialogOpen(false);
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const result = await databaseService.performDatabaseCleanup();
      if (result.errors.length > 0) {
        setError(`Cleanup completed with some errors: ${result.errors.join(', ')}`);
      } else {
        setSuccess(`Database cleanup completed successfully. Cleaned ${result.totalCleaned} old records.`);
      }
    } catch (err) {
      console.error('Error cleaning database:', err);
      setError(err instanceof Error ? err.message : 'Failed to cleanup database. Please try again.');
    } finally {
      setLoading(false);
      setCurrentOperation(null);
    }
  };

  const handleExportDatabase = async () => {
    setLoading(true);
    setError(null);
    setSuccess(null);

    try {
      const blob = await databaseService.exportCompleteDatabase('json');
      
      // Create download link
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `database-export-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      setSuccess('Database exported successfully.');
    } catch (err) {
      console.error('Error exporting database:', err);
      setError(err instanceof Error ? err.message : 'Failed to export database. Please try again.');
    } finally {
      setLoading(false);
      setCurrentOperation(null);
    }
  };

  const openConfirmDialog = (operation: OperationType) => {
    setCurrentOperation(operation);
    setConfirmDialogOpen(true);
    setError(null);
    setSuccess(null);
  };

  const handleConfirmAction = () => {
    switch (currentOperation) {
      case 'clear':
        handleClearDatabase();
        break;
      case 'cleanup':
        handleCleanupDatabase();
        break;
      default:
        setConfirmDialogOpen(false);
        setCurrentOperation(null);
    }
  };

  const getDialogContent = () => {
    switch (currentOperation) {
      case 'clear':
        return {
          title: 'Clear Local Database Cache',
          description: 'This will clear all local data and resync from the server. This operation is useful for resolving sync issues on iPads that cannot clear their browser cache.\n\nWarning: All unsaved changes will be lost. Are you sure you want to continue?',
          action: 'Clear Database'
        };
      case 'cleanup':
        return {
          title: 'Cleanup Old Database Records',
          description: 'This will remove old records (older than 3 months) including discharged admissions, resolved feedback, and completed shifts. This helps improve database performance.\n\nAre you sure you want to continue?',
          action: 'Cleanup Database'
        };
      default:
        return {
          title: '',
          description: '',
          action: ''
        };
    }
  };

  const dialogContent = getDialogContent();

  return (
    <Stack spacing={2}>
      <Typography variant="h5" component="h2" sx={{
        fontWeight: 'bold',
        color: 'ithink.purple'
      }}>
        Database Operations
      </Typography>

      <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
        Database management tools for administrators
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 2 }}>
          {success}
        </Alert>
      )}

      <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2} sx={{ width: '100%' }}>
        <Button
          variant="outlined"
          size="small"
          disabled={loading}
          sx={{
            color: 'warning.main',
            borderColor: 'warning.main',
            '&:hover': {
              backgroundColor: theme => alpha(theme.palette.warning.main, 0.04),
              borderColor: 'warning.main',
            },
            '&:disabled': {
              opacity: 0.6,
            },
            flex: 1,
            minHeight: '64px',
            borderRadius: '12px',
            display: 'flex',
            flexDirection: 'column',
            gap: '4px',
            textAlign: 'center',
            lineHeight: 1.2,
            px: 3,
            py: 2
          }}
          onClick={() => openConfirmDialog('clear')}
        >
          {loading && currentOperation === 'clear' ? (
            <CircularProgress size={24} sx={{ mb: 0.5 }} />
          ) : (
            <StorageIcon sx={{ mb: 0.5 }} />
          )}
          <Box component="span" sx={{ fontSize: '0.875rem' }}>
            {loading && currentOperation === 'clear' ? 'Clearing...' : 'Clear Local<br />Database Cache'}
          </Box>
        </Button>

        <Button
          variant="outlined"
          size="small"
          disabled={loading}
          sx={{
            color: 'info.main',
            borderColor: 'info.main',
            '&:hover': {
              backgroundColor: theme => alpha(theme.palette.info.main, 0.04),
              borderColor: 'info.main',
            },
            '&:disabled': {
              opacity: 0.6,
            },
            flex: 1,
            minHeight: '64px',
            borderRadius: '12px',
            display: 'flex',
            flexDirection: 'column',
            gap: '4px',
            textAlign: 'center',
            lineHeight: 1.2,
            px: 3,
            py: 2
          }}
          onClick={() => openConfirmDialog('cleanup')}
        >
          {loading && currentOperation === 'cleanup' ? (
            <CircularProgress size={24} sx={{ mb: 0.5 }} />
          ) : (
            <CleaningServicesIcon sx={{ mb: 0.5 }} />
          )}
          <Box component="span" sx={{ fontSize: '0.875rem' }}>
            {loading && currentOperation === 'cleanup' ? 'Cleaning...' : 'Cleanup Old<br />Records'}
          </Box>
        </Button>

        <Button
          variant="outlined"
          size="small"
          disabled={loading}
          sx={{
            color: 'ithink.purple',
            borderColor: 'ithink.purple',
            '&:hover': {
              backgroundColor: theme => alpha(theme.palette.ithink.purple, 0.04),
              borderColor: 'ithink.purple',
            },
            '&:disabled': {
              opacity: 0.6,
            },
            flex: 1,
            minHeight: '64px',
            borderRadius: '12px',
            display: 'flex',
            flexDirection: 'column',
            gap: '4px',
            textAlign: 'center',
            lineHeight: 1.2,
            px: 3,
            py: 2
          }}
          onClick={() => {
            setCurrentOperation('export');
            handleExportDatabase();
          }}
        >
          {loading && currentOperation === 'export' ? (
            <CircularProgress size={24} sx={{ mb: 0.5 }} />
          ) : (
            <DownloadIcon sx={{ mb: 0.5 }} />
          )}
          <Box component="span" sx={{ fontSize: '0.875rem' }}>
            {loading && currentOperation === 'export' ? 'Exporting...' : 'Export Complete<br />Database'}
          </Box>
        </Button>
      </Stack>

      <Dialog
        open={confirmDialogOpen}
        onClose={() => setConfirmDialogOpen(false)}
        aria-labelledby="confirm-dialog-title"
        aria-describedby="confirm-dialog-description"
      >
        <DialogTitle id="confirm-dialog-title">
          {dialogContent.title}
        </DialogTitle>
        <DialogContent>
          <DialogContentText id="confirm-dialog-description" sx={{ whiteSpace: 'pre-line' }}>
            {dialogContent.description}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setConfirmDialogOpen(false)}
            color="inherit"
          >
            Cancel
          </Button>
          <Button
            onClick={handleConfirmAction}
            color={currentOperation === 'clear' ? 'warning' : 'primary'}
            variant="contained"
            autoFocus
          >
            {dialogContent.action}
          </Button>
        </DialogActions>
      </Dialog>
    </Stack>
  );
};