import React from 'react';
import { Festival } from '../../types/festival';
import { format } from 'date-fns';
import {
  Paper,
  List,
  ListItem,
  Typography,
  Chip,
  Button,
  Stack,
  Box,
  CircularProgress,
} from '@mui/material';
import { useFestival } from '../../contexts/FestivalContext';

interface FestivalListProps {
  festivals: Festival[];
  onSetActive: (festival: Festival) => Promise<void>;
  onDelete: (id: string) => Promise<void>;
}

export const FestivalList: React.FC<FestivalListProps> = ({ festivals, onSetActive, onDelete }) => {
  const { loading } = useFestival();

  return (
    <Paper
      elevation={1}
      sx={{
        bgcolor: 'rgba(255, 255, 255, 0.9)',
        backdropFilter: 'blur(8px)',
        overflow: 'hidden',
        borderRadius: 1,
      }}
    >
      <List sx={{ p: 0 }}>
        {festivals.map((festival) => (
          <ListItem
            key={festival._id}
            divider
            sx={{
              px: { xs: 2, sm: 3 },
              py: 2,
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
            }}
          >
            <Box sx={{ minWidth: 0, flex: 1 }}>
              <Stack direction="row" spacing={1} alignItems="center">
                <Typography
                  variant="body2"
                  sx={{
                    fontWeight: 500,
                    color: 'ithink.pink',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis',
                    whiteSpace: 'nowrap',
                  }}
                >
                  {festival.name}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ flexShrink: 0 }}
                >
                  {festival.type === 'festival' ? 'Festival' : 'Regular Event'}
                </Typography>
              </Stack>
              <Typography
                variant="body2"
                color="text.secondary"
                sx={{ mt: 1 }}
              >
                {format(new Date(festival.startDate), 'MMM d, yyyy')} -{' '}
                {format(new Date(festival.endDate), 'MMM d, yyyy')}
              </Typography>
            </Box>
            <Stack direction="row" spacing={1} sx={{ ml: 2, flexShrink: 0 }}>
              {festival.isActive ? (
                <Chip
                  label={loading ? 'Updating...' : 'Active'}
                  size="small"
                  color="success"
                  icon={loading ? <CircularProgress size={16} /> : undefined}
                  sx={{
                    height: 24,
                    fontSize: '0.75rem',
                  }}
                />
              ) : (
                <Button
                  onClick={() => onSetActive(festival)}
                  size="small"
                  variant="outlined"
                  disabled={loading}
                  sx={{
                    textTransform: 'none',
                    minWidth: 'auto',
                    px: 1.5,
                    py: 0.5,
                    fontSize: '0.75rem',
                    borderColor: 'divider',
                    color: 'text.primary',
                    '&:hover': {
                      bgcolor: 'action.hover',
                      borderColor: 'divider',
                    },
                  }}
                >
                  {loading ? 'Setting...' : 'Set Active'}
                </Button>
              )}
              <Button
                onClick={() => onDelete(festival._id!)}
                size="small"
                color="error"
                variant="contained"
                disabled={loading}
                sx={{
                  textTransform: 'none',
                  minWidth: 'auto',
                  px: 1.5,
                  py: 0.5,
                  fontSize: '0.75rem',
                  bgcolor: 'error.light',
                  '&:hover': {
                    bgcolor: 'error.main',
                  },
                }}
              >
                {loading ? 'Deleting...' : 'Delete'}
              </Button>
            </Stack>
          </ListItem>
        ))}
      </List>
    </Paper>
  );
};
