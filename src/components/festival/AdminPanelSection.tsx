import React from 'react';
import { AdminPanel } from '../AdminPanel';
import { DatabaseOperationsPanel } from './DatabaseOperationsPanel';
import {
  Box,
  Typography,
  Grid,
} from '@mui/material';

interface AdminPanelSectionProps {
  festivalId: string | null;
}

export const AdminPanelSection: React.FC<AdminPanelSectionProps> = ({ festivalId }) => {
  return (
    <Box sx={{ mt: 4 }}>
      <Typography
        variant="h4"
        component="h2"
        sx={{
          mb: 3,
          fontWeight: 'bold',
          color: 'text.primary',
        }}
      >
        Admin Panel
      </Typography>
      <Grid container spacing={3}>
        <Grid size={{ xs: 12, md: 6, lg: 3 }}>
          <AdminPanel festivalId={festivalId} type="admissions" />
        </Grid>
        <Grid size={{ xs: 12, md: 6, lg: 3 }}>
          <AdminPanel festivalId={festivalId} type="items" />
        </Grid>
        <Grid size={{ xs: 12, md: 6, lg: 3 }}>
          <AdminPanel festivalId={festivalId} type="lostProperty" />
        </Grid>
        <Grid size={{ xs: 12, md: 6, lg: 3 }}>
          <AdminPanel festivalId={festivalId} type="shifts" />
        </Grid>
        <Grid size={{ xs: 12, md: 6, lg: 3 }}>
          <DatabaseOperationsPanel festivalId={festivalId} />
        </Grid>
      </Grid>
    </Box>
  );
};
