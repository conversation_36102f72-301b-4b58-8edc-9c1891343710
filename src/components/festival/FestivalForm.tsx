import React from 'react';
import {
  Box,
  Button,
  Checkbox,
  FormControlLabel,
  Grid,
  Paper,
  TextField,
  Typography,
  FormGroup,
  MenuItem,
  Select,
  InputLabel,
  FormControl,
} from '@mui/material';
import { FestivalLocation } from '../../types/festival';

interface FestivalFormData {
  name: string;
  startDate: string;
  endDate: string;
  location: string;
  type: 'festival' | 'regular_event';
  mainUrl: string;
  mapUrl: string;
  travelInfoUrl: string;
  faqsUrl: string;
  showAdmissions: boolean;
  showFrontOfHouse: boolean;
  showLostProperty: boolean;
  showShifts: boolean;
  hasMultipleLocations?: boolean;
  locations?: FestivalLocation[];
}

interface FestivalFormProps {
  formData: FestivalFormData;
  onSubmit: (e: React.FormEvent) => Promise<void>;
  onChange: (data: FestivalFormData) => void;
}

export const FestivalForm: React.FC<FestivalFormProps> = ({
  formData,
  onSubmit,
  onChange,
}) => {
  const handleChange = (field: keyof FestivalFormData) => (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    onChange({
      ...formData,
      [field]: e.target.type === 'checkbox' ? e.target.checked : e.target.value,
    });
  };

  const [hasArena, setHasArena] = React.useState(true);
  const [hasCampsite, setHasCampsite] = React.useState(false);

  const handleSubmitWithLocations = async (e: React.FormEvent) => {
    e.preventDefault();
    
    const locations: FestivalLocation[] = [];
    
    if (hasArena) {
      locations.push({
        id: `arena_${Date.now()}`,
        name: 'Arena',
        type: 'arena',
        description: 'Main arena area'
      });
    }
    
    if (hasCampsite) {
      locations.push({
        id: `campsite_${Date.now()}`,
        name: 'Campsite',
        type: 'campsite',
        description: 'Festival campsite'
      });
    }

    // Update formData with locations
    // Set hasMultipleLocations to true if there are any locations
    onChange({
      ...formData,
      hasMultipleLocations: locations.length > 0,
      locations
    });

    await onSubmit(e);
  };

  return (
    <Paper sx={{ p: 3 }}>
      <form onSubmit={handleSubmitWithLocations}>
        <Grid container spacing={3}>
          <Grid size={{ xs: 12 }}>
            <Typography variant="h6" gutterBottom>
              Create New Festival
            </Typography>
          </Grid>

          <Grid size={{ xs: 12, sm: 6 }}>
            <TextField
              fullWidth
              required
              label="Festival Name"
              value={formData.name}
              onChange={handleChange('name')}
            />
          </Grid>

          <Grid size={{ xs: 12, sm: 6 }}>
            <FormControl fullWidth>
              <InputLabel>Event Type</InputLabel>
              <Select
                value={formData.type}
                label="Event Type"
                onChange={(e) => handleChange('type')({ target: { value: e.target.value } } as any)}
              >
                <MenuItem value="festival">Festival</MenuItem>
                <MenuItem value="regular_event">Regular Event</MenuItem>
              </Select>
            </FormControl>
          </Grid>

          <Grid size={{ xs: 12, sm: 6 }}>
            <TextField
              fullWidth
              required
              type="date"
              label="Start Date"
              value={formData.startDate}
              onChange={handleChange('startDate')}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>

          <Grid size={{ xs: 12, sm: 6 }}>
            <TextField
              fullWidth
              required
              type="date"
              label="End Date"
              value={formData.endDate}
              onChange={handleChange('endDate')}
              InputLabelProps={{ shrink: true }}
            />
          </Grid>

          <Grid size={{ xs: 12 }}>
            <Typography variant="subtitle1" gutterBottom>
              Festival Locations
            </Typography>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={hasArena}
                    onChange={(e) => setHasArena(e.target.checked)}
                  />
                }
                label="Arena"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={hasCampsite}
                    onChange={(e) => setHasCampsite(e.target.checked)}
                  />
                }
                label="Campsite"
              />
            </FormGroup>
          </Grid>

          <Grid size={{ xs: 12 }}>
            <Typography variant="subtitle1" gutterBottom>
              External Links
            </Typography>
          </Grid>

          <Grid size={{ xs: 12, sm: 6 }}>
            <TextField
              fullWidth
              label="Main Website URL"
              value={formData.mainUrl}
              onChange={handleChange('mainUrl')}
            />
          </Grid>

          <Grid size={{ xs: 12, sm: 6 }}>
            <TextField
              fullWidth
              label="Map URL"
              value={formData.mapUrl}
              onChange={handleChange('mapUrl')}
            />
          </Grid>

          <Grid size={{ xs: 12, sm: 6 }}>
            <TextField
              fullWidth
              label="Travel Info URL"
              value={formData.travelInfoUrl}
              onChange={handleChange('travelInfoUrl')}
            />
          </Grid>

          <Grid size={{ xs: 12, sm: 6 }}>
            <TextField
              fullWidth
              label="FAQs URL"
              value={formData.faqsUrl}
              onChange={handleChange('faqsUrl')}
            />
          </Grid>

          <Grid size={{ xs: 12 }}>
            <Typography variant="subtitle1" gutterBottom>
              Enabled Features
            </Typography>
            <FormGroup>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.showAdmissions}
                    onChange={handleChange('showAdmissions')}
                  />
                }
                label="Admissions"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.showFrontOfHouse}
                    onChange={handleChange('showFrontOfHouse')}
                  />
                }
                label="Front of House"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.showLostProperty}
                    onChange={handleChange('showLostProperty')}
                  />
                }
                label="Lost Property"
              />
              <FormControlLabel
                control={
                  <Checkbox
                    checked={formData.showShifts}
                    onChange={handleChange('showShifts')}
                  />
                }
                label="Shifts"
              />
            </FormGroup>
          </Grid>

          <Grid size={{ xs: 12 }}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
              >
                Create Festival
              </Button>
            </Box>
          </Grid>
        </Grid>
      </form>
    </Paper>
  );
};