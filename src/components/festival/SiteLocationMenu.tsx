import React, { useState } from 'react';
import { Festival } from '../../types/festival';
import { MapPinIcon } from '@heroicons/react/24/outline';
import {
  Box,
  Typography,
  Menu,
  MenuItem,
  IconButton,
} from '@mui/material';
import { useSiteLocation } from '../../contexts/SiteLocationContext';

interface SiteLocationMenuProps {
  festival: Festival;
}

export const SiteLocationMenu: React.FC<SiteLocationMenuProps> = ({ festival }) => {
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const { activeSiteLocation, setActiveSiteLocation } = useSiteLocation();
  const open = Boolean(anchorEl);

  if (!festival.hasMultipleLocations) return null;

  const handleClick = (event: React.MouseEvent<HTMLButtonElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSiteLocationSelect = (siteLocationId: string | null) => {
    if (!siteLocationId) {
      setActiveSiteLocation(null);
      handleClose();
      return;
    }
    
    const siteLocation = festival.locations.find(loc => loc.id === siteLocationId);
    if (siteLocation) {
      setActiveSiteLocation(siteLocation);
    }
    handleClose();
  };

  const getSiteLocationLabel = () => {
    if (!activeSiteLocation) return 'All Locations';
    return activeSiteLocation.type === 'arena' ? 'Arena' : 'Campsite';
  };

  return (
    <Box sx={{ mt: 1 }}>
      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
        <Typography variant="body2" color="black">
          Site:
        </Typography>
        <Typography variant="body2" color="black" sx={{ fontWeight: 'bold' }}>
          {getSiteLocationLabel()}
        </Typography>
        <IconButton
          onClick={handleClick}
          title="Change Site Location"
          size="small"
          sx={{
            color: 'black',
            '&:hover': {
              bgcolor: 'rgba(0, 0, 0, 0.05)',
            },
          }}
        >
          <Box
            component={MapPinIcon}
            sx={{
              width: 16,
              height: 16,
            }}
          />
        </IconButton>
      </Box>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        anchorOrigin={{
          vertical: 'bottom',
          horizontal: 'left',
        }}
        transformOrigin={{
          vertical: 'top',
          horizontal: 'left',
        }}
        sx={{
          '& .MuiPaper-root': {
            width: 200,
            bgcolor: 'background.paper',
            boxShadow: 3,
            borderRadius: 1,
            mt: 1,
          },
        }}
      >
        <MenuItem
          onClick={() => handleSiteLocationSelect(null)}
          selected={!activeSiteLocation}
          sx={{
            py: 1,
            '&:hover': {
              bgcolor: 'action.hover',
            },
          }}
        >
          <Typography variant="body2" color="text.primary">
            All Sites
          </Typography>
        </MenuItem>
        {festival.locations.map((siteLocation) => (
          <MenuItem
            key={siteLocation.id}
            onClick={() => handleSiteLocationSelect(siteLocation.id)}
            selected={activeSiteLocation?.id === siteLocation.id}
            sx={{
              py: 1,
              '&:hover': {
                bgcolor: 'action.hover',
              },
            }}
          >
            <Typography variant="body2" color="text.primary">
              {siteLocation.type === 'arena' ? 'Arena' : 'Campsite'}
            </Typography>
          </MenuItem>
        ))}
      </Menu>
    </Box>
  );
};