import React, { useState, useEffect } from 'react';
import { Box, Typography, Chip } from '@mui/material';
import { Festival } from '../../types/festival';

interface FestivalCounterProps {
  festival: Festival;
  isCollapsed?: boolean;
}

interface CounterState {
  type: 'before' | 'during' | 'after';
  days: number;
  currentDay?: number;
  totalDays?: number;
  isLoading: boolean;
  error?: string;
}

export const FestivalCounter: React.FC<FestivalCounterProps> = ({ 
  festival, 
  isCollapsed = false 
}) => {
  const [counterState, setCounterState] = useState<CounterState>({
    type: 'before',
    days: 0,
    isLoading: true,
  });

  const calculateCounterState = (): CounterState => {
    try {
      const now = new Date();
      const startDate = new Date(festival.startDate);
      const endDate = new Date(festival.endDate);

      // Validate dates
      if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return {
          type: 'before',
          days: 0,
          isLoading: false,
          error: 'Invalid festival dates',
        };
      }

      // Normalize dates to start of day for accurate day calculations
      const normalizeDate = (date: Date) => {
        const normalized = new Date(date);
        normalized.setHours(0, 0, 0, 0);
        return normalized;
      };

      const normalizedNow = normalizeDate(now);
      const normalizedStart = normalizeDate(startDate);
      const normalizedEnd = normalizeDate(endDate);

      // Calculate total festival duration
      const totalDurationMs = normalizedEnd.getTime() - normalizedStart.getTime();
      const totalDays = Math.max(1, Math.ceil(totalDurationMs / (1000 * 60 * 60 * 24)) + 1);

      if (normalizedNow < normalizedStart) {
        // Before festival starts
        const timeDiff = normalizedStart.getTime() - normalizedNow.getTime();
        const days = Math.ceil(timeDiff / (1000 * 60 * 60 * 24));
        return {
          type: 'before',
          days,
          isLoading: false,
        };
      } else if (normalizedNow <= normalizedEnd) {
        // During festival
        const timeDiff = normalizedNow.getTime() - normalizedStart.getTime();
        const currentDay = Math.floor(timeDiff / (1000 * 60 * 60 * 24)) + 1;
        return {
          type: 'during',
          days: 0,
          currentDay: Math.max(1, currentDay),
          totalDays,
          isLoading: false,
        };
      } else {
        // After festival ends
        const timeDiff = normalizedNow.getTime() - normalizedEnd.getTime();
        const days = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
        return {
          type: 'after',
          days,
          isLoading: false,
        };
      }
    } catch (error) {
      return {
        type: 'before',
        days: 0,
        isLoading: false,
        error: 'Error calculating festival timing',
      };
    }
  };

  useEffect(() => {
    // Initial calculation
    setCounterState(calculateCounterState());

    // Update every minute
    const interval = setInterval(() => {
      setCounterState(calculateCounterState());
    }, 60000);

    return () => clearInterval(interval);
  }, [festival.startDate, festival.endDate]);

  const getDisplayText = (): string => {
    if (counterState.isLoading) return 'Loading...';
    if (counterState.error) return 'Date error';

    switch (counterState.type) {
      case 'before':
        if (counterState.days === 1) return '1 day until festival';
        return `${counterState.days} days until festival`;
      case 'during':
        return `Day ${counterState.currentDay} of ${counterState.totalDays}`;
      case 'after':
        if (counterState.days === 0) return 'Festival ended today';
        if (counterState.days === 1) return '1 day since festival ended';
        return `${counterState.days} days since festival ended`;
      default:
        return '';
    }
  };

  const getChipColor = (): 'primary' | 'secondary' | 'default' => {
    switch (counterState.type) {
      case 'before':
        return 'primary';
      case 'during':
        return 'secondary';
      case 'after':
        return 'default';
      default:
        return 'default';
    }
  };

  const getChipVariant = (): 'filled' | 'outlined' => {
    return counterState.type === 'during' ? 'filled' : 'outlined';
  };

  if (isCollapsed) {
    // Collapsed view - show minimal indicator
    return (
      <Box
        sx={{
          display: 'flex',
          justifyContent: 'center',
          p: 0.5,
        }}
      >
        <Box
          sx={{
            width: 8,
            height: 8,
            borderRadius: '50%',
            bgcolor: counterState.type === 'during' ? 'secondary.main' : 
                    counterState.type === 'before' ? 'primary.main' : 'grey.400',
          }}
        />
      </Box>
    );
  }

  return (
    <Box
      sx={{
        p: 1.5,
        borderTop: 1,
        borderColor: 'rgba(0, 0, 0, 0.1)',
      }}
    >
      <Chip
        label={getDisplayText()}
        color={getChipColor()}
        variant={getChipVariant()}
        size="small"
        sx={{
          width: '100%',
          fontSize: '0.75rem',
          fontWeight: 500,
          '& .MuiChip-label': {
            px: 1,
          },
        }}
      />
      
      {counterState.error && (
        <Typography
          variant="caption"
          sx={{
            display: 'block',
            textAlign: 'center',
            mt: 0.5,
            color: 'error.main',
          }}
        >
          {counterState.error}
        </Typography>
      )}
    </Box>
  );
};