import React, { useState, useEffect } from 'react';
import { Festival, FestivalLocation } from '../types/festival';
import { databaseService } from '../services/database/index';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  IconButton,
  TextField,
  Select,
  MenuItem,
  FormControl,
  InputLabel,
  Button,
  Grid,
  Box,
  Typography,
  FormControlLabel,
  Checkbox,
  styled,
  FormGroup,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

const StyledFormControlLabel = styled(FormControlLabel)({
  margin: 0,
  flexDirection: 'column-reverse',
  '.MuiFormControlLabel-label': {
    marginLeft: 0,
    marginTop: 4,
    fontSize: '0.875rem',
  },
});

interface FestivalEditModalProps {
  festival: Festival;
  isOpen: boolean;
  onClose: () => void;
  onSave: () => void;
}

export const FestivalEditModal: React.FC<FestivalEditModalProps> = ({
  festival,
  isOpen,
  onClose,
  onSave,
}) => {
  const [formData, setFormData] = useState({
    name: festival.name,
    startDate: festival.startDate,
    endDate: festival.endDate,
    location: festival.location,
    type: festival.type,
    mainUrl: festival.mainUrl || '',
    mapUrl: festival.mapUrl || '',
    travelInfoUrl: festival.travelInfoUrl || '',
    faqsUrl: festival.faqsUrl || '',
    showAdmissions: festival.showAdmissions ?? true,
    showFrontOfHouse: festival.showFrontOfHouse ?? true,
    showLostProperty: festival.showLostProperty ?? true,
    showShifts: festival.showShifts ?? false,
    hasMultipleLocations: festival.hasMultipleLocations,
    locations: festival.locations || [],
  });

  const [hasArena, setHasArena] = useState(
    festival.locations?.some(loc => loc.type === 'arena') ?? true
  );
  const [hasCampsite, setHasCampsite] = useState(
    festival.locations?.some(loc => loc.type === 'campsite') ?? false
  );

  useEffect(() => {
    setFormData({
      name: festival.name,
      startDate: festival.startDate,
      endDate: festival.endDate,
      location: festival.location,
      type: festival.type,
      mainUrl: festival.mainUrl || '',
      mapUrl: festival.mapUrl || '',
      travelInfoUrl: festival.travelInfoUrl || '',
      faqsUrl: festival.faqsUrl || '',
      showAdmissions: festival.showAdmissions ?? true,
      showFrontOfHouse: festival.showFrontOfHouse ?? true,
      showLostProperty: festival.showLostProperty ?? true,
      showShifts: festival.showShifts ?? false,
      hasMultipleLocations: festival.hasMultipleLocations,
      locations: festival.locations || [],
    });
    setHasArena(festival.locations?.some(loc => loc.type === 'arena') ?? true);
    setHasCampsite(festival.locations?.some(loc => loc.type === 'campsite') ?? false);
  }, [festival]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    try {
      const locations: FestivalLocation[] = [];
      
      if (hasArena) {
        const existingArena = festival.locations?.find(loc => loc.type === 'arena');
        locations.push(existingArena || {
          id: `arena_${Date.now()}`,
          name: 'Arena',
          type: 'arena',
          description: 'Main arena area'
        });
      }
      
      if (hasCampsite) {
        const existingCampsite = festival.locations?.find(loc => loc.type === 'campsite');
        locations.push(existingCampsite || {
          id: `campsite_${Date.now()}`,
          name: 'Campsite',
          type: 'campsite',
          description: 'Festival campsite'
        });
      }

      await databaseService.updateFestival({
        ...festival,
        ...formData,
        hasMultipleLocations: locations.length > 0,
        locations,
      });
      onSave();
      onClose();
    } catch (error) {
      console.error('Error updating festival:', error);
    }
  };

  return (
    <Dialog
      open={isOpen}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      sx={{
        '& .MuiDialog-paper': {
          m: 2,
          maxHeight: 'calc(100% - 32px)',
        },
      }}
    >
      <DialogTitle sx={{ m: 0, p: 2 }}>
        <Typography variant="h6">Edit Festival</Typography>
        <IconButton
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
            color: 'text.secondary',
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <form onSubmit={handleSubmit}>
        <DialogContent dividers>
          <Grid container spacing={2}>
            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                required
                label="Name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                required
                type="date"
                label="Start Date"
                value={formData.startDate}
                onChange={(e) => setFormData({ ...formData, startDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid size={{ xs: 12, sm: 6 }}>
              <TextField
                fullWidth
                required
                type="date"
                label="End Date"
                value={formData.endDate}
                onChange={(e) => setFormData({ ...formData, endDate: e.target.value })}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                required
                label="Location"
                value={formData.location}
                onChange={(e) => setFormData({ ...formData, location: e.target.value })}
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <FormControl fullWidth>
                <InputLabel>Type</InputLabel>
                <Select
                  value={formData.type}
                  label="Type"
                  onChange={(e) => setFormData({ ...formData, type: e.target.value as 'festival' | 'regular_event' })}
                >
                  <MenuItem value="festival">Festival</MenuItem>
                  <MenuItem value="regular_event">Regular Event</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid size={{ xs: 12 }}>
              <Typography variant="subtitle1" gutterBottom>
                Festival Sites
              </Typography>
              <FormGroup>
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={hasArena}
                      onChange={(e) => setHasArena(e.target.checked)}
                    />
                  }
                  label="Arena"
                />
                <FormControlLabel
                  control={
                    <Checkbox
                      checked={hasCampsite}
                      onChange={(e) => setHasCampsite(e.target.checked)}
                    />
                  }
                  label="Campsite"
                />
              </FormGroup>
            </Grid>

            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                type="url"
                label="Main Website URL"
                value={formData.mainUrl}
                onChange={(e) => setFormData({ ...formData, mainUrl: e.target.value })}
                placeholder="https://example.com"
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                type="url"
                label="Map URL"
                value={formData.mapUrl}
                onChange={(e) => setFormData({ ...formData, mapUrl: e.target.value })}
                placeholder="https://example.com/map"
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                type="url"
                label="Travel Information URL"
                value={formData.travelInfoUrl}
                onChange={(e) => setFormData({ ...formData, travelInfoUrl: e.target.value })}
                placeholder="https://example.com/travel"
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                type="url"
                label="FAQs URL"
                value={formData.faqsUrl}
                onChange={(e) => setFormData({ ...formData, faqsUrl: e.target.value })}
                placeholder="https://example.com/faqs"
              />
            </Grid>

            <Grid size={{ xs: 12 }}>
              <Box display="flex" justifyContent="flex-end" gap={2}>
                <StyledFormControlLabel
                  control={
                    <Checkbox
                      checked={formData.showAdmissions}
                      onChange={(e) => setFormData({ ...formData, showAdmissions: e.target.checked })}
                    />
                  }
                  label="Admissions"
                />
                <StyledFormControlLabel
                  control={
                    <Checkbox
                      checked={formData.showFrontOfHouse}
                      onChange={(e) => setFormData({ ...formData, showFrontOfHouse: e.target.checked })}
                    />
                  }
                  label="Front of House"
                />
                <StyledFormControlLabel
                  control={
                    <Checkbox
                      checked={formData.showLostProperty}
                      onChange={(e) => setFormData({ ...formData, showLostProperty: e.target.checked })}
                    />
                  }
                  label="Lost Property"
                />
                <StyledFormControlLabel
                  control={
                    <Checkbox
                      checked={formData.showShifts}
                      onChange={(e) => setFormData({ ...formData, showShifts: e.target.checked })}
                    />
                  }
                  label="Shifts"
                />
              </Box>
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={onClose} variant="outlined" color="inherit">
            Cancel
          </Button>
          <Button type="submit" variant="contained" sx={{ bgcolor: 'ithink.pink' }}>
            Save Changes
          </Button>
        </DialogActions>
      </form>
    </Dialog>
  );
};
