import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Chip,
  Alert,
  CircularProgress
} from '@mui/material';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import DeleteIcon from '@mui/icons-material/Delete';
import { format } from 'date-fns';
import { databaseService } from '../../services/database';
import { Feedback } from '../../types';
import { FeedbackDetailsModal } from './FeedbackDetailsModal';

export const FeedbackAdminSection: React.FC = () => {
  const [feedback, setFeedback] = useState<Feedback[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedFeedback, setSelectedFeedback] = useState<Feedback | null>(null);

  const loadFeedback = async () => {
    try {
      setError(null);
      setLoading(true);

      // Get all feedback directly from database service
      const allFeedback = await databaseService.getAllFeedback();
      console.log('Loaded feedback:', allFeedback);

      // Check if feedback array is valid
      if (!Array.isArray(allFeedback)) {
        throw new Error('Invalid feedback data received');
      }

      setFeedback(allFeedback);
    } catch (error) {
      console.error('Error loading feedback:', error);
      setError(error instanceof Error ? error.message : 'Failed to load feedback. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    loadFeedback();
  }, []);

  const handleMarkResolved = async (id: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent row click
    try {
      setError(null);
      await databaseService.updateFeedbackStatus(id, true);
      await loadFeedback();
    } catch (error) {
      console.error('Error marking feedback as resolved:', error);
      setError('Failed to update feedback status. Please try again.');
    }
  };

  const handleDelete = async (id: string, event: React.MouseEvent) => {
    event.stopPropagation(); // Prevent row click
    if (window.confirm('Are you sure you want to delete this feedback?')) {
      try {
        setError(null);
        await databaseService.deleteFeedback(id);
        await loadFeedback();
      } catch (error) {
        console.error('Error deleting feedback:', error);
        setError('Failed to delete feedback. Please try again.');
      }
    }
  };

  const handleRowClick = (item: Feedback) => {
    setSelectedFeedback(item);
  };

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight={200}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        User Feedback
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell>Date</TableCell>
              <TableCell>Name</TableCell>
              <TableCell>Page</TableCell>
              <TableCell>Feedback</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {feedback.map((item) => (
              <TableRow 
                key={item._id}
                onClick={() => handleRowClick(item)}
                sx={{ 
                  cursor: 'pointer',
                  '&:hover': {
                    bgcolor: 'action.hover'
                  }
                }}
              >
                <TableCell>
                  {format(new Date(item.timestamp || 0), 'dd/MM/yyyy HH:mm')}
                </TableCell>
                <TableCell>{item.name}</TableCell>
                <TableCell>{item.page}</TableCell>
                <TableCell>
                  {item.feedback.length > 100
                    ? `${item.feedback.substring(0, 100)}...`
                    : item.feedback}
                </TableCell>
                <TableCell>
                  <Chip
                    label={item.resolved ? 'Resolved' : 'Open'}
                    color={item.resolved ? 'success' : 'default'}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  {!item.resolved && (
                    <Tooltip title="Mark as resolved">
                      <IconButton
                        onClick={(e) => handleMarkResolved(item._id, e)}
                        size="small"
                        color="primary"
                      >
                        <CheckCircleIcon />
                      </IconButton>
                    </Tooltip>
                  )}
                  <Tooltip title="Delete">
                    <IconButton
                      onClick={(e) => handleDelete(item._id, e)}
                      size="small"
                      color="error"
                    >
                      <DeleteIcon />
                    </IconButton>
                  </Tooltip>
                </TableCell>
              </TableRow>
            ))}
            {feedback.length === 0 && (
              <TableRow>
                <TableCell colSpan={6} align="center">
                  No feedback available
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </TableContainer>

      <FeedbackDetailsModal
        feedback={selectedFeedback}
        open={selectedFeedback !== null}
        onClose={() => setSelectedFeedback(null)}
      />
    </Box>
  );
};