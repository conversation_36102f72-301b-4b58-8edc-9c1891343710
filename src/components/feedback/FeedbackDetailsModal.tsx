import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Typography,
  Box,
  Chip
} from '@mui/material';
import { format } from 'date-fns';
import { Feedback } from '../../types';

interface FeedbackDetailsModalProps {
  feedback: Feedback | null;
  open: boolean;
  onClose: () => void;
}

export const FeedbackDetailsModal: React.FC<FeedbackDetailsModalProps> = ({
  feedback,
  open,
  onClose
}) => {
  if (!feedback) return null;

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>Feedback Details</DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Typography variant="overline" color="text.secondary">
            Submitted by
          </Typography>
          <Typography variant="body1" gutterBottom>
            {feedback.name}
          </Typography>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="overline" color="text.secondary">
            Page
          </Typography>
          <Typography variant="body1" gutterBottom>
            {feedback.page}
          </Typography>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="overline" color="text.secondary">
            Status
          </Typography>
          <Box>
            <Chip
              label={feedback.resolved ? 'Resolved' : 'Open'}
              color={feedback.resolved ? 'success' : 'default'}
              size="small"
            />
          </Box>
        </Box>

        <Box sx={{ mb: 3 }}>
          <Typography variant="overline" color="text.secondary">
            Timestamp
          </Typography>
          <Typography variant="body1" gutterBottom>
            {format(new Date(feedback.timestamp || 0), 'dd/MM/yyyy HH:mm')}
          </Typography>
        </Box>

        <Box>
          <Typography variant="overline" color="text.secondary">
            Feedback
          </Typography>
          <Typography
            variant="body1"
            component="div"
            sx={{
              whiteSpace: 'pre-wrap',
              bgcolor: 'grey.100',
              p: 2,
              borderRadius: 1,
              mt: 1
            }}
          >
            {feedback.feedback}
          </Typography>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Close</Button>
      </DialogActions>
    </Dialog>
  );
};