import React, { useState } from 'react';
import { Fab } from '@mui/material';
import FeedbackIcon from '@mui/icons-material/Feedback';
import { FeedbackModal } from './FeedbackModal';

export const FeedbackButton: React.FC = () => {
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpen = () => setIsModalOpen(true);
  const handleClose = () => setIsModalOpen(false);

  return (
    <>
      <Fab
        color="primary"
        aria-label="feedback"
        onClick={handleOpen}
        sx={{
          position: 'fixed',
          bottom: 16,
          right: 16,
          zIndex: 1000
        }}
      >
        <FeedbackIcon />
      </Fab>
      <FeedbackModal
        open={isModalOpen}
        onClose={handleClose}
      />
    </>
  );
};