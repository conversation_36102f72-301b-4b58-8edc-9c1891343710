import React, { useState } from 'react';
import { useFestival } from '../contexts/FestivalContext';
import {
  Box,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  List,
  ListItem,
  ListItemButton,
  ListItemText,
  IconButton,
  Tooltip,
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';

interface ActiveFestivalSelectorProps {
  isCollapsed?: boolean;
}

export const ActiveFestivalSelector: React.FC<ActiveFestivalSelectorProps> = ({ isCollapsed = false }) => {
  const { festivals, activeFestival, setActiveFestival, loading } = useFestival();
  const [isModalOpen, setIsModalOpen] = useState(false);

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
  };

  const handleSelectFestival = async (festival: any) => {
    await setActiveFestival(festival);
    handleCloseModal();
  };

  return (
    <>
      <Tooltip title={isCollapsed ? "Select Festival" : ""} placement="right">
        <Box
          onClick={handleOpenModal}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: isCollapsed ? 'center' : 'flex-start',
            p: isCollapsed ? 1 : 1.5,
            cursor: 'pointer',
            borderRadius: 1,
            '&:hover': {
              bgcolor: 'rgba(0, 0, 0, 0.05)',
            },
          }}
        >
          {isCollapsed ? (
            <Typography
              variant="subtitle2"
              sx={{
                fontWeight: 600,
                color: 'black',
                transform: 'rotate(-90deg)',
                whiteSpace: 'nowrap',
              }}
            >
              Active Festival
            </Typography>
          ) : (
            <Box>
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'black' }}>
                Active Festival:
              </Typography>
              <Typography variant="body2" color="black">
                {activeFestival ? activeFestival.name : 'None selected'}
              </Typography>
            </Box>
          )}
        </Box>
      </Tooltip>

      <Dialog
        open={isModalOpen}
        onClose={handleCloseModal}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          Select Festival
          <IconButton edge="end" color="inherit" onClick={handleCloseModal} aria-label="close">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {loading ? (
            <Typography>Loading festivals...</Typography>
          ) : festivals.length === 0 ? (
            <Typography>No festivals available.</Typography>
          ) : (
            <List>
              {festivals.map((festival) => (
                <ListItem key={festival._id} disablePadding>
                  <ListItemButton
                    onClick={() => handleSelectFestival(festival)}
                    selected={activeFestival?._id === festival._id}
                  >
                    <ListItemText
                      primary={festival.name}
                      secondary={
                        festival.startDate && festival.endDate
                          ? `${new Date(festival.startDate).toLocaleDateString()} - ${new Date(
                              festival.endDate
                            ).toLocaleDateString()}`
                          : undefined
                      }
                    />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};