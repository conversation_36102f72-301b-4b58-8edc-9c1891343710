import React, { useState, useCallback } from 'react';
import { Grid, Paper, SelectChangeEvent, Button, Box } from '@mui/material';
import { LocationSection } from './admission/LocationSection';
import { PersonalInfoSection } from './admission/PersonalInfoSection';
import { PhysicalDescriptionSection } from './admission/PhysicalDescriptionSection';
import { SubstanceUseSection } from './admission/SubstanceUseSection';
import { SafeguardingSection } from './admission/SafeguardingSection';
import { ReferralSection } from './admission/ReferralSection';
import { AdmissionNotesSection } from './admission/AdmissionNotesSection';
import { useAdmissionForm } from '../hooks/useAdmissionForm';
import { useFestival } from '../contexts/FestivalContext';
import { useSiteLocation } from '../contexts/SiteLocationContext';
import { BaysOrChairs, SafeguardingCategory, NewWelfareAdmission } from '../types/admission';
import { AdmissionFormData, createInitialFormData } from '../types/forms';
import { Festival } from '../types/festival';
import { NoteEntry } from '../types/base';

interface AdmissionFormProps {
  onSubmit: (formData: NewWelfareAdmission) => Promise<void>;
  activeFestival: Festival | null;
  initialData?: AdmissionFormData;
}

export const AdmissionForm: React.FC<AdmissionFormProps> = ({ onSubmit, activeFestival, initialData }) => {
  const { activeSiteLocation } = useSiteLocation();
  const [newNote, setNewNote] = useState('');
  
  // Simplified form hook usage
  const {
    formData,
    setFormData,
    bayStatus,
    handleSubmit: handleFormSubmit,
    isSubmitting,
    handleInputChange,
    handleSelectChange,
  } = useAdmissionForm({
    initialData: initialData || createInitialFormData(),
    onSubmit: async (data) => {
      // Simplified submission logic
      const submissionData: NewWelfareAdmission = {
        ...data,
        type: 'admission',
        documentType: 'admission',
        syncStatus: 'sync_pending',
        festivalId: activeFestival?._id || '',
        timestamp: new Date().toISOString(),
        createdAt: data.createdAt || new Date().toISOString()
      };
      
      // Add new note if present
      if (newNote.trim()) {
        const timestamp = new Date().toISOString();
        const newNoteEntry: NoteEntry = {
          timestamp,
          note: newNote.trim(),
          author: 'Staff'
        };
        
        submissionData.AdditionalNotes = [...(submissionData.AdditionalNotes || []), newNoteEntry];
        setNewNote(''); // Clear the note input
      }
      
      await onSubmit(submissionData);
    }
  });

  // Custom handler just for the new note field
  const handleNoteChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setNewNote(e.target.value);
  };

  // Simple direct handlers for form updates, wrapped in useCallback
  const handleLocationChange = useCallback((value: number | undefined) => {
    setFormData(prev => ({ ...prev, Location: value }));
  }, [setFormData]);

  const handleBaysOrChairsChange = useCallback((value: BaysOrChairs) => {
    setFormData(prev => ({ ...prev, BaysOrChairs: value }));
  }, [setFormData]);

  const handleInBayNowChange = useCallback((value: boolean) => {
    setFormData(prev => ({ ...prev, InBayNow: value }));
  }, [setFormData]);

  const handleAgeChange = useCallback((age: number | undefined) => {
    setFormData(prev => ({ ...prev, Age: age }));
  }, [setFormData]);

  // Validate Safeguarding category before passing down
  const validSafeguardingCategories: SafeguardingCategory[] = [
    'Sexual Assault',
    'Physical Assault',
    'Emotional Abuse',
    'Other'
  ];
  const validatedSafeguardingCategory =
    validSafeguardingCategories.includes(formData.Safeguarding as SafeguardingCategory)
    ? formData.Safeguarding as SafeguardingCategory
    : undefined; // Use undefined for invalid values

  return (
    <form onSubmit={(e) => { e.preventDefault(); handleFormSubmit(); }}>
      <Paper sx={{ p: 3 }}>
        <Grid container spacing={3}>
          <Grid size={{ xs: 12 }}>
            <LocationSection
              location={formData.Location}
              baysOrChairs={formData.BaysOrChairs}
              inBayNow={formData.InBayNow}
              bayStatus={bayStatus}
              onLocationChange={handleLocationChange}
              onBaysOrChairsChange={handleBaysOrChairsChange}
              onInBayNowChange={handleInBayNowChange}
            />
          </Grid>
          <Grid size={{ xs: 12 }}>
            <AdmissionNotesSection
              isEdit={!!formData._id} // Set to true if editing an existing admission
              admissionNotes={formData.AdmissionNotes}
              additionalNotes={formData.AdditionalNotes}
              onInputChange={handleInputChange} // Use regular input handler for AdmissionNotes field
              onNewNoteChange={handleNoteChange} // Use custom handler for new note field
              newNoteValue={newNote} // Pass the newNote state value
            />
          </Grid>
          <Grid size={{ xs: 12 }}>
            <PersonalInfoSection
              firstName={formData.FirstName}
              surname={formData.Surname}
              dob={formData.DOB}
              age={formData.Age}
              gender={formData.Gender}
              pronoun={formData.Pronoun}
              ethnicity={formData.Ethnicity}
              contactName={formData.ContactName}
              contactNumber={formData.ContactNumber}
              onInputChange={handleInputChange}
              onSelectChange={handleSelectChange}
              onAgeChange={handleAgeChange}
            />
          </Grid>
          <Grid size={{ xs: 12 }}>
            <SubstanceUseSection
              substancesUsed={formData.SubstanceUsed}
              onSelectChange={handleSelectChange}
            />
          </Grid>
          <Grid size={{ xs: 12 }}>
            <SafeguardingSection
              category={validatedSafeguardingCategory}
              notes={formData.SafeguardingNotes || ''}
              reasonCategory={formData.ReasonCategory}
              onSelectChange={handleSelectChange}
              onInputChange={handleInputChange}
            />
          </Grid>
          <Grid size={{ xs: 12 }}>
            <ReferralSection
              referredBy={formData.ReferredBy}
              reasonCategory={formData.ReasonCategory}
              onSelectChange={handleSelectChange}
            />
          </Grid>
          <Grid size={{ xs: 12 }}>
            <PhysicalDescriptionSection
              hairColour={formData.HairColour}
              hairStyle={formData.HairStyle}
              clothingTop={formData.ClothingTop}
              clothingBottom={formData.ClothingBottom}
              footwear={formData.Footwear}
              otherFeatures={formData.OtherFeatures}
              onInputChange={handleInputChange}
            />
          </Grid>
          <Grid size={{ xs: 12 }}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                variant="contained"
                color="primary"
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : 'Save Admission'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </form>
  );
};