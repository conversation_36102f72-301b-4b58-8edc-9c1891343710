import React, { useState } from 'react';
import { NavLink } from 'react-router-dom';
import { useFestival } from '../contexts/FestivalContext';
import { useMediaQuery } from 'react-responsive';
import { ShiftNotes } from './ShiftNotes';
import { getVersion } from '../utils/version';
import { FestivalLinks } from './festival/FestivalLinks';
import { FestivalCounter } from './festival/FestivalCounter';
import { useSiteLocation } from '../contexts/SiteLocationContext';
import { useAuth } from '../contexts/AuthContext';
import { ActiveFestivalSelector } from './ActiveFestivalSelector';
import { CacheStatus } from './shared/CacheStatus';
import { useSyncStatus } from '../hooks/useSyncStatus';
import { databaseService } from '../services/database/index';
import {
  HomeIcon,
  UserGroupIcon,
  UserIcon,
  BuildingOfficeIcon,
  ArchiveBoxIcon,
  DocumentChartBarIcon,
  Cog6ToothIcon,
  CalendarDaysIcon,
  ChatBubbleLeftIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  BookOpenIcon,
  EyeIcon,
} from '@heroicons/react/24/outline';
// Import MUI components individually for better tree-shaking
import Box from '@mui/material/Box';
import List from '@mui/material/List';
import ListItem from '@mui/material/ListItem';
import ListItemButton from '@mui/material/ListItemButton';
import ListItemText from '@mui/material/ListItemText';
import Typography from '@mui/material/Typography';
import Button from '@mui/material/Button';
import IconButton from '@mui/material/IconButton';
import Tooltip from '@mui/material/Tooltip';
import Divider from '@mui/material/Divider';
import Menu from '@mui/material/Menu';
import MenuItem from '@mui/material/MenuItem';
import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import CloseIcon from '@mui/icons-material/Close';
import SyncIcon from '@mui/icons-material/Sync';
import CheckCircleIcon from '@mui/icons-material/CheckCircle';
import ErrorIcon from '@mui/icons-material/Error';
import WarningIcon from '@mui/icons-material/Warning';
import CircularProgress from '@mui/material/CircularProgress';

export interface SidebarProps {
  onSignOut: () => Promise<boolean>;
  children?: React.ReactNode;
}

export const Sidebar: React.FC<SidebarProps> = ({ onSignOut, children }) => {
  const { activeFestival, festivals, setActiveFestival, loading } = useFestival();
  const { activeSiteLocation, setActiveSiteLocation } = useSiteLocation();
  const { userEmail, hasAccess } = useAuth();
  const isDesktop = useMediaQuery({ query: '(min-width: 1024px)' });
  const [isNotesOpen, setIsNotesOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const [isSyncing, setIsSyncing] = useState(false);

  const [isFestivalModalOpen, setIsFestivalModalOpen] = useState(false);
  
  // Get sync status
  const syncStatus = useSyncStatus();

  const handleFestivalClick = (event: React.MouseEvent<HTMLDivElement>) => {
    // Only open festival selection modal if no festival is active
    if (!activeFestival) {
      setIsFestivalModalOpen(true);
    } else if (activeFestival.hasMultipleLocations) {
      setAnchorEl(event.currentTarget);
    }
  };

  const handleFestivalModalClose = () => {
    setIsFestivalModalOpen(false);
  };

  const handleSelectFestival = async (festival: any) => {
    await setActiveFestival(festival);
    handleFestivalModalClose();
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleSiteLocationSelect = (siteLocationId: string | null) => {
    if (!activeFestival) return;
    
    if (!siteLocationId) {
      setActiveSiteLocation(null);
    } else {
      const siteLocation = activeFestival.locations.find(loc => loc.id === siteLocationId);
      if (siteLocation) {
        setActiveSiteLocation(siteLocation);
      }
    }
    handleClose();
  };

  const getSiteLocationLabel = () => {
    if (!activeSiteLocation) return '';
    return ` (${activeSiteLocation.type === 'arena' ? 'Arena' : 'Campsite'})`;
  };

  const navLinkSx = (isActive: boolean) => ({
    display: 'flex',
    alignItems: 'center',
    p: 0.75,
    borderRadius: 1,
    textDecoration: 'none !important',
    color: 'black',
    '&:hover': {
      bgcolor: 'rgba(0, 0, 0, 0.05)',
    },
    ...(isActive && {
      background: 'rgba(0, 0, 0, 0.1)',
    }),
  });

  const iconSx = {
    width: 18,
    height: 18,
    mr: isCollapsed ? 0 : 1.5,
    color: 'black',
  };

  const renderNavLink = (to: string, Icon: React.ComponentType, text: string, end?: boolean) => (
    <ListItem disablePadding>
      <NavLink to={to} style={{ width: '100%', textDecoration: 'none' }} end={end}>
        {({ isActive }) => (
          <Tooltip title={isCollapsed ? text : ''} placement="right">
            <Box sx={navLinkSx(isActive)}>
              <Box component={Icon} sx={iconSx} />
              {!isCollapsed && <Typography color="black">{text}</Typography>}
            </Box>
          </Tooltip>
        )}
      </NavLink>
    </ListItem>
  );

  const handleSyncClick = async () => {
    if (isSyncing) return;
    
    try {
      setIsSyncing(true);
      await databaseService.manualSync();
    } catch (error) {
      console.error('Manual sync failed:', error);
    } finally {
      setIsSyncing(false);
    }
  };

  const getSyncIcon = () => {
    if (isSyncing || syncStatus.status === 'syncing' || syncStatus.status === 'initial_sync') {
      return <CircularProgress size={18} sx={{ color: '#1976d2' }} />;
    }
    
    switch (syncStatus.status) {
      case 'synced':
        return <CheckCircleIcon sx={{ width: 18, height: 18, color: '#2e7d32' }} />;
      case 'error':
        return <ErrorIcon sx={{ width: 18, height: 18, color: '#d32f2f' }} />;
      case 'auth_error':
        return <WarningIcon sx={{ width: 18, height: 18, color: '#ed6c02' }} />;
      default:
        return <SyncIcon sx={{ width: 18, height: 18, color: '#1976d2' }} />;
    }
  };

  const getSyncText = () => {
    if (isSyncing) return 'Syncing...';
    
    switch (syncStatus.status) {
      case 'synced':
        return syncStatus.pendingChanges > 0 ? `Synced (${syncStatus.pendingChanges} pending)` : 'Synced';
      case 'syncing':
        return 'Syncing...';
      case 'initial_sync':
        return 'Initial Sync...';
      case 'error':
        return 'Sync Failed';
      case 'auth_error':
        return 'Auth Error';
      case 'paused':
        return 'Sync Paused';
      case 'disconnected':
        return 'Disconnected';
      default:
        return 'Sync';
    }
  };

  const getSyncColor = () => {
    if (isSyncing || syncStatus.status === 'syncing' || syncStatus.status === 'initial_sync') return '#1976d2';
    
    switch (syncStatus.status) {
      case 'synced':
        return '#2e7d32';
      case 'error':
        return '#d32f2f';
      case 'auth_error':
        return '#ed6c02';
      default:
        return '#1976d2';
    }
  };

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        height: '100%',
        background: 'linear-gradient(to right, #662D91, #f5f5f5)',
        color: 'black',
        width: isCollapsed ? 56 : 240,
        transition: 'width 0.2s ease-in-out',
        position: 'relative',
      }}
    >
      {/* Logo and Brand Name */}
      <Box sx={{ display: 'flex', alignItems: 'center', p: 1.5 }}>
        {!isCollapsed && (
          <Box
            component="img"
            src="/ithink-logo.svg"
            alt="iThink Logo"
            sx={{ height: 42, mr: 1 }}
          />
        )}
      </Box>

      {/* Collapse Button */}
      <IconButton
        onClick={() => setIsCollapsed(!isCollapsed)}
        sx={{
          position: 'absolute',
          right: -20,
          top: 20,
          bgcolor: '#662D91',
          color: 'white',
          width: 40,
          height: 40,
          '&:hover': {
            bgcolor: 'rgba(102, 45, 145, 0.9)',
          },
          border: '1px solid rgba(255, 255, 255, 0.2)',
          borderRadius: '50%',
          zIndex: 1,
        }}
      >
        {isCollapsed ? <ChevronRightIcon /> : <ChevronLeftIcon />}
      </IconButton>

      {/* Active Festival Selector - Always visible */}
      <Box
        sx={{
          borderTop: 1,
          borderBottom: 1,
          borderColor: 'rgba(0, 0, 0, 0.1)',
        }}
      >
        {/* Active Festival Header with Festival Links */}
        {!isCollapsed ? (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between',
              p: 1.5,
              cursor: 'pointer',
              borderRadius: 1,
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.05)',
              },
            }}
            onClick={handleFestivalClick}
          >
            <Box
              sx={{
                flex: 1,
                cursor: 'pointer',
                '&:hover': {
                  opacity: 0.8,
                }
              }}
              onClick={(e) => {
                e.stopPropagation();
                setIsFestivalModalOpen(true);
              }}
            >
              <Typography variant="subtitle2" sx={{ fontWeight: 600, color: 'black' }}>
                Active Festival:
              </Typography>
              <Typography variant="body2" color="black">
                {activeFestival ? activeFestival.name : 'None selected'}
              </Typography>
            </Box>
            {activeFestival && (
              <Box sx={{ ml: 2 }}>
                <FestivalLinks festival={activeFestival} />
              </Box>
            )}
          </Box>
        ) : (
          <ActiveFestivalSelector isCollapsed={isCollapsed} />
        )}
        
        {/* Festival Counter - Always visible when festival is active */}
        {activeFestival && (
          <FestivalCounter festival={activeFestival} isCollapsed={isCollapsed} />
        )}
        
        {/* Site Location - Only visible when not collapsed and festival has multiple locations */}
        {activeFestival && !isCollapsed && activeFestival.hasMultipleLocations && (
          <Box
            sx={{
              px: 1.5,
              pb: 1.5,
            }}
          >
            <Typography variant="body2" color="black">
              Site: {activeSiteLocation ? (activeSiteLocation.type === 'arena' ? 'Arena' : 'Campsite') : 'All Sites'}
            </Typography>
          </Box>
        )}
      </Box>

      {/* Site Location Menu */}
      {activeFestival?.hasMultipleLocations && (
        <Menu
          anchorEl={anchorEl}
          open={Boolean(anchorEl)}
          onClose={handleClose}
          anchorOrigin={{
            vertical: 'bottom',
            horizontal: 'left',
          }}
          transformOrigin={{
            vertical: 'top',
            horizontal: 'left',
          }}
        >
          <MenuItem
            onClick={() => handleSiteLocationSelect(null)}
            selected={!activeSiteLocation}
          >
            <Typography variant="body2">All Sites</Typography>
          </MenuItem>
          {activeFestival.locations.map((siteLocation) => (
            <MenuItem
              key={siteLocation.id}
              onClick={() => handleSiteLocationSelect(siteLocation.id)}
              selected={activeSiteLocation?.id === siteLocation.id}
            >
              <Typography variant="body2">
                {siteLocation.type === 'arena' ? 'Arena' : 'Campsite'}
              </Typography>
            </MenuItem>
          ))}
        </Menu>
      )}

      {/* Navigation Links */}
      <List sx={{ flexGrow: 1, px: 1.5 }}>
        {renderNavLink('/', HomeIcon, 'Dashboard', true)}
        {activeFestival?.showAdmissions !== false && hasAccess('admissions') && (
          <>
            {renderNavLink('/admissions', UserGroupIcon, 'Admissions')}
            {hasAccess('new-admission') && renderNavLink('/new-admission', UserIcon, 'New Admission')}
          </>
        )}
        {activeFestival?.showFrontOfHouse !== false && hasAccess('front-of-house') && renderNavLink('/front-of-house', BuildingOfficeIcon, 'Front of House')}
        {activeFestival?.showLostProperty !== false && hasAccess('lost-property') && renderNavLink('/lost-property', ArchiveBoxIcon, 'Lost Property')}
        {hasAccess('sensory-hub') && renderNavLink('/sensory-hub', EyeIcon, 'Sensory Hub')}
        
        {/* Knowledge Base - Everyone with access to the app can view */}
        {hasAccess('knowledge-base-view') && renderNavLink('/knowledge-base', BookOpenIcon, 'Knowledge Base')}
        
        {/* Reports - Restricted access */}
        {hasAccess('reports') && renderNavLink('/reports', DocumentChartBarIcon, 'Reports')}
        
        {/* Shifts - Restricted access */}
        {activeFestival && activeFestival.showShifts !== false && hasAccess('shifts') &&
          renderNavLink(`/shifts/${activeFestival._id}`, CalendarDaysIcon, 'Shifts')}
        
        {/* Admin sections - Restricted access */}
        {hasAccess('festival-management') &&
          renderNavLink('/admin/festivals', Cog6ToothIcon, 'Festival Management')}
        {hasAccess('feedback-management') &&
          renderNavLink('/admin/feedback', ChatBubbleLeftIcon, 'Feedback Management')}
        
        {hasAccess('access-management') &&
          renderNavLink('/admin/access', Cog6ToothIcon, 'Access Management')}
        
        {/* Display user email if available */}
        {!isCollapsed && userEmail && (
          <Box sx={{ mt: 2, p: 1, bgcolor: 'rgba(0, 0, 0, 0.05)', borderRadius: 1 }}>
            <Typography variant="caption" sx={{ display: 'block', color: 'black' }}>
              Logged in as:
            </Typography>
            <Typography variant="caption" sx={{ display: 'block', color: 'black', wordBreak: 'break-all' }}>
              {userEmail}
            </Typography>
          </Box>
        )}
      </List>

      {children}

      {/* Sync Status, Notes and Version */}
      <Box sx={{ p: 1.5, borderTop: 1, borderColor: 'rgba(0, 0, 0, 0.1)' }}>
        {/* Sync Status Indicator */}
        <Tooltip title={isCollapsed ? getSyncText() : ''} placement="right">
          <Button
            fullWidth
            onClick={handleSyncClick}
            disabled={isSyncing}
            sx={{
              ...navLinkSx(false),
              color: getSyncColor(),
              minWidth: 'unset',
              mb: 1,
            }}
          >
            <Box sx={{ width: 18, height: 18, mr: isCollapsed ? 0 : 1.5 }}>
              {getSyncIcon()}
            </Box>
            {!isCollapsed && <Typography sx={{ color: getSyncColor() }}>{getSyncText()}</Typography>}
          </Button>
        </Tooltip>

        {/* Notes Button */}
        <Tooltip title={isCollapsed ? 'Notes' : ''} placement="right">
          <Button
            fullWidth
            onClick={() => setIsNotesOpen(true)}
            sx={{
              color: 'black',
              display: 'flex',
              alignItems: 'center',
              p: 0.75,
              borderRadius: 1,
              minWidth: 'unset',
              '&:hover': {
                bgcolor: 'rgba(0, 0, 0, 0.05)',
              },
            }}
          >
            <Box
              component="svg"
              sx={{
                width: 18,
                height: 18,
                mr: isCollapsed ? 0 : 1.5,
              }}
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
            </Box>
            {!isCollapsed && <Typography color="black">Notes</Typography>}
          </Button>
        </Tooltip>
        {!isCollapsed && (
          <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
            <NavLink
              to="/user-guide"
              style={{ textDecoration: 'none' }}
            >
              <Typography
                variant="caption"
                sx={{
                  display: 'block',
                  textAlign: 'center',
                  mt: 1.5,
                  color: 'black',
                  '&:hover': {
                    textDecoration: 'underline'
                  }
                }}
              >
                User Guide
              </Typography>
            </NavLink>
            <NavLink
              to="/changelog"
              style={{ textDecoration: 'none' }}
            >
              <Typography
                variant="caption"
                sx={{
                  display: 'block',
                  textAlign: 'center',
                  mt: 0.5,
                  color: 'black',
                  '&:hover': {
                    textDecoration: 'underline'
                  }
                }}
              >
                Version {getVersion()}
              </Typography>
            </NavLink>
          </Box>
        )}
      </Box>
      {isNotesOpen && <ShiftNotes onClose={() => setIsNotesOpen(false)} />}

      {/* Festival Selection Modal */}
      <Dialog
        open={isFestivalModalOpen}
        onClose={handleFestivalModalClose}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          Select Festival
          <IconButton edge="end" color="inherit" onClick={handleFestivalModalClose} aria-label="close">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent>
          {loading ? (
            <Typography>Loading festivals...</Typography>
          ) : festivals.length === 0 ? (
            <Typography>No festivals available.</Typography>
          ) : (
            <List>
              {festivals.map((festival) => (
                <ListItem key={festival._id} disablePadding>
                  <ListItemButton
                    onClick={() => handleSelectFestival(festival)}
                    selected={activeFestival?._id === festival._id}
                  >
                    <ListItemText
                      primary={festival.name}
                      secondary={
                        festival.startDate && festival.endDate
                          ? `${new Date(festival.startDate).toLocaleDateString()} - ${new Date(
                              festival.endDate
                            ).toLocaleDateString()}`
                          : undefined
                      }
                    />
                  </ListItemButton>
                </ListItem>
              ))}
            </List>
          )}
        </DialogContent>
      </Dialog>
    </Box>
  );
};
