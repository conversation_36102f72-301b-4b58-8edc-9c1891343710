import React, { useState, useEffect } from 'react';
import {
  Button,
  <PERSON>alog,
  <PERSON>alogActions,
  DialogContent,
  DialogTitle,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Box,
  Chip,
  Typography,
  ListItemText
} from '@mui/material';
import { KnowledgeBaseItem } from '../../types';

interface KnowledgeBaseItemFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (item: Omit<KnowledgeBaseItem, '_id' | '_rev' | 'createdAt'> | KnowledgeBaseItem) => void;
  initialCategories: string[];
  editItem: KnowledgeBaseItem | null;
  festivalId: string;
}

export const KnowledgeBaseItemForm: React.FC<KnowledgeBaseItemFormProps> = ({
  open,
  onClose,
  onSubmit,
  initialCategories,
  editItem,
  festivalId
}) => {
  const [title, setTitle] = useState('');
  const [url, setUrl] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [newCategory, setNewCategory] = useState('');
  const [categories, setCategories] = useState<string[]>([]);
  const [customCategory, setCustomCategory] = useState(false);
  
  // Form validation
  const [titleError, setTitleError] = useState('');
  const [urlError, setUrlError] = useState('');
  const [categoryError, setCategoryError] = useState('');

  // Initialize form with data if editing
  useEffect(() => {
    setCategories(initialCategories);
    
    if (editItem) {
      setTitle(editItem.title);
      setUrl(editItem.url);
      setDescription(editItem.description);
      setCategory(editItem.category);
    } else {
      setTitle('');
      setUrl('');
      setDescription('');
      setCategory(initialCategories.length > 0 ? initialCategories[0] : '');
    }
  }, [editItem, initialCategories]);

  const validateForm = (): boolean => {
    let isValid = true;
    
    if (!title.trim()) {
      setTitleError('Title is required');
      isValid = false;
    } else {
      setTitleError('');
    }
    
    if (!url.trim()) {
      setUrlError('URL is required');
      isValid = false;
    } else {
      setUrlError('');
    }
    
    if (customCategory) {
      if (!newCategory.trim()) {
        setCategoryError('Category is required');
        isValid = false;
      } else {
        setCategoryError('');
      }
    } else {
      if (!category) {
        setCategoryError('Category is required');
        isValid = false;
      } else {
        setCategoryError('');
      }
    }
    
    return isValid;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }
    
    // Determine the final category value
    const finalCategory = customCategory ? newCategory.trim() : category;
    
    const itemData = {
      title: title.trim(),
      url: url.trim(),
      description: description.trim() || '', // Allow empty description
      category: finalCategory,
      festivalId,
      type: 'knowledgeBase',
      documentType: 'knowledgeBase',
      syncStatus: 'sync_pending' as const
    };
    
    // If editing, include the ID and revision
    if (editItem) {
      onSubmit({
        ...itemData,
        _id: editItem._id,
        _rev: editItem._rev,
        createdAt: editItem.createdAt
      });
    } else {
      onSubmit(itemData);
    }
  };

  const handleToggleCustomCategory = () => {
    setCustomCategory(!customCategory);
    if (!customCategory) {
      setCategory('');
    } else {
      setNewCategory('');
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {editItem ? 'Edit Resource' : 'Add New Resource'}
      </DialogTitle>
      <DialogContent>
        <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
          <TextField
            label="Title"
            fullWidth
            required
            value={title}
            onChange={(e) => setTitle(e.target.value)}
            error={!!titleError}
            helperText={titleError}
          />
          
          <TextField
            label="URL"
            fullWidth
            required
            value={url}
            onChange={(e) => setUrl(e.target.value)}
            placeholder="e.g., example.com"
            error={!!urlError}
            helperText={urlError || "The URL where this resource can be found"}
          />
          
          <TextField
            label="Description"
            fullWidth
            multiline
            rows={3}
            value={description}
            onChange={(e) => setDescription(e.target.value)}
            placeholder="What is this resource about? Why would someone use it?"
          />
          
          {customCategory ? (
            <TextField
              label="New Category"
              fullWidth
              required
              value={newCategory}
              onChange={(e) => setNewCategory(e.target.value)}
              error={!!categoryError}
              helperText={categoryError || "Create a new category for this resource"}
            />
          ) : (
            <FormControl fullWidth required error={!!categoryError}>
              <InputLabel>Category</InputLabel>
              <Select
                value={category}
                onChange={(e) => setCategory(e.target.value)}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    <Chip label={selected} />
                  </Box>
                )}
              >
                {categories.map((cat) => (
                  <MenuItem key={cat} value={cat}>
                    <ListItemText primary={cat} />
                  </MenuItem>
                ))}
              </Select>
              {categoryError && <FormHelperText>{categoryError}</FormHelperText>}
            </FormControl>
          )}
          
          <Button 
            size="small" 
            onClick={handleToggleCustomCategory}
            sx={{ alignSelf: 'flex-start' }}
          >
            {customCategory ? 'Select Existing Category' : 'Create New Category'}
          </Button>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          {editItem ? 'Update' : 'Add'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};