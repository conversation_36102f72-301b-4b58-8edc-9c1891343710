import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Divider,
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Chip,
  SelectChangeEvent,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import AddIcon from '@mui/icons-material/Add';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import LinkIcon from '@mui/icons-material/Link';
import PhoneIcon from '@mui/icons-material/Phone';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import CachedIcon from '@mui/icons-material/Cached';
import { KnowledgeBaseItem } from '../../types';
import { databaseService } from '../../services/database';
import { useFestival } from '../../contexts/FestivalContext';
import { useAuth } from '../../contexts/AuthContext';
import { useSmartKnowledgeBase, useSmartKnowledgeCategories } from '../../hooks/useSmartData';
import { SyncStatus } from '../../types/base';

// Define the form component inline since TypeScript is having issues with module imports
interface KnowledgeBaseItemFormProps {
  open: boolean;
  onClose: () => void;
  onSubmit: (item: Omit<KnowledgeBaseItem, '_id' | '_rev' | 'createdAt'> | KnowledgeBaseItem) => void;
  initialCategories: string[];
  editItem: KnowledgeBaseItem | null;
  festivalId: string;
  existingItems: KnowledgeBaseItem[];
}

const KnowledgeBaseItemForm: React.FC<KnowledgeBaseItemFormProps> = ({
  open,
  onClose,
  onSubmit,
  initialCategories,
  editItem,
  festivalId,
  existingItems
}) => {
  const [title, setTitle] = useState('');
  const [url, setUrl] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [description, setDescription] = useState('');
  const [category, setCategory] = useState('');
  const [subcategory, setSubcategory] = useState('');
  const [newCategory, setNewCategory] = useState('');
  const [newSubcategory, setNewSubcategory] = useState('');
  const [categories, setCategories] = useState<string[]>([]);
  const [subcategories, setSubcategories] = useState<string[]>([]);
  const [customCategory, setCustomCategory] = useState(false);
  const [customSubcategory, setCustomSubcategory] = useState(false);
  const [showForAllFestivals, setShowForAllFestivals] = useState(false);
  
  // Form validation
  const [titleError, setTitleError] = useState('');
  const [urlError, setUrlError] = useState('');
  const [categoryError, setCategoryError] = useState('');
  const [phoneNumberError, setPhoneNumberError] = useState('');

  // Initialize form with data if editing
  useEffect(() => {
    setCategories(initialCategories);
    
    if (editItem) {
      setTitle(editItem.title);
      setUrl(editItem.url);
      setPhoneNumber(editItem.phoneNumber || '');
      setDescription(editItem.description);
      setCategory(editItem.category);
      setSubcategory(editItem.subcategory || '');
      setShowForAllFestivals(editItem.showForAllFestivals || false);
      setCustomSubcategory(false);
    } else {
      setTitle('');
      setUrl('');
      setPhoneNumber('');
      setDescription('');
      setCategory(initialCategories.length > 0 ? initialCategories[0] : '');
      setSubcategory('');
      setShowForAllFestivals(false);
      setCustomSubcategory(false);
    }
  }, [editItem, initialCategories]);

  // Update subcategories when category changes
  useEffect(() => {
    if (category) {
      // Get subcategories for the selected category from existing items
      const existingSubcategories = existingItems
        .filter((item: KnowledgeBaseItem) => item.category === category && item.subcategory)
        .map((item: KnowledgeBaseItem) => item.subcategory as string)
        .filter((value: string, index: number, self: string[]) => self.indexOf(value) === index);
      
      setSubcategories(existingSubcategories);
    } else {
      setSubcategories([]);
    }
  }, [category, existingItems]);

  const validateForm = (): boolean => {
    let isValid = true;
    
    if (!title.trim()) {
      setTitleError('Title is required');
      isValid = false;
    } else {
      setTitleError('');
    }
    
    if (!url.trim()) {
      setUrlError('URL is required');
      isValid = false;
    } else {
      setUrlError('');
    }
    
    // Phone number validation if provided (optional field)
    if (phoneNumber.trim()) {
      const phoneRegex = /^[+]?[(]?[0-9]{1,4}[)]?[-\s.]?[0-9]{1,4}[-\s.]?[0-9]{1,9}$/;
      if (!phoneRegex.test(phoneNumber.trim())) {
        setPhoneNumberError('Please enter a valid phone number');
        isValid = false;
      } else {
        setPhoneNumberError('');
      }
    } else {
      setPhoneNumberError('');
    }
    
    if (customCategory) {
      if (!newCategory.trim()) {
        setCategoryError('Category is required');
        isValid = false;
      } else {
        setCategoryError('');
      }
    } else {
      if (!category) {
        setCategoryError('Category is required');
        isValid = false;
      } else {
        setCategoryError('');
      }
    }
    
    // Subcategory is optional, so we only validate it if custom subcategory is enabled
    // and the user is trying to create a new one
    if (customSubcategory && !newSubcategory.trim()) {
      // We could add a specific error state for subcategory if needed
      isValid = false;
    }
    
    return isValid;
  };

  const handleSubmit = () => {
    if (!validateForm()) {
      return;
    }
    
    // Determine the final category and subcategory values
    const finalCategory = customCategory ? newCategory.trim() : category;
    const finalSubcategory = customSubcategory ? newSubcategory.trim() : subcategory;
    
    const itemData = {
      title: title.trim(),
      url: url.trim(),
      phoneNumber: phoneNumber.trim() || undefined, // Only include if it has a value
      description: description.trim() || '', // Allow empty description
      category: finalCategory,
      subcategory: finalSubcategory || undefined, // Only include if it has a value
      festivalId,
      showForAllFestivals, // Include the new field
      type: 'knowledgeBase',
      documentType: 'knowledgeBase',
      syncStatus: 'sync_pending' as SyncStatus
    };
    
    // If editing, include the ID and revision
    if (editItem) {
      onSubmit({
        ...itemData,
        _id: editItem._id,
        _rev: editItem._rev,
        createdAt: editItem.createdAt
      });
    } else {
      onSubmit(itemData);
    }
  };

  const handleToggleCustomCategory = () => {
    setCustomCategory(!customCategory);
    if (!customCategory) {
      setCategory('');
    } else {
      setNewCategory('');
    }
  };

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        {editItem ? 'Edit Resource' : 'Add New Resource'}
      </DialogTitle>
      <DialogContent>
        <Box component="form" sx={{ display: 'flex', flexDirection: 'column', gap: 2, mt: 1 }}>
          <TextField
            label="Title"
            fullWidth
            required
            value={title}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setTitle(e.target.value)}
            error={!!titleError}
            helperText={titleError}
          />
          
          <TextField
            label="URL"
            fullWidth
            required
            value={url}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setUrl(e.target.value)}
            placeholder="e.g., example.com"
            error={!!urlError}
            helperText={urlError || "The URL where this resource can be found"}
          />
          
          <TextField
            label="Phone Number"
            fullWidth
            value={phoneNumber}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setPhoneNumber(e.target.value)}
            placeholder="e.g., +44 1234 567890"
            error={!!phoneNumberError}
            helperText={phoneNumberError || "A contact phone number (optional)"}
          />
          
          <TextField
            label="Description"
            fullWidth
            multiline
            rows={3}
            value={description}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) => setDescription(e.target.value)}
            placeholder="What is this resource about? Why would someone use it?"
          />
          
          {customCategory ? (
            <TextField
              label="New Category"
              fullWidth
              required
              value={newCategory}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewCategory(e.target.value)}
              error={!!categoryError}
              helperText={categoryError || "Create a new category for this resource"}
            />
          ) : (
            <FormControl fullWidth required error={!!categoryError}>
              <InputLabel>Category</InputLabel>
              <Select
                value={category}
                onChange={(e: SelectChangeEvent) => setCategory(e.target.value)}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    <Chip label={selected} />
                  </Box>
                )}
              >
                {categories.map((cat) => (
                  <MenuItem key={cat} value={cat}>
                    <ListItemText primary={cat} />
                  </MenuItem>
                ))}
              </Select>
              {categoryError && <FormHelperText>{categoryError}</FormHelperText>}
            </FormControl>
          )}
          
          <Button
            size="small"
            onClick={handleToggleCustomCategory}
            sx={{ alignSelf: 'flex-start' }}
          >
            {customCategory ? 'Select Existing Category' : 'Create New Category'}
          </Button>
          
          {/* Subcategory Field */}
          {customSubcategory ? (
            <TextField
              label="New Subcategory"
              fullWidth
              value={newSubcategory}
              onChange={(e: React.ChangeEvent<HTMLInputElement>) => setNewSubcategory(e.target.value)}
              helperText="Create a new subcategory (optional)"
            />
          ) : (
            <FormControl fullWidth>
              <InputLabel>Subcategory</InputLabel>
              <Select
                value={subcategory}
                onChange={(e: SelectChangeEvent) => setSubcategory(e.target.value)}
                renderValue={(selected) => (
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    <Chip label={selected} />
                  </Box>
                )}
              >
                <MenuItem value="">
                  <ListItemText primary="None (optional)" />
                </MenuItem>
                {subcategories.map((subcat) => (
                  <MenuItem key={subcat} value={subcat}>
                    <ListItemText primary={subcat} />
                  </MenuItem>
                ))}
              </Select>
            </FormControl>
          )}
          
          <Button
            size="small"
            onClick={() => setCustomSubcategory(!customSubcategory)}
            sx={{ alignSelf: 'flex-start' }}
          >
            {customSubcategory ? 'Use Simple Subcategory' : 'Create New Subcategory'}
          </Button>
          
          <Box sx={{ mt: 2, mb: 1 }}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={showForAllFestivals}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setShowForAllFestivals(e.target.checked)}
                  color="primary"
                />
              }
              label="Show this resource for all festivals (not just this one)"
            />
          </Box>
        </Box>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSubmit} variant="contained" color="primary">
          {editItem ? 'Update' : 'Add'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export const KnowledgeBase: React.FC = () => {
  const { activeFestival, loading: festivalLoading } = useFestival();
  const { hasAccess, userEmail } = useAuth();
  const [formOpen, setFormOpen] = useState(false);
  const [editingItem, setEditingItem] = useState<KnowledgeBaseItem | null>(null);
  const [expanded, setExpanded] = useState<string | false>(false);
  
  // Check if user has edit permissions
  const canEdit = hasAccess('knowledge-base-edit');

  // Use smart data hooks for caching
  const itemsResult = useSmartKnowledgeBase({
    onError: (error) => console.error('Error loading knowledge base items:', error)
  });
  
  const categoriesResult = useSmartKnowledgeCategories({
    onError: (error) => console.error('Error loading knowledge base categories:', error)
  });

  // Derived state from smart data hooks
  const items = itemsResult.data || [];
  const categories = categoriesResult.data || ['Substance Info', 'Mental Health', 'Support Contacts'];
  const isLoading = itemsResult.isLoading || categoriesResult.isLoading;
  const error = itemsResult.error || categoriesResult.error;
  const isFromCache = itemsResult.isFromCache || categoriesResult.isFromCache;

  const handleRefresh = async () => {
    await Promise.all([
      itemsResult.refetch(),
      categoriesResult.refetch()
    ]);
  };

  const handleAccordionChange = (panel: string) => (_: React.SyntheticEvent, isExpanded: boolean) => {
    setExpanded(isExpanded ? panel : false);
  };

  const handleOpenLink = (url: string) => {
    if (!url.startsWith('http://') && !url.startsWith('https://')) {
      url = 'https://' + url;
    }
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  const handleAddItem = () => {
    setEditingItem(null);
    setFormOpen(true);
  };

  const handleEditItem = (item: KnowledgeBaseItem) => {
    setEditingItem(item);
    setFormOpen(true);
  };

  const handleDeleteItem = async (itemId: string) => {
    if (!window.confirm('Are you sure you want to delete this item?')) {
      return;
    }

    try {
      await databaseService.deleteKnowledgeBaseItem(itemId);
      await handleRefresh(); // Use smart refresh instead
    } catch (error) {
      console.error('Error deleting knowledge base item:', error);
    }
  };

  const handleFormClose = () => {
    setFormOpen(false);
    setEditingItem(null);
  };

  const handleFormSubmit = async (item: Omit<KnowledgeBaseItem, '_id' | '_rev' | 'createdAt'> | KnowledgeBaseItem) => {
    try {
      if ('_id' in item) {
        await databaseService.updateKnowledgeBaseItem(item as KnowledgeBaseItem);
      } else {
        await databaseService.addKnowledgeBaseItem(item);
      }
      await handleRefresh(); // Use smart refresh instead
      setFormOpen(false);
      setEditingItem(null);
    } catch (error) {
      console.error('Error saving knowledge base item:', error);
    }
  };

  if (!activeFestival) {
    return (
      <Box sx={{ p: 4, textAlign: 'center' }}>
        <Typography color="text.secondary">
          Please select a festival first to access the Knowledge Base.
        </Typography>
      </Box>
    );
  }
  
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">{error}</Alert>
      </Box>
    );
  }

  // Organize items by category and subcategory
  const getItemsByCategory = (category: string) => {
    return items.filter(item => item.category === category);
  };

  // Group items by subcategory within a category
  const getSubcategoriesForCategory = (category: string): string[] => {
    const categoryItems = getItemsByCategory(category);
    const subcategories = categoryItems
      .map(item => item.subcategory || '')
      .filter((subcategory, index, self) =>
        subcategory !== '' && self.indexOf(subcategory) === index
      );
    return subcategories;
  };

  // Get items for a specific subcategory
  const getItemsBySubcategory = (category: string, subcategory: string) => {
    return items.filter(item =>
      item.category === category && item.subcategory === subcategory
    );
  };

  // Get items without a subcategory for a category
  const getItemsWithoutSubcategory = (category: string) => {
    return items.filter(item =>
      item.category === category && (!item.subcategory || item.subcategory === '')
    );
  };

  // Resource Tile Component
  const ResourceTile: React.FC<{item: KnowledgeBaseItem}> = ({item}) => {
    return (
      <Paper
        elevation={2}
        sx={{
          p: 2,
          height: '100%',
          display: 'flex',
          flexDirection: 'column',
          cursor: 'pointer',
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            transform: 'translateY(-5px)',
            boxShadow: 6,
          },
          position: 'relative',
          borderLeft: item.showForAllFestivals ? '4px solid' : 'none',
          borderColor: item.showForAllFestivals ? 'secondary.main' : 'transparent'
        }}
        onClick={() => handleOpenLink(item.url)}
      >
        {canEdit && (
          <Box sx={{ position: 'absolute', top: 8, right: 8, display: 'flex', gap: 0.5 }}>
            <Tooltip title="Edit">
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleEditItem(item);
                }}
              >
                <EditIcon fontSize="small" />
              </IconButton>
            </Tooltip>
            <Tooltip title="Delete">
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  handleDeleteItem(item._id);
                }}
              >
                <DeleteIcon fontSize="small" />
              </IconButton>
            </Tooltip>
          </Box>
        )}
        
        <Box sx={{ mt: 1 }}>
          <Typography
            variant="h6"
            sx={{
              mb: 1,
              fontSize: '1.1rem',
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}
          >
            <LinkIcon fontSize="small" />
            {item.title}
          </Typography>
          
          {/* Left border indicates availability at all festivals */}
        </Box>
        
        {/* Show phone number if available, otherwise show URL */}
        <Typography
          variant="body2"
          sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 0.5,
            mb: 1,
            color: 'primary.main'
          }}
        >
          {item.phoneNumber ? (
            <>
              <PhoneIcon fontSize="small" />
              {item.phoneNumber}
            </>
          ) : (
            <>
              <LinkIcon fontSize="small" />
              {item.url}
            </>
          )}
        </Typography>
        
        <Typography
          variant="body2"
          color="text.secondary"
          sx={{
            flexGrow: 1,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            display: '-webkit-box',
            WebkitLineClamp: 3,
            WebkitBoxOrient: 'vertical',
          }}
        >
          {item.description}
        </Typography>
      </Paper>
    );
  };

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 3, alignItems: 'center' }}>
        {userEmail && (
          <Typography variant="body2" color="text.secondary">
            Logged in as: {userEmail}
          </Typography>
        )}
        
        {canEdit && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            onClick={handleAddItem}
          >
            Add Resource
          </Button>
        )}
      </Box>

      {categories.length === 0 ? (
        <Alert severity="info" sx={{ mt: 2 }}>
          No resource categories yet. Add your first resource to get started.
        </Alert>
      ) : (
        <Box sx={{ mb: 4 }}>
          {categories.map((category) => {
            const categoryItems = getItemsByCategory(category);
            const subcategories = getSubcategoriesForCategory(category);
            const itemsWithoutSubcategory = getItemsWithoutSubcategory(category);
            
            return (
              <Box key={category} sx={{ mb: 4 }}>
                <Typography
                  variant="h5"
                  sx={{
                    mb: 2,
                    borderBottom: '2px solid',
                    borderColor: 'primary.main',
                    paddingBottom: 1
                  }}
                >
                  {category} ({categoryItems.length})
                </Typography>
                
                {/* Items without subcategory */}
                {itemsWithoutSubcategory.length > 0 && (
                  <Box sx={{ mb: 3 }}>
                    <Box
                      sx={{
                        display: 'grid',
                        gridTemplateColumns: {
                          xs: '1fr',
                          sm: 'repeat(2, 1fr)',
                          md: 'repeat(3, 1fr)',
                          lg: 'repeat(4, 1fr)'
                        },
                        gap: 2
                      }}
                    >
                      {itemsWithoutSubcategory.map(item => (
                        <ResourceTile key={item._id} item={item} />
                      ))}
                    </Box>
                  </Box>
                )}
                
                {/* Items with subcategories */}
                {subcategories.map(subcategory => {
                  const subcategoryItems = getItemsBySubcategory(category, subcategory);
                  
                  return subcategoryItems.length > 0 ? (
                    <Box key={subcategory} sx={{ mb: 3 }}>
                      <Typography
                        variant="h6"
                        sx={{
                          mb: 2,
                          ml: 2,
                          color: 'secondary.main',
                          display: 'flex',
                          alignItems: 'center'
                        }}
                      >
                        <Box
                          component="span"
                          sx={{
                            width: 8,
                            height: 8,
                            bgcolor: 'secondary.main',
                            borderRadius: '50%',
                            display: 'inline-block',
                            mr: 1
                          }}
                        />
                        {subcategory} ({subcategoryItems.length})
                      </Typography>
                      
                      <Box
                        sx={{
                          display: 'grid',
                          gridTemplateColumns: {
                            xs: '1fr',
                            sm: 'repeat(2, 1fr)',
                            md: 'repeat(3, 1fr)',
                            lg: 'repeat(4, 1fr)'
                          },
                          gap: 2
                        }}
                      >
                        {subcategoryItems.map(item => (
                          <ResourceTile key={item._id} item={item} />
                        ))}
                      </Box>
                    </Box>
                  ) : null;
                })}
              </Box>
            );
          })}
        </Box>
      )}

      {formOpen && (
        <KnowledgeBaseItemForm
          open={formOpen}
          onClose={handleFormClose}
          onSubmit={handleFormSubmit}
          initialCategories={categories}
          editItem={editingItem}
          festivalId={activeFestival?._id || ''}
          existingItems={items}
        />
      )}
    </Box>
  );
};