import React, { Component, ErrorInfo, ReactNode } from 'react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
}

/**
 * ErrorSuppressor is a component that catches and suppresses specific React errors
 * to prevent them from crashing the application. It's used as a workaround for
 * the "Maximum update depth exceeded" error that occurs in the AdmissionForm.
 */
class ErrorSuppressor extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(_: Error): State {
    // Update state so the next render will show the fallback UI.
    return { hasError: true };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Only log the error if it's not the "Maximum update depth exceeded" error
    if (!error.message.includes('Maximum update depth exceeded')) {
      console.error('Uncaught error:', error, errorInfo);
    }
  }

  render() {
    // Even if there's an error, we still render the children
    // This allows the component to function despite the error
    return this.props.children;
  }
}

export default ErrorSuppressor;