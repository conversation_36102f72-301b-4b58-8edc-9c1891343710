import React, { useState, useCallback } from 'react';
import {
  Paper,
  Typography,
  Box,
  Button,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  Alert,
  CircularProgress,
  Grid
} from '@mui/material';
import { useFestival } from '../../contexts/FestivalContext';
import { useSiteLocation } from '../../contexts/SiteLocationContext';
import { databaseService } from '../../services/database/index';
import { NewSensoryHubVisit, VisitPurpose, UserType } from '../../types/sensory-hub';

interface SensoryHubVisitFormProps {
  onSuccess?: () => void;
}

export const SensoryHubVisitForm: React.FC<SensoryHubVisitFormProps> = ({ onSuccess }) => {
  const { activeFestival } = useFestival();
  const { activeSiteLocation } = useSiteLocation();
  
  // Form state
  const [purpose, setPurpose] = useState<VisitPurpose>('look_around');
  const [userType, setUserType] = useState<UserType>('public');
  const [teamName, setTeamName] = useState('');
  
  // UI state
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  // Form validation
  const isFormValid = useCallback(() => {
    if (!activeFestival) return false;
    if (userType === 'crew' && !teamName.trim()) return false;
    return true;
  }, [activeFestival, userType, teamName]);

  // Handle form submission
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!isFormValid()) {
      setError('Please fill in all required fields');
      return;
    }

    if (!activeFestival) {
      setError('No active festival selected');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);
      setSuccess(null);

      // Wait for database initialization
      await databaseService.waitForInitialization();

      const visitData: NewSensoryHubVisit = {
        documentType: 'sensory-hub-visit',
        type: 'sensory-hub-visit',
        festivalId: activeFestival._id,
        siteLocationId: activeSiteLocation?.id,
        visitTimestamp: new Date().toISOString(),
        purpose,
        userType,
        ...(userType === 'crew' && { teamName: teamName.trim() })
      };

      await databaseService.addSensoryHubVisit(visitData);
      
      // Clear form
      setPurpose('look_around');
      setUserType('public');
      setTeamName('');
      
      setSuccess('Visit recorded successfully!');
      
      // Call success callback if provided
      if (onSuccess) {
        onSuccess();
      }
      
    } catch (error) {
      console.error('Failed to record visit:', error);
      setError('Failed to record visit. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  }, [activeFestival, activeSiteLocation, purpose, userType, teamName, isFormValid, onSuccess]);

  // Handle purpose change
  const handlePurposeChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setPurpose(event.target.value as VisitPurpose);
    setError(null);
  }, []);

  // Handle user type change
  const handleUserTypeChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const newUserType = event.target.value as UserType;
    setUserType(newUserType);
    
    // Clear team name if switching to public
    if (newUserType === 'public') {
      setTeamName('');
    }
    
    setError(null);
  }, []);

  // Handle team name change
  const handleTeamNameChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    setTeamName(event.target.value);
    setError(null);
  }, []);

  if (!activeFestival) {
    return (
      <Paper sx={{ p: 3 }}>
        <Alert severity="warning">
          Please select an active festival to record visits.
        </Alert>
      </Paper>
    );
  }

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" sx={{ mb: 3, fontWeight: 'bold', color: 'text.primary' }}>
        Record Sensory Hub Visit
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {success && (
        <Alert severity="success" sx={{ mb: 3 }}>
          {success}
        </Alert>
      )}

      <form onSubmit={handleSubmit}>
        <Grid container spacing={3}>
          {/* Visit Purpose */}
          <Grid size={{ xs: 12, md: 6 }}>
            <FormControl component="fieldset" fullWidth>
              <FormLabel component="legend" sx={{ mb: 1, fontWeight: 'bold' }}>
                Visit Purpose *
              </FormLabel>
              <RadioGroup
                value={purpose}
                onChange={handlePurposeChange}
                row
              >
                <FormControlLabel
                  value="look_around"
                  control={<Radio />}
                  label="Look Around"
                />
                <FormControlLabel
                  value="use_service"
                  control={<Radio />}
                  label="Use Service"
                />
              </RadioGroup>
            </FormControl>
          </Grid>

          {/* User Type */}
          <Grid size={{ xs: 12, md: 6 }}>
            <FormControl component="fieldset" fullWidth>
              <FormLabel component="legend" sx={{ mb: 1, fontWeight: 'bold' }}>
                User Type *
              </FormLabel>
              <RadioGroup
                value={userType}
                onChange={handleUserTypeChange}
                row
              >
                <FormControlLabel
                  value="public"
                  control={<Radio />}
                  label="Public"
                />
                <FormControlLabel
                  value="crew"
                  control={<Radio />}
                  label="Crew"
                />
              </RadioGroup>
            </FormControl>
          </Grid>

          {/* Team Name - Only shown for crew */}
          {userType === 'crew' && (
            <Grid size={{ xs: 12 }}>
              <TextField
                fullWidth
                label="Team Name"
                value={teamName}
                onChange={handleTeamNameChange}
                required
                placeholder="Enter team name"
                helperText="Required for crew visits"
                error={userType === 'crew' && !teamName.trim()}
              />
            </Grid>
          )}

          {/* Festival and Location Info */}
          <Grid size={{ xs: 12 }}>
            <Box sx={{ p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
              <Typography variant="body2" color="text.secondary">
                <strong>Festival:</strong> {activeFestival.name}
              </Typography>
              {activeSiteLocation && (
                <Typography variant="body2" color="text.secondary">
                  <strong>Location:</strong> {activeSiteLocation.type === 'arena' ? 'Arena' : 'Campsite'}
                </Typography>
              )}
            </Box>
          </Grid>

          {/* Submit Button */}
          <Grid size={{ xs: 12 }}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
              <Button
                type="submit"
                variant="contained"
                color="primary"
                disabled={isSubmitting || !isFormValid()}
                startIcon={isSubmitting ? <CircularProgress size={20} /> : null}
                sx={{ minWidth: 120 }}
              >
                {isSubmitting ? 'Recording...' : 'Record Visit'}
              </Button>
            </Box>
          </Grid>
        </Grid>
      </form>
    </Paper>
  );
};