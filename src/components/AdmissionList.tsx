import React, { useState } from 'react';
import { WelfareAdmission } from '../types/admission';
import { 
  Box, 
  Paper, 
  Typography, 
  CircularProgress,
  Button,
} from '@mui/material';
import { 
  DataGrid, 
  GridColDef, 
  GridRenderCellParams,
  GridToolbar,
} from '@mui/x-data-grid';
import { format, formatDistance, differenceInYears } from 'date-fns';
import { AdmissionModals } from './admission/AdmissionModals';

export interface AdmissionListProps {
  admissions: WelfareAdmission[];
  loading: boolean;
  error: Error | null;
  onDischarge: (id: string, notes: string, dischargeTime: string) => Promise<void>;
  onEdit: (admission: WelfareAdmission) => void;
}

export const AdmissionList: React.FC<AdmissionListProps> = ({
  admissions,
  loading,
  error,
  onDischarge,
  onEdit
}) => {
  const [showDischargeModal, setShowDischargeModal] = useState(false);
  const [selectedAdmission, setSelectedAdmission] = useState<WelfareAdmission | null>(null);
  const [dischargeNotes, setDischargeNotes] = useState('');
  const [dischargeTime, setDischargeTime] = useState(new Date().toISOString().slice(0, 16));
  const [isSubmitting, setIsSubmitting] = useState(false);

  const getAdmissionId = (admission: WelfareAdmission): string => {
    if (!admission._id) {
      throw new Error('Admission has no valid ID');
    }
    return admission._id;
  };

  const isValidDate = (dateString: string | undefined): boolean => {
    if (!dateString) return false;
    const date = new Date(dateString);
    return date instanceof Date && !isNaN(date.getTime());
  };

  const getTimeSinceAdmission = (admission: WelfareAdmission): string => {
    if (!isValidDate(admission.Attended)) {
      return 'Unknown';
    }
    try {
      return formatDistance(new Date(admission.Attended), new Date(), { addSuffix: true });
    } catch (error) {
      console.error('Error formatting distance:', error);
      return 'Unknown';
    }
  };

  const getTimeSinceDischarge = (admission: WelfareAdmission): string => {
    if (!isValidDate(admission.DischargeTime)) {
      return '';
    }
    try {
      return formatDistance(new Date(admission.DischargeTime), new Date(), { addSuffix: true });
    } catch (error) {
      console.error('Error formatting discharge distance:', error);
      return '';
    }
  };

  const handleDischargeClick = (admission: WelfareAdmission) => {
    setSelectedAdmission(admission);
    setDischargeTime(new Date().toISOString().slice(0, 16));
    setDischargeNotes('');
    setShowDischargeModal(true);
  };

  const handleConfirmDischarge = async () => {
    if (!selectedAdmission || !dischargeNotes.trim()) return;

    setIsSubmitting(true);
    try {
      await onDischarge(getAdmissionId(selectedAdmission), dischargeNotes, dischargeTime);
      handleCloseDischargeModal();
    } catch (error) {
      console.error('Error discharging patient:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCloseDischargeModal = () => {
    setShowDischargeModal(false);
    setSelectedAdmission(null);
    setDischargeNotes('');
    setDischargeTime(new Date().toISOString().slice(0, 16));
  };

  const inBayColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Name',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row) return 'n/a';
        const admission = params.row;
        return `${admission.FirstName || ''} ${admission.Surname || ''}`.trim().toLowerCase() || 'n/a';
      },
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        const admission = params.row;
        return `${admission.FirstName || ''} ${admission.Surname || ''}`.trim() || 'N/A';
      },
    },
    {
      field: 'Age',
      headerName: 'Age',
      width: 70,
      maxWidth: 70,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row) return null;
        const admission = params.row;
        return admission.Age || (admission.DOB ? differenceInYears(new Date(), new Date(admission.DOB)) : null);
      },
    },
    {
      field: 'BaysOrChairs',
      headerName: 'Bay/Chair',
      width: 90,
      maxWidth: 90,
      sortable: true,
      filterable: true,
    },
    {
      field: 'Location',
      headerName: 'Location',
      width: 90,
      maxWidth: 90,
      sortable: true,
      filterable: true,
    },
    {
      field: 'timeSinceAdmission',
      headerName: 'Time Since',
      width: 110,
      sortable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row) return -1;
        const admission = params.row;
        return admission.Attended ? new Date(admission.Attended).getTime() : -1;
      },
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        return getTimeSinceAdmission(params.row);
      },
    },
    {
      field: 'ReasonCategory',
      headerName: 'Reason',
      flex: 0.5,
      minWidth: 90,
      sortable: true,
      filterable: true,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 140,
      sortable: false,
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => (
        <Box display="flex" gap={1}>
          <Button
            variant="contained"
            size="small"
            onClick={() => onEdit(params.row)}
            sx={{ bgcolor: 'primary.main', fontSize: '0.7rem', py: 0.5, px: 1, minWidth: 'auto' }}
          >
            Edit
          </Button>
          <Button
            variant="contained"
            size="small"
            onClick={() => handleDischargeClick(params.row)}
            sx={{ bgcolor: 'error.main', fontSize: '0.7rem', py: 0.5, px: 1, minWidth: 'auto' }}
          >
            Discharge
          </Button>
        </Box>
      ),
    },
  ];

  const dischargedColumns: GridColDef[] = [
    {
      field: 'name',
      headerName: 'Name',
      flex: 1,
      minWidth: 100,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row) return 'n/a';
        const admission = params.row;
        return `${admission.FirstName || ''} ${admission.Surname || ''}`.trim().toLowerCase() || 'n/a';
      },
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        const admission = params.row;
        return `${admission.FirstName || ''} ${admission.Surname || ''}`.trim() || 'N/A';
      },
    },
    {
      field: 'Age',
      headerName: 'Age',
      width: 70,
      maxWidth: 70,
      sortable: true,
      filterable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row) return null;
        const admission = params.row;
        return admission.Age || (admission.DOB ? differenceInYears(new Date(), new Date(admission.DOB)) : null);
      },
    },
    {
      field: 'BaysOrChairs',
      headerName: 'Bay/Chair',
      width: 90,
      maxWidth: 90,
      sortable: true,
      filterable: true,
    },
    {
      field: 'Location',
      headerName: 'Location',
      width: 90,
      maxWidth: 90,
      sortable: true,
      filterable: true,
    },
    {
      field: 'DischargeTime',
      headerName: 'Discharged',
      width: 100,
      sortable: true,
      type: 'dateTime',
      valueGetter: (params: any) => {
        if (!params || !params.row) return null;
        const admission = params.row;
        return admission.DischargeTime ? new Date(admission.DischargeTime) : null;
      },
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        const admission = params.row;
        return isValidDate(admission.DischargeTime) ? format(new Date(admission.DischargeTime), 'Pp') : '';
      },
    },
    {
      field: 'timeSinceDischarge',
      headerName: 'Time Since',
      width: 110,
      sortable: true,
      valueGetter: (params: any) => {
        if (!params || !params.row) return -1;
        const admission = params.row;
        return admission.DischargeTime ? new Date(admission.DischargeTime).getTime() : -1;
      },
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => {
        return getTimeSinceDischarge(params.row);
      },
    },
    {
      field: 'ReasonCategory',
      headerName: 'Reason',
      flex: 0.5,
      minWidth: 90,
      sortable: true,
      filterable: true,
    },
    {
      field: 'actions',
      headerName: 'Actions',
      width: 80,
      sortable: false,
      renderCell: (params: GridRenderCellParams<WelfareAdmission>) => (
        <Button
          variant="contained"
          size="small"
          onClick={() => onEdit(params.row)}
          sx={{ bgcolor: 'primary.main', fontSize: '0.7rem', py: 0.5, px: 1, minWidth: 'auto' }}
        >
          Edit
        </Button>
      ),
    },
  ];

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Box p={2}>
        <Typography color="error">Error loading admissions: {error.message}</Typography>
      </Box>
    );
  }

  const inBayNowAdmissions = admissions.filter(admission => admission.InBayNow);
  const dischargedAdmissions = admissions.filter(admission => !admission.InBayNow);

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        In Bay Now
      </Typography>
      <Paper sx={{ height: 'auto', minHeight: '40vh', marginBottom: 2 }}>
        <DataGrid
          rows={inBayNowAdmissions}
          columns={inBayColumns}
          getRowId={getAdmissionId}
          density="compact"
          slots={{ toolbar: GridToolbar }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
              quickFilterProps: { debounceMs: 300 },
            },
          }}
          initialState={{
            sorting: {
              sortModel: [{ field: 'timeSinceAdmission', sort: 'desc' }],
            },
            columns: {
              columnVisibilityModel: {
                // Hide less important columns on small screens
                ReasonCategory: window.innerWidth > 600,
                timeSinceAdmission: window.innerWidth > 500,
              },
            },
          }}
          sx={{
            '& .MuiDataGrid-cell': {
              fontSize: '0.8rem',
              padding: '0 8px',
            },
            '& .MuiDataGrid-columnHeaders': {
              fontSize: '0.8rem',
            },
            '& .MuiDataGrid-toolbarContainer': {
              padding: '4px 8px',
            },
            '& .MuiDataGrid-columnHeaderTitle': {
              fontWeight: 'bold',
              fontSize: '0.8rem',
            },
            border: 'none',
            width: '100%',
            overflow: 'hidden',
          }}
          autoHeight
        />
      </Paper>

      <Typography variant="h6" gutterBottom>
        Discharged Clients
      </Typography>
      <Paper sx={{ height: 'auto', minHeight: '40vh' }}>
        <DataGrid
          rows={dischargedAdmissions}
          columns={dischargedColumns}
          getRowId={getAdmissionId}
          density="compact"
          slots={{ toolbar: GridToolbar }}
          slotProps={{
            toolbar: {
              showQuickFilter: true,
              quickFilterProps: { debounceMs: 300 },
            },
          }}
          initialState={{
            sorting: {
              sortModel: [{ field: 'DischargeTime', sort: 'desc' }],
            },
            columns: {
              columnVisibilityModel: {
                // Hide less important columns on small screens
                ReasonCategory: window.innerWidth > 600,
                timeSinceDischarge: window.innerWidth > 500,
                DischargeTime: window.innerWidth > 450,
              },
            },
          }}
          sx={{
            '& .MuiDataGrid-cell': {
              fontSize: '0.8rem',
              padding: '0 8px',
            },
            '& .MuiDataGrid-columnHeaders': {
              fontSize: '0.8rem',
            },
            '& .MuiDataGrid-toolbarContainer': {
              padding: '4px 8px',
            },
            '& .MuiDataGrid-columnHeaderTitle': {
              fontWeight: 'bold',
              fontSize: '0.8rem',
            },
            border: 'none',
            width: '100%',
            overflow: 'hidden',
          }}
          autoHeight
        />
      </Paper>

      <AdmissionModals
        showReAdmitModal={false}
        showConfirmReAdmit={false}
        showConfirmDischarge={showDischargeModal}
        reAdmitLocation={0}
        reAdmitNotes=""
        dischargeNotes={dischargeNotes}
        dischargeTime={dischargeTime}
        bayStatus={null}
        isSubmitting={isSubmitting}
        onReAdmitLocationChange={() => {}}
        onReAdmitNotesChange={() => {}}
        onDischargeNotesChange={setDischargeNotes}
        onDischargeTimeChange={setDischargeTime}
        onCloseReAdmit={() => {}}
        onCloseConfirmReAdmit={() => {}}
        onCloseConfirmDischarge={handleCloseDischargeModal}
        onConfirmReAdmit={() => {}}
        onConfirmDischarge={handleConfirmDischarge}
        onShowConfirmReAdmit={() => {}}
      />
    </Box>
  );
};

export default AdmissionList;
