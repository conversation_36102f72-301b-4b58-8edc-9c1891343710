import React, { useState, useEffect } from 'react';
import { Box, Typography, Paper, Divider, Fade } from '@mui/material';
import { styled } from '@mui/material/styles';

// Styled component for screenshots
const ScreenshotImage = styled('img')({
  width: '100%',
  maxWidth: '800px',
  borderRadius: '8px',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
  marginTop: '16px',
  marginBottom: '24px',
  transition: 'transform 0.3s ease',
  '&:hover': {
    transform: 'scale(1.02)',
  },
});

// Styled component for section content
const SectionContent = styled(Box)(({ theme }) => ({
  '& strong': {
    fontWeight: 'bold',
    display: 'block',
    fontSize: '1.2rem',
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(1),
    color: theme.palette.primary.main
  },
  '& h3': {
    fontSize: '1.1rem',
    fontWeight: 'bold',
    marginTop: theme.spacing(3),
    marginBottom: theme.spacing(1),
    color: theme.palette.primary.main
  },
  '& em': {
    fontStyle: 'italic',
    color: theme.palette.text.secondary
  },
  '& p': {
    marginBottom: theme.spacing(2),
    lineHeight: 1.6
  },
  '& ul': {
    marginBottom: theme.spacing(2),
    paddingLeft: theme.spacing(3)
  },
  '& li': {
    marginBottom: theme.spacing(1),
    lineHeight: 1.6,
    position: 'relative',
    paddingLeft: theme.spacing(1)
  }
}));

interface MarkdownContentProps {
  content: string;
  title?: string;
}

interface Section {
  title: string;
  content: string;
  screenshot?: string;
}

export const MarkdownContent: React.FC<MarkdownContentProps> = ({ content, title }) => {
  const [sections, setSections] = useState<Section[]>([]);
  const [loaded, setLoaded] = useState(false);

  useEffect(() => {
    // Parse the content into sections with screenshots
    parseContent();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [content, title]);

  const parseContent = () => {
    // First, handle the case where content might be empty
    if (!content || content.trim() === '') {
      setSections([]);
      setLoaded(true);
      return;
    }
    
    // Map of available screenshots (based on the files in public/images/userguide)
    const availableScreenshots = [
      'getting-started-1',
      'dashboard-1',
      'admissions-1',
      'admissions-2',
      'front-of-house-1',
      'lost-property-1',
      'knowledge-base-1',
      'reports-1'
    ];
    
    // Map section titles to screenshot names
    const sectionScreenshotMap: Record<string, string> = {
      'Getting Started': 'getting-started-1',
      'Dashboard': 'dashboard-1',
      'Admissions': 'admissions-1',
      'Front of House': 'front-of-house-1',
      'Lost Property': 'lost-property-1',
      'Knowledge Base': 'knowledge-base-1',
      'Reports': 'reports-1'
    };
    
    // Split content by main section headers (##)
    const mainSections = content.split(/\n## /);
    
    // Process the first part (might contain the title)
    let introContent = mainSections[0];
    if (introContent.startsWith('# ')) {
      // Remove the title
      introContent = introContent.replace(/^# [^\n]+\n/, '');
    }
    
    const parsedSections: Section[] = [];
    
    // If there's intro content, add it as a section
    if (introContent.trim()) {
      parsedSections.push({
        title: 'Introduction',
        content: introContent.trim()
      });
    }
    
    // Process each main section
    for (let i = 1; i < mainSections.length; i++) {
      const mainSection = mainSections[i];
      
      // Extract the section title (first line)
      const titleMatch = mainSection.match(/^([^\n]+)/);
      if (!titleMatch) continue;
      
      const mainTitle = titleMatch[1].trim();
      let mainContent = mainSection.substring(titleMatch[0].length).trim();
      
      // Determine screenshot based on section title
      let screenshot = sectionScreenshotMap[mainTitle];
      
      // Check for screenshots in this section - both formats
      const oldFormatMatch = mainContent.match(/\[Screenshot: ([^\]]+)\]/);
      const newFormatMatch = mainContent.match(/\[(dashboard-1|admissions-1|admissions-2|front-of-house-1|getting-started-1|knowledge-base-1|lost-property-1|reports-1)\]/);
      
      if (oldFormatMatch) {
        // Remove the old format placeholder
        mainContent = mainContent.replace(/\[Screenshot: ([^\]]+)\]/, '');
      }
      
      if (newFormatMatch) {
        // Use the screenshot name directly from the new format
        screenshot = newFormatMatch[1];
        // Remove the new format placeholder - use a global replace to catch all instances
        mainContent = mainContent.replace(new RegExp(`\\[${screenshot}\\]`, 'g'), '');
        
        // Log for debugging
        console.log(`Found screenshot: ${screenshot} for section: ${mainTitle}`);
      }
      
      // Split by subsections (###)
      const subSections = mainContent.split(/\n### /);
      
      // The first part is the main section content
      const mainSectionContent = subSections[0].trim();
      
      // Create the main section
      parsedSections.push({
        title: mainTitle,
        content: mainSectionContent,
        screenshot
      });
      
      // Process subsections
      for (let j = 1; j < subSections.length; j++) {
        const subSection = subSections[j];
        
        // Extract the subsection title
        const subTitleMatch = subSection.match(/^([^\n]+)/);
        if (!subTitleMatch) continue;
        
        const subTitle = subTitleMatch[1].trim();
        let subContent = subSection.substring(subTitleMatch[0].length).trim();
        
        // Check for screenshots in this subsection - both formats
        const subOldFormatMatch = subContent.match(/\[Screenshot: ([^\]]+)\]/);
        const subNewFormatMatch = subContent.match(/\[(dashboard-1|admissions-1|admissions-2|front-of-house-1|getting-started-1|knowledge-base-1|lost-property-1|reports-1)\]/);
        
        // For "Creating a New Admission" subsection, use admissions-2 screenshot
        let subScreenshot = undefined;
        if (subTitle === "Creating a New Admission") {
          subScreenshot = "admissions-2";
        }
        
        if (subOldFormatMatch) {
          // Remove the old format placeholder
          subContent = subContent.replace(/\[Screenshot: ([^\]]+)\]/, '');
        }
        
        if (subNewFormatMatch) {
          // Use the screenshot name directly from the new format
          subScreenshot = subNewFormatMatch[1];
          // Remove the new format placeholder - use a global replace to catch all instances
          subContent = subContent.replace(new RegExp(`\\[${subScreenshot}\\]`, 'g'), '');
          
          // Log for debugging
          console.log(`Found screenshot: ${subScreenshot} for subsection: ${subTitle}`);
        }
        
        // Add the subsection
        parsedSections.push({
          title: `${mainTitle} - ${subTitle}`,
          content: subContent,
          screenshot: subScreenshot
        });
      }
    }
    
    // Handle special case for Technical Support section
    const techSupportMatch = content.match(/---\s*\n\s*\*\*Technical Support\*\*([\s\S]+)$/);
    if (techSupportMatch) {
      parsedSections.push({
        title: 'Technical Support',
        content: techSupportMatch[1].trim()
      });
    }
    
    setSections(parsedSections);
    setLoaded(true);
  };

  // Process markdown formatting for a section
  const formatContent = (text: string) => {
    // First, handle headers (###)
    let processedText = text.replace(/^###\s+([^\n]+)/gm, '<h3>$1</h3>');
    
    // We don't need to handle image placeholders here as they're handled in parseContent
    
    // Identify list blocks and non-list blocks
    const listBlocks: string[] = [];
    const nonListBlocks: string[] = [];
    
    // Split by double newlines to get paragraphs
    const paragraphs = processedText.split(/\n\n+/);
    
    paragraphs.forEach(paragraph => {
      // Check if this paragraph contains list items
      if (paragraph.match(/^(\d+\.|-)\s+/m)) {
        listBlocks.push(paragraph);
      } else {
        nonListBlocks.push(paragraph);
      }
    });
    
    // Process list blocks
    const processedListBlocks = listBlocks.map(block => {
      // Split into lines
      const lines = block.split('\n');
      
      // Process each line
      const processedLines = lines.map(line => {
        // Check if it's a list item
        if (line.match(/^\d+\.\s+/)) {
          return `<li>${line.replace(/^\d+\.\s+/, '')}</li>`;
        } else if (line.match(/^-\s+/)) {
          return `<li>${line.replace(/^-\s+/, '')}</li>`;
        }
        return line;
      });
      
      // Join back into a block with proper list tags
      return `<ul style="padding-left: 20px; margin-bottom: 16px;">${processedLines.join('')}</ul>`;
    });
    
    // Process non-list blocks
    const processedNonListBlocks = nonListBlocks.map(block => {
      // Skip blocks that are already HTML (like <h3>)
      if (block.startsWith('<h3>')) {
        return block;
      }
      return `<p>${block}</p>`;
    });
    
    // Combine all blocks
    let combinedHtml = [...processedNonListBlocks, ...processedListBlocks].join('');
    
    // Apply other formatting
    return combinedHtml
      // Replace bold (**text**)
      .replace(/\*\*([^*]+)\*\*/g, '<strong>$1</strong>')
      // Replace italic (*text*)
      .replace(/\*([^*]+)\*/g, '<em>$1</em>')
      // Replace any remaining ### headers (in case they weren't at the start of a paragraph)
      .replace(/###\s+([^\n<]+)/g, '<h3>$1</h3>');
  };

  return (
    <Fade in={loaded} timeout={500}>
      <Paper sx={{ p: 4, mb: 3, borderRadius: 2 }}>
        {title && (
          <Box mb={4}>
            <Typography variant="h4" component="h1" gutterBottom color="primary" fontWeight="500">
              {title}
            </Typography>
            <Divider />
          </Box>
        )}
        
        {sections.map((section, index) => (
          <Box key={index} mb={5}>
            <Typography variant="h5" component="h2" gutterBottom color="primary.dark" fontWeight="500">
              {section.title}
            </Typography>
            
            <SectionContent
              dangerouslySetInnerHTML={{ __html: formatContent(section.content) }}
            />
            
            {section.screenshot && (
              <>
                {/* Debug info */}
                <Typography variant="caption" color="text.secondary" sx={{ display: 'block', textAlign: 'center', mb: 1 }}>
                  Screenshot: {section.screenshot}
                </Typography>
                
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    mt: 3,
                    mb: 2
                  }}
                >
                  <ScreenshotImage
                    src={`/images/userguide/${section.screenshot}.png`}
                    alt={`Screenshot for ${section.title}`}
                    style={{ display: 'block', maxWidth: '100%' }}
                  />
                </Box>
              </>
            )}
            
            {index < sections.length - 1 && (
              <Divider sx={{ mt: 4 }} />
            )}
          </Box>
        ))}
      </Paper>
    </Fade>
  );
};