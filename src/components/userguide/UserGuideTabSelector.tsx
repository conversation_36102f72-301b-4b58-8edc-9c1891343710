import React from 'react';
import { Tabs, Tab, Box, Paper } from '@mui/material';

export type UserGuideTab = 
  | 'getting-started' 
  | 'dashboard' 
  | 'admissions' 
  | 'front-of-house' 
  | 'shift-management' 
  | 'lost-property' 
  | 'knowledge-base' 
  | 'reports' 
  | 'feedback' 
  | 'access-management' 
  | 'data-management' 
  | 'technical-support'
  | 'faq';

interface TabSelectorProps {
  activeTab: UserGuideTab;
  onTabChange: (tab: UserGuideTab) => void;
}

export const UserGuideTabSelector: React.FC<TabSelectorProps> = ({ activeTab, onTabChange }) => {
  const handleChange = (_event: React.SyntheticEvent, newValue: UserGuideTab) => {
    onTabChange(newValue);
  };

  return (
    <Paper
      elevation={2}
      sx={{
        borderRadius: 2,
        mb: 3,
        overflow: 'hidden',
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.05)'
      }}
    >
      <Tabs
        value={activeTab}
        onChange={handleChange}
        aria-label="user guide tabs"
        textColor="primary"
        indicatorColor="primary"
        variant="scrollable"
        scrollButtons="auto"
        sx={{
          '& .MuiTab-root': {
            textTransform: 'none',
            minWidth: 120,
            fontSize: '0.875rem',
            fontWeight: 500,
            color: 'text.secondary',
            py: 2,
            '&.Mui-selected': {
              color: 'primary.main',
              fontWeight: 600,
            },
            '&:hover': {
              backgroundColor: 'rgba(0, 0, 0, 0.03)',
            }
          },
          '& .MuiTabs-indicator': {
            height: 3,
            borderTopLeftRadius: 3,
            borderTopRightRadius: 3,
          }
        }}
      >
        <Tab label="Getting Started" value="getting-started" />
        <Tab label="Dashboard" value="dashboard" />
        <Tab label="Admissions" value="admissions" />
        <Tab label="Front of House" value="front-of-house" />
        <Tab label="Shift Management" value="shift-management" />
        <Tab label="Lost Property" value="lost-property" />
        <Tab label="Knowledge Base" value="knowledge-base" />
        <Tab label="Reports" value="reports" />
        <Tab label="Feedback" value="feedback" />
        <Tab label="Access Management" value="access-management" />
        <Tab label="Data Management" value="data-management" />
        <Tab label="Technical Support" value="technical-support" />
        <Tab label="FAQ" value="faq" />
      </Tabs>
    </Paper>
  );
};