import React from 'react';
import { Box, Typography } from '@mui/material';
import { styled } from '@mui/material/styles';

// Styled component for screenshots
const ScreenshotImage = styled('img')({
  width: '100%',
  maxWidth: '800px',
  borderRadius: '8px',
  boxShadow: '0 4px 12px rgba(0, 0, 0, 0.15)',
  marginTop: '16px',
  marginBottom: '24px',
  transition: 'transform 0.3s ease',
  '&:hover': {
    transform: 'scale(1.02)',
  },
});

interface UserGuideImageProps {
  imageName: string;
  altText?: string;
}

export const UserGuideImage: React.FC<UserGuideImageProps> = ({ imageName, altText }) => {
  return (
    <Box
      sx={{
        display: 'flex',
        justifyContent: 'center',
        mt: 3,
        mb: 2
      }}
    >
      <ScreenshotImage
        src={`/images/userguide/${imageName}.png`}
        alt={altText || `Screenshot for ${imageName}`}
        style={{ display: 'block', maxWidth: '100%' }}
      />
    </Box>
  );
};