import React, { useState } from 'react';
import { databaseService } from '../services/database/index';
import { Festival } from '../types/festival';
import { useFestival } from '../contexts/FestivalContext';
import { differenceInDays, parseISO, isWithinInterval } from 'date-fns';
import {
  Stack,
  Card,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  Typography,
  CircularProgress,
  Alert,
  Box,
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import WarningIcon from '@mui/icons-material/Warning';

export const FestivalSelector: React.FC = () => {
  const { festivals, activeFestival, setActiveFestival, loading, error: contextError } = useFestival();
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false);
  const [festivalToDelete, setFestivalToDelete] = useState<Festival | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleDeleteClick = (festival: Festival) => {
    setFestivalToDelete(festival);
    setIsDeleteModalOpen(true);
  };

  const handleDelete = async () => {
    if (!festivalToDelete) return;

    setError(null);
    try {
      await databaseService.deleteFestival(festivalToDelete._id);
      if (activeFestival?._id === festivalToDelete._id) {
        await setActiveFestival(null);
      }
      setIsDeleteModalOpen(false);
      setFestivalToDelete(null);
    } catch (err) {
      setError('Failed to delete festival');
      console.error(err);
    }
  };

  if (loading && festivals.length === 0) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return <Alert severity="error">{error}</Alert>;
  }

  return (
    <Stack spacing={1}>
      {festivals.map((festival) => (
        <Card
          key={festival._id}
          variant="outlined"
          sx={{
            p: 1.5,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'space-between',
            bgcolor: 'background.paper',
          }}
        >
          <Button
            onClick={() => {
              const selectedFestival = festivals.find(f => f._id === festival._id);
              if (selectedFestival) {
                setActiveFestival(selectedFestival);
              }
            }}
            sx={{
              justifyContent: 'flex-start',
              textAlign: 'left',
              p: 1,
              flex: 1,
              color: activeFestival?._id === festival._id ? 'ithink.pink' : 'text.primary',
              '&:hover': {
                bgcolor: 'action.hover',
              },
            }}
          >
            <Stack spacing={0.5}>
              <Typography
                variant="body1"
                sx={{
                  fontWeight: activeFestival?._id === festival._id ? 600 : 400,
                }}
              >
                {festival.name}
              </Typography>
              {isWithinInterval(new Date(), {
                start: parseISO(festival.startDate),
                end: parseISO(festival.endDate)
              }) && (
                <Typography variant="body2" color="text.secondary">
                  Day {differenceInDays(new Date(), parseISO(festival.startDate)) + 1} of{' '}
                  {differenceInDays(parseISO(festival.endDate), parseISO(festival.startDate)) + 1}
                </Typography>
              )}
            </Stack>
          </Button>
          <IconButton
            onClick={() => handleDeleteClick(festival)}
            size="small"
            color="default"
            sx={{
              '&:hover': {
                color: 'error.main',
              },
            }}
          >
            <DeleteIcon />
          </IconButton>
        </Card>
      ))}

      <Dialog
        open={isDeleteModalOpen}
        onClose={() => setIsDeleteModalOpen(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <WarningIcon color="error" />
          Delete Festival
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete {festivalToDelete?.name}? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button
            onClick={() => setIsDeleteModalOpen(false)}
            variant="outlined"
            color="inherit"
          >
            Cancel
          </Button>
          <Button
            onClick={handleDelete}
            variant="contained"
            color="error"
            disabled={loading}
          >
            Delete
          </Button>
        </DialogActions>
      </Dialog>
    </Stack>
  );
};
