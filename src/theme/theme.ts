import { createTheme, alpha } from '@mui/material/styles';

declare module '@mui/material/styles' {
  interface Palette {
    ithink: {
      purple: string;
      pink: string;
      purpleLight: string;
      pinkLight: string;
      gradientStart: string;
      gradientEnd: string;
    };
  }

  interface PaletteOptions {
    ithink: {
      purple: string;
      pink: string;
      purpleLight: string;
      pinkLight: string;
      gradientStart: string;
      gradientEnd: string;
    };
  }
}

const ithinkColors = {
  purple: '#662D91',
  pink: '#E0338C',
  purpleLight: '#8E44AD',
  pinkLight: '#E74C3C',
  gradientStart: '#662D91',
  gradientEnd: '#E0338C',
};

export const theme = createTheme({
  palette: {
    primary: {
      main: ithinkColors.purple,
      light: ithinkColors.purpleLight,
      contrastText: '#fff',
    },
    secondary: {
      main: ithinkColors.pink,
      light: ithinkColors.pinkLight,
      contrastText: '#fff',
    },
    ithink: ithinkColors,
    background: {
      default: '#f5f5f5',
      paper: '#ffffff',
    },
  },
  typography: {
    fontFamily: [
      '-apple-system',
      'BlinkMacSystemFont',
      '"Segoe UI"',
      'Roboto',
      '"Helvetica Neue"',
      'Arial',
      'sans-serif',
    ].join(','),
    h1: {
      fontSize: '2rem',
      fontWeight: 600,
      color: ithinkColors.purple,
    },
    h2: {
      fontSize: '1.75rem',
      fontWeight: 600,
      color: ithinkColors.purple,
    },
    h3: {
      fontSize: '1.5rem',
      fontWeight: 500,
      color: ithinkColors.purple,
    },
    h4: {
      fontSize: '1.25rem',
      fontWeight: 500,
      color: ithinkColors.purple,
    },
    h5: {
      fontSize: '1.125rem',
      fontWeight: 500,
      color: ithinkColors.purple,
    },
    h6: {
      fontSize: '1rem',
      fontWeight: 500,
      color: ithinkColors.purple,
    },
  },
  components: {
    MuiButton: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          borderRadius: '8px',
          fontWeight: 500,
        },
        contained: {
          boxShadow: 'none',
          '&:hover': {
            boxShadow: 'none',
          },
        },
      },
      defaultProps: {
        disableElevation: true,
      },
    },
    MuiPaper: {
      styleOverrides: {
        root: {
          borderRadius: '12px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
        },
      },
    },
    MuiCard: {
      styleOverrides: {
        root: {
          borderRadius: '12px',
          boxShadow: '0 2px 4px rgba(0,0,0,0.05)',
        },
      },
    },
    MuiTableCell: {
      styleOverrides: {
        root: {
          borderBottom: '1px solid rgba(224, 224, 224, 0.4)',
        },
        head: {
          fontWeight: 600,
          backgroundColor: alpha(ithinkColors.purple, 0.05),
        },
      },
    },
    MuiAppBar: {
      styleOverrides: {
        root: {
          background: `linear-gradient(45deg, ${ithinkColors.gradientStart} 30%, ${ithinkColors.gradientEnd} 90%)`,
          boxShadow: 'none',
        },
      },
    },
    MuiTabs: {
      styleOverrides: {
        indicator: {
          backgroundColor: ithinkColors.pink,
        },
      },
    },
    MuiTab: {
      styleOverrides: {
        root: {
          textTransform: 'none',
          '&.Mui-selected': {
            color: ithinkColors.pink,
          },
        },
      },
    },
    MuiTextField: {
      styleOverrides: {
        root: {
          '& .MuiOutlinedInput-root': {
            '&.Mui-focused fieldset': {
              borderColor: ithinkColors.purple,
            },
          },
        },
      },
    },
    MuiCheckbox: {
      styleOverrides: {
        root: {
          color: ithinkColors.purple,
          '&.Mui-checked': {
            color: ithinkColors.purple,
          },
        },
      },
    },
    MuiRadio: {
      styleOverrides: {
        root: {
          color: ithinkColors.purple,
          '&.Mui-checked': {
            color: ithinkColors.purple,
          },
        },
      },
    },
  },
});

// Helper function to create gradient text
export const gradientText = {
  background: `-webkit-linear-gradient(45deg, ${ithinkColors.gradientStart} 30%, ${ithinkColors.gradientEnd} 90%)`,
  WebkitBackgroundClip: 'text',
  WebkitTextFillColor: 'transparent',
  backgroundClip: 'text',
  textFillColor: 'transparent',
};