// Import the user guide content directly
import userGuideContent from '../assets/user-guide.md';

// Parse the user guide content into sections
export const parseUserGuide = () => {
  const content = userGuideContent;
  
  // Define all expected sections
  const parsedSections: Record<string, string> = {
    'getting-started': '',
    'dashboard': '',
    'admissions': '',
    'front-of-house': '',
    'shift-management': '',
    'lost-property': '',
    'knowledge-base': '',
    'reports': '',
    'feedback': '',
    'access-management': '',
    'data-management': '',
    'technical-support': '',
    'faq': ''
  };
  
  // Map section titles to keys
  const sectionMap: Record<string, string> = {
    'Getting Started': 'getting-started',
    'Dashboard': 'dashboard',
    'Admissions': 'admissions',
    'Front of House': 'front-of-house',
    'Shift Management': 'shift-management',
    'Lost Property': 'lost-property',
    'Knowledge Base': 'knowledge-base',
    'Reports': 'reports',
    'Feedback': 'feedback',
    'Access Management': 'access-management',
    'Data Management': 'data-management',
    'Technical Support': 'technical-support',
    'Frequently Asked Questions (FAQ)': 'faq'
  };
  
  // First, extract the intro (everything before the first ## heading)
  const introMatch = content.match(/^# [^\n]+\n([\s\S]*?)(?=\n## |$)/);
  const intro = introMatch ? introMatch[1].trim() : '';
  
  // Extract all main sections using regex
  const sectionRegex = /## ([^\n]+)([\s\S]*?)(?=\n## |$)/g;
  let match;
  
  while ((match = sectionRegex.exec(content)) !== null) {
    const title = match[1].trim();
    const sectionContent = match[2].trim();
    
    // Map the title to a key
    const key = sectionMap[title] || title.toLowerCase().replace(/\s+/g, '-');
    
    // If this is a recognized section, add it
    if (parsedSections.hasOwnProperty(key)) {
      parsedSections[key] = sectionContent;
    }
  }
  
  // Handle technical support section (it's at the end after ---)
  const techSupportMatch = content.match(/---\s*\n\s*\*\*Technical Support\*\*([\s\S]+)$/);
  if (techSupportMatch) {
    parsedSections['technical-support'] = techSupportMatch[1].trim();
  }
  
  return {
    intro,
    sections: parsedSections
  };
};

export const USER_GUIDE = parseUserGuide();