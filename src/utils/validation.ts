import { WelfareAdmission } from '../types/index';

// Validation error type
export interface ValidationError {
  field: string;
  message: string;
}

// Validate date range
const isDateInRange = (date: string, earliest?: string, latest?: string): boolean => {
  const dateValue = new Date(date);
  if (earliest && dateValue < new Date(earliest)) return false;
  if (latest && dateValue > new Date(latest)) return false;
  return true;
};

// Validate string length
const isValidLength = (value: string, maxLength?: number): boolean => {
  if (!maxLength) return true;
  return value.length <= maxLength;
};

// Validate number range
const isValidNumber = (value: number, min?: number, max?: number): boolean => {
  if (min !== undefined && value < min) return false;
  if (max !== undefined && value > max) return false;
  return true;
};

// Validate datetime format
const isValidDateTime = (value: string): boolean => {
  const date = new Date(value);
  return !isNaN(date.getTime());
};

export const validateWelfareAdmission = (admission: Partial<WelfareAdmission>): ValidationError[] => {
  const errors: ValidationError[] = [];

  // Required fields
  if (!admission.Attended) {
    errors.push({ field: 'Attended', message: 'Attended date is required' });
  } else if (!isValidDateTime(admission.Attended)) {
    errors.push({ field: 'Attended', message: 'Invalid datetime format for Attended' });
  }

  if (!admission.FirstName) {
    errors.push({ field: 'FirstName', message: 'First name is required' });
  }
  if (!admission.BaysOrChairs) {
    errors.push({ field: 'BaysOrChairs', message: 'Bays or chairs selection is required' });
  }
  if (!admission.ReferredBy) {
    errors.push({ field: 'ReferredBy', message: 'Referral source is required' });
  }
  if (!admission.ReasonCategory) {
    errors.push({ field: 'ReasonCategory', message: 'Reason category is required' });
  }
  if (admission.InBayNow === undefined) {
    errors.push({ field: 'InBayNow', message: 'In bay now status is required' });
  }
  if (!admission.AdmissionNotes) {
    errors.push({ field: 'AdmissionNotes', message: 'Admission notes are required' });
  }

  // String length validations
  if (admission.FirstName && !isValidLength(admission.FirstName, 255)) {
    errors.push({ field: 'FirstName', message: 'First name must not exceed 255 characters' });
  }
  if (admission.Surname && !isValidLength(admission.Surname, 255)) {
    errors.push({ field: 'Surname', message: 'Surname must not exceed 255 characters' });
  }

  // Number range validations
  if (admission.Age !== undefined && !isValidNumber(admission.Age, 1, 110)) {
    errors.push({ field: 'Age', message: 'Age must be between 1 and 110' });
  }
  if (admission.Location !== undefined && !isValidNumber(admission.Location, 0, 150)) {
    errors.push({ field: 'Location', message: 'Location must be between 0 and 150' });
  }

  // Date validations
  if (admission.DOB) {
    if (!isValidDateTime(admission.DOB)) {
      errors.push({ field: 'DOB', message: 'Invalid datetime format for DOB' });
    } else if (!isDateInRange(admission.DOB, '1920-01-01', '2024-06-23')) {
      errors.push({ field: 'DOB', message: 'Date of birth must be between 1920-01-01 and 2024-06-23' });
    }
  }

  if (admission.DischargeTime && !isValidDateTime(admission.DischargeTime)) {
    errors.push({ field: 'DischargeTime', message: 'Invalid datetime format for DischargeTime' });
  }

  return errors;
};

// Calculate duration in milliseconds
export const calculateDuration = (attended: string, discharged?: string): number | undefined => {
  if (!discharged) return undefined;
  return new Date(discharged).getTime() - new Date(attended).getTime();
};

// Calculate duration in hours (precise)
export const calculateDurationPrecise = (attended: string, discharged?: string): number | undefined => {
  const duration = calculateDuration(attended, discharged);
  if (!duration) return undefined;
  return Math.floor(duration / (1000 * 60 * 60));
};
