# Database Proxy Worker

This Cloudflare Worker acts as a proxy between the frontend application and the CouchDB database, handling CORS and authentication.

## Setup

1. Install dependencies:
```bash
npm install
```

2. Configure environment variables in the Cloudflare Dashboard:
   - `ALLOWED_ORIGINS`: Comma-separated list of allowed origins
   - `COUCHDB_URL`: URL of your CouchDB instance

3. Deploy the worker:
```bash
npm run deploy
```

## Usage

Once deployed, update the `remoteUrl` in your database configuration (`src/services/database/config.ts`) to point to your worker URL:

```typescript
export const DB_CONFIG: DatabaseConfig = {
  localName: 'ithinc_welfare',
  remoteUrl: 'https://database-proxy.[your-worker-subdomain].workers.dev',
  // ... other config
};
```

## Development

To run the worker locally:
```bash
npm run dev
```

## Security

The worker:
- Only allows requests from specified origins
- Forwards authentication headers to CouchDB
- Handles CORS headers automatically

Make sure to:
1. Set appropriate ALLOWED_ORIGINS in your worker configuration
2. Keep your CouchDB credentials secure
3. Use HTTPS for all communications