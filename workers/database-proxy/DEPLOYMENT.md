# Database Proxy Worker Deployment Guide

## Setting up Secrets

The worker requires the following secrets to be set up:

1. `ALLOWED_ORIGINS`: Comma-separated list of allowed origins
2. `COUCHDB_URL`: The URL of your CouchDB instance
3. `CF_ACCESS_CLIENT_ID`: Cloudflare Access client ID for WARP
4. `CF_ACCESS_CLIENT_SECRET`: Cloudflare Access client secret for WARP

### Setting up secrets locally for development

```bash
# Set ALLOWED_ORIGINS
wrangler secret put ALLOWED_ORIGINS
# When prompted, enter: http://localhost:3000,https://welfare.brisflix.com,https://ithink.brisflix.com,https://www.ithink.brisflix.com

# Set COUCHDB_URL
wrangler secret put COUCHDB_URL
# When prompted, enter: https://welfaredb.brisflix.com

# Set Cloudflare Access credentials
wrangler secret put CF_ACCESS_CLIENT_ID
# When prompted, enter: 4407321a127017362955b7aec7346dff.access

wrangler secret put CF_ACCESS_CLIENT_SECRET
# When prompted, enter: 3c835971357d3c402bd12c0032989fc5d8ac17854164ff77ffae1c20da9a6756
```

### Setting up secrets for different environments

For development:
```bash
wrangler secret put ALLOWED_ORIGINS --env development
# Enter: https://welfare.brisflix.com,http://localhost:3000,http://beast:3000

wrangler secret put COUCHDB_URL --env development
# Enter: https://welfaredb.brisflix.com

wrangler secret put CF_ACCESS_CLIENT_ID --env development
# Enter: 4407321a127017362955b7aec7346dff.access

wrangler secret put CF_ACCESS_CLIENT_SECRET --env development
# Enter: 3c835971357d3c402bd12c0032989fc5d8ac17854164ff77ffae1c20da9a6756
```

For production:
```bash
wrangler secret put ALLOWED_ORIGINS --env production
# Enter: https://welfare.brisflix.com,https://ithink.brisflix.com,https://www.ithink.brisflix.com

wrangler secret put COUCHDB_URL --env production
# Enter: https://welfaredb.brisflix.com

wrangler secret put CF_ACCESS_CLIENT_ID --env production
# Enter: 4407321a127017362955b7aec7346dff.access

wrangler secret put CF_ACCESS_CLIENT_SECRET --env production
# Enter: 3c835971357d3c402bd12c0032989fc5d8ac17854164ff77ffae1c20da9a6756
```

## Deployment

To deploy to development:
```bash
wrangler deploy --env development
```

To deploy to production:
```bash
wrangler deploy --env production
```

## Verifying Deployment

After deployment, you can verify the worker is functioning correctly by:

1. Making a request to the worker's URL
2. Checking that CORS headers are being set correctly
3. Verifying that requests are being properly proxied to CouchDB
4. Confirming that Cloudflare Access authentication is working

## Troubleshooting

If you encounter any issues:

1. Check that all secrets are properly set:
   ```bash
   wrangler secret list
   ```

2. Verify the worker's logs in the Cloudflare dashboard

3. Common issues:
   - 403 errors: Check that the origin making the request is in the ALLOWED_ORIGINS list
   - 500 errors: Check that COUCHDB_URL is correct and accessible
   - CORS errors: Verify that the origin is properly configured in ALLOWED_ORIGINS
   - Authentication errors: Verify CF_ACCESS_CLIENT_ID and CF_ACCESS_CLIENT_SECRET are correct