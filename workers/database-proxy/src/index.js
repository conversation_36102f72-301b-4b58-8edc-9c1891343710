const corsHeaders = {
  'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
  'Access-Control-Allow-Headers': 'Content-Type, Authorization, CF-Access-Client-Id, CF-Access-Client-Secret, Accept, If-Match, If-None-Match, Origin, Accept-Encoding',
  'Access-Control-Allow-Credentials': 'true',
  'Access-Control-Max-Age': '86400',
  'Access-Control-Expose-Headers': 'ETag, Content-Type',
};

function parseAllowedOrigins(originsString) {
  try {
    return originsString
      .replace(/\s+/g, '')
      .split(',')
      .filter(origin => origin.length > 0);
  } catch (error) {
    console.error('Error parsing allowed origins:', error);
    return ['http://localhost:3000'];
  }
}

function getAllowedOrigin(request, allowedOrigins) {
  const origin = request.headers.get('Origin');
  if (!origin) {
    console.log('No Origin header found in request');
    return null;
  }

  try {
    const parsedAllowedOrigins = parseAllowedOrigins(allowedOrigins);
    console.log('Parsed allowed origins:', parsedAllowedOrigins);
    console.log('Request origin:', origin);
    
    if (parsedAllowedOrigins.includes(origin)) {
      return origin;
    }
    
    if (origin.startsWith('http://localhost:')) {
      return origin;
    }
    
    console.log('Origin not allowed:', origin);
    return null;
  } catch (error) {
    console.error('Error checking origin:', error);
    return null;
  }
}

async function handleOptions(request, allowedOrigins) {
  const origin = getAllowedOrigin(request, allowedOrigins);
  if (!origin) {
    return new Response(null, { 
      status: 204,
      headers: {
        ...corsHeaders,
        'Access-Control-Allow-Origin': '*'
      }
    });
  }

  const accessControlRequestHeaders = request.headers.get('Access-Control-Request-Headers');

  return new Response(null, {
    status: 204,
    headers: {
      ...corsHeaders,
      'Access-Control-Allow-Origin': origin,
      ...(accessControlRequestHeaders && {
        'Access-Control-Allow-Headers': accessControlRequestHeaders,
      }),
    },
  });
}

// Special handling for _local and _changes endpoints
async function handleSpecialEndpoint(url, fetchOptions, origin) {
  const maxRetries = 5;
  let retryCount = 0;
  let lastError;

  while (retryCount < maxRetries) {
    try {
      // Increase timeout for these endpoints
      fetchOptions.timeout = 60000; // 60 seconds

      const response = await fetch(url, fetchOptions);
      const responseText = await response.text();

      try {
        const responseBody = JSON.parse(responseText);
        return new Response(JSON.stringify(responseBody), {
          status: response.status,
          headers: {
            'Content-Type': 'application/json',
            ...corsHeaders,
            'Access-Control-Allow-Origin': origin
          }
        });
      } catch (e) {
        return new Response(responseText, {
          status: response.status,
          headers: {
            'Content-Type': 'text/plain',
            ...corsHeaders,
            'Access-Control-Allow-Origin': origin
          }
        });
      }
    } catch (error) {
      lastError = error;
      if (error.message.includes('502')) {
        retryCount++;
        if (retryCount < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, Math.pow(2, retryCount) * 2000));
          continue;
        }
      }
      throw error;
    }
  }

  throw lastError;
}

async function proxyRequest(request, allowedOrigins, couchdbUrl, cfAccessClientId, cfAccessClientSecret) {
  const origin = getAllowedOrigin(request, allowedOrigins);
  if (!origin) {
    return new Response(null, { 
      status: 403,
      headers: {
        ...corsHeaders,
        'Access-Control-Allow-Origin': '*'
      }
    });
  }

  const url = new URL(request.url);
  const targetUrl = couchdbUrl + url.pathname + url.search;

  try {
    const headers = new Headers(request.headers);
    
    // Always use the configured Cloudflare Access credentials
    headers.set('CF-Access-Client-Id', cfAccessClientId);
    headers.set('CF-Access-Client-Secret', cfAccessClientSecret);
    
    // Extract basic auth from request if present, or use configured values
    const authHeader = request.headers.get('Authorization');
    if (!authHeader && env.COUCHDB_USERNAME && env.COUCHDB_PASSWORD) {
      const basicAuth = 'Basic ' + btoa(`${env.COUCHDB_USERNAME}:${env.COUCHDB_PASSWORD}`);
      headers.set('Authorization', basicAuth);
    }
    
    const fetchOptions = {
      method: request.method,
      headers: headers,
    };

    if (request.method !== 'GET' && request.method !== 'HEAD' && request.method !== 'OPTIONS') {
      const bodyText = await request.text();
      fetchOptions.body = bodyText;
    }

    // Special handling for _local and _changes endpoints
    if (url.pathname.includes('/_local/') || url.pathname.includes('/_changes')) {
      return handleSpecialEndpoint(targetUrl, fetchOptions, origin);
    }

    // Regular request handling
    const response = await fetch(targetUrl, fetchOptions);
    const responseText = await response.text();

    try {
      const responseBody = JSON.parse(responseText);
      return new Response(JSON.stringify(responseBody), {
        status: response.status,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
          'Access-Control-Allow-Origin': origin
        }
      });
    } catch (e) {
      return new Response(responseText, {
        status: response.status,
        headers: {
          'Content-Type': response.headers.get('Content-Type') || 'text/plain',
          ...corsHeaders,
          'Access-Control-Allow-Origin': origin
        }
      });
    }
  } catch (error) {
    console.error('Proxy request failed:', {
      error: error.message,
      stack: error.stack,
      url: targetUrl
    });
    
    return new Response(JSON.stringify({
      error: 'Proxy request failed',
      message: error.message,
      url: targetUrl
    }), {
      status: 502,
      headers: {
        'Content-Type': 'application/json',
        ...corsHeaders,
        'Access-Control-Allow-Origin': origin
      }
    });
  }
}

export default {
  async fetch(request, env, ctx) {
    try {
      // Check for required secrets
      if (!env.ALLOWED_ORIGINS || !env.COUCHDB_URL || !env.CF_ACCESS_CLIENT_ID || !env.CF_ACCESS_CLIENT_SECRET) {
        throw new Error('Required secrets ALLOWED_ORIGINS, COUCHDB_URL, CF_ACCESS_CLIENT_ID, and CF_ACCESS_CLIENT_SECRET must be set');
      }

      if (request.method === 'OPTIONS') {
        return handleOptions(request, env.ALLOWED_ORIGINS);
      }

      return proxyRequest(
        request, 
        env.ALLOWED_ORIGINS, 
        env.COUCHDB_URL,
        env.CF_ACCESS_CLIENT_ID,
        env.CF_ACCESS_CLIENT_SECRET
      );
    } catch (error) {
      console.error('Worker error:', {
        message: error.message,
        stack: error.stack
      });
      
      const origin = getAllowedOrigin(request, env.ALLOWED_ORIGINS || '');
      if (!origin) {
        return new Response(null, { 
          status: 403,
          headers: {
            ...corsHeaders,
            'Access-Control-Allow-Origin': '*'
          }
        });
      }

      return new Response(JSON.stringify({
        error: 'Worker error',
        message: error.message
      }), {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          ...corsHeaders,
          'Access-Control-Allow-Origin': origin
        }
      });
    }
  },
};
