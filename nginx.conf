events {
    worker_connections 1024;
}

http {
    include /etc/nginx/mime.types;
    default_type application/octet-stream;

    # Logging configuration
    log_format detailed_log '[$time_local] $remote_addr "$request" '
                          '$status $body_bytes_sent "$http_referer" '
                          '"$http_user_agent" "$http_x_forwarded_for" '
                          'Request Headers: $http_cf_access_client_id '
                          'Response: $upstream_http_content_type '
                          'Upstream: $upstream_addr $upstream_status';

    # Cloudflare real IP configuration
    set_real_ip_from ************/22;
    set_real_ip_from ************/22;
    set_real_ip_from **********/22;
    set_real_ip_from **********/13;
    set_real_ip_from **********/14;
    set_real_ip_from *************/18;
    set_real_ip_from **********/22;
    set_real_ip_from ************/18;
    set_real_ip_from ***********/15;
    set_real_ip_from **********/13;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from ************/20;
    set_real_ip_from *************/22;
    set_real_ip_from ************/17;
    real_ip_header CF-Connecting-IP;

    server {
        listen 80;
        server_name welfare.brisflix.com;

        root /usr/share/nginx/html;
        index index.html;

        # Security headers
        add_header X-Content-Type-Options "nosniff" always;
        add_header X-Frame-Options "DENY" always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

        # Main application
        location / {
            try_files $uri $uri/ /index.html;
            add_header Cache-Control "no-store, no-cache, must-revalidate";
        }

        # Handle manifest.json - serve directly without CORS headers
        location = /manifest.json {
            try_files $uri =404;
            add_header Content-Type application/json;
            add_header Cache-Control "no-store, no-cache, must-revalidate";
        }

        # Static files
        location /static/ {
            expires 1y;
            add_header Cache-Control "public, immutable";
            try_files $uri =404;
        }

        # SVG files
        location ~* \.svg$ {
            add_header Content-Type image/svg+xml;
            add_header Cache-Control "public, max-age=31536000";
            try_files $uri =404;
        }

        # Error handling
        error_page 404 /index.html;
        error_page 502 /index.html;

        # Large client header buffers for Cloudflare
        large_client_header_buffers 4 32k;
    }

    error_log /var/log/nginx/error.log debug;
    access_log /var/log/nginx/access.log detailed_log;
}
